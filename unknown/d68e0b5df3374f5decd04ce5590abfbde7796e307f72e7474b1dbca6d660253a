
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Menu, X, Bell, Settings, Moon, Sun } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTheme } from "next-themes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/store";
import { ClinicSwitcher } from "@/components/clinic/ClinicSwitcher";

interface NavbarProps {
  onMenuClick: () => void;
}

export const Navbar = ({ onMenuClick }: NavbarProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { logout, user, staff, clinic: currentClinic } = useAuth();
  const { theme, setTheme } = useTheme();

  const handleMenuClick = () => {
    setIsOpen(!isOpen);
    onMenuClick();
  };

  return (
    <nav className="bg-background/80 backdrop-blur-md border-b px-4 py-3 fixed w-full top-0 z-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleMenuClick}
            className="p-2 hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors"
          >
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
          <Link to="/" className="flex items-center space-x-2">
            <span className="text-xl font-semibold text-primary">VetCare</span>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          {/* Clinic Switcher */}
          <ClinicSwitcher />

          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            {theme === "dark" ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </Button>
          <button className="p-2 hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors relative">
            <Bell size={20} />
            <span className="absolute top-1 right-1 w-2 h-2 bg-accent rounded-full"></span>
          </button>
          <button className="p-2 hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors">
            <Settings size={20} />
          </button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center space-x-2 hover:bg-accent hover:text-accent-foreground rounded-lg p-1 transition-colors">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={staff?.imageUrl || "https://github.com/shadcn.png"} />
                  <AvatarFallback>
                    {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                  {currentClinic && (
                    <p className="text-xs leading-none text-muted-foreground mt-1">
                      {currentClinic.clinicName}
                    </p>
                  )}
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link to="/profile" className="w-full">View Profile</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/settings/user" className="w-full">Settings</Link>
              </DropdownMenuItem>
              {staff?.isClinicOwner && (
                <DropdownMenuItem>
                  <Link to="/admin/clinics" className="w-full">Manage Clinics</Link>
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => logout()} className="text-red-600 cursor-pointer">
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </nav>
  );
};
