import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createAppointment,
    getAllAppointments,
    getAppointmentById,
    updateAppointment,
    deleteAppointment,
    getAppointmentTypes,
    completeAppointment,
    reassignAppointment,
    updateAppointmentProgress,
} from "../controllers/appointment.controller.js";

const appointmentRouter = Router();

// Apply authentication to all routes
appointmentRouter.use(verifyToken);

appointmentRouter.get('/types', getAppointmentTypes);
appointmentRouter.post('/', createAppointment);
appointmentRouter.get('/', getAllAppointments);

// Progress routes
appointmentRouter.put('/:appointmentId/progress', updateAppointmentProgress);

appointmentRouter.post('/:appointmentId/complete', completeAppointment);
appointmentRouter.put('/:appointmentId/reassign', reassignAppointment);
appointmentRouter.get('/:appointmentId', getAppointmentById);
appointmentRouter.put('/:appointmentId', updateAppointment);
appointmentRouter.delete('/:appointmentId', deleteAppointment);

export default appointmentRouter;