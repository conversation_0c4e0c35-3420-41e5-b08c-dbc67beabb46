import express from 'express';
import {
    getAllServices,
    getServicesByCategory,
    getServiceById,
    createService,
    updateService,
    deleteService,
    searchServices,
    getServiceCategories
} from '../controllers/service.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get service categories with counts
router.get('/categories', getServiceCategories);

// Search services
router.get('/search', searchServices);

// Get services by category
router.get('/category/:category', getServicesByCategory);

// Get all services with filtering
router.get('/', getAllServices);

// Get single service by ID
router.get('/:serviceId', getServiceById);

// Create new service
router.post('/', createService);

// Update service
router.put('/:serviceId', updateService);

// Delete service (soft delete)
router.delete('/:serviceId', deleteService);

export default router;
