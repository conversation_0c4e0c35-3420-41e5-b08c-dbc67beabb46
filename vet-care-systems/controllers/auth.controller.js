import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import Staff from "../models/staff.model.js";
import User from "../models/user.model.js";
import jwt from "jsonwebtoken";
import { JWT_EXPIRES_IN, JWT_SECRET } from "../config/env.js";
import { sendResponse } from '../utils/responseHandler.js';
import Clinic from "../models/clinic.model.js";
import Role from "../models/role.model.js";

export const signUp = async (req, res) => {
    const {
        firstName,
        middleName,
        lastName,
        email,
        phoneNumber,
        password,
        address,
        dob,
        is_clinic_owner = false,
        clinic_data,
        roleId,
        jobTitle,
        employmentDate,
        salary,
        emergencyContact,
        schedule
    } = req.body;

    // Validate role
    const role = await Role.findOne({ roleId });
    if (!role) {
        return sendResponse(res, 404, false, "Invalid role specified");
    }

    // Check if staff already exists
    const existingStaff = await Staff.findOne({ email });
    if (existingStaff) {
        return sendResponse(res, 409, false, "Staff with this email already exists");
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    let session = null;
    try {
        session = await mongoose.startSession();
        session.startTransaction();

        let clinicId = null;

        // Create clinic if user is a clinic owner
        if (is_clinic_owner && clinic_data) {
            const [newClinic] = await Clinic.create([{
                clinicName: clinic_data.name,
                phoneNumber: clinic_data.phoneNumber || phoneNumber,
                email: clinic_data.email || email,
                address: clinic_data.address || address,
                status: 1
            }], { session });

            // Use the numeric clinicId instead of MongoDB's _id
            clinicId = newClinic.clinicId;
        }

        // Create staff record
        const staffData = {
            firstName,
            middleName,
            lastName,
            email,
            password: hashedPassword,
            phoneNumber,
            address,
            dob,
            // Ensure all clinic IDs are numeric
            clinicId: clinicId ? parseInt(clinicId) : null,
            primaryClinicId: clinicId ? parseInt(clinicId) : null,
            isClinicOwner: is_clinic_owner,
            roleId: parseInt(roleId),
            permissions: role.permissions,
            jobTitle: jobTitle || (is_clinic_owner ? 'Clinic Owner' : 'Staff Member'),
            employmentDate: employmentDate || new Date(),
            salary: salary || 0,
            emergencyContact: emergencyContact || {
                name: '',
                relationship: '',
                phoneNumber: ''
            },
            schedule: schedule || {
                workDays: [],
                workHours: { start: '', end: '' }
            },
            status: 1,
            lastLogin: null,
            loginCount: 0,
            clinicActivity: clinicId ? [{
                clinicId: parseInt(clinicId),
                lastActive: new Date(),
                activityCount: 1
            }] : [],
            currentClinicId: clinicId ? parseInt(clinicId) : null
        };

        const [newStaff] = await Staff.create([staffData], { session });

        // Generate JWT - simplified for now
        const token = jwt.sign(
            {
                userId: newStaff._id,
                staffId: newStaff._id
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // Prepare response data
        const staffWithoutPassword = { ...newStaff._doc };
        delete staffWithoutPassword.password;

        const responseData = {
            token,
            staff: staffWithoutPassword,
            role
        };

        // Add clinic data if applicable
        if (clinicId) {
            // Use clinicId (Number) instead of _id (ObjectId)
            const clinic = await Clinic.findOne({ clinicId: parseInt(clinicId) })
                .select('clinicId clinicName address phoneNumber email status')
                .session(session)
                .lean();

            if (clinic) {
                responseData.clinic = clinic;
                responseData.clinics = [clinic];
            }
        }

        await session.commitTransaction();
        return sendResponse(res, 201, true, "Staff created successfully", responseData);
    } catch (error) {
        if (session?.inTransaction()) {
            await session.abortTransaction();
        }
        console.error("Signup error:", error);
        return sendResponse(res, 500, false, error.message);
    } finally {
        if (session) {
            session.endSession();
        }
    }
};

export const signIn = async (req, res) => {
    try {
        const { email, password } = req.body;
        const userAgent = req.headers['user-agent'];
        const ipAddress = req.ip || req.connection.remoteAddress;

        // Special case for the default admin email
        const isAdminLogin = email === '<EMAIL>';

        // First check if this is a staff login
        let staffMember = null;
        let user = null;
        let isStaffLogin = false;

        if (!isAdminLogin) {
            // Try to find the staff member by email first
            staffMember = await Staff.findOne({ email })
                .select('staffId firstName middleName lastName email phoneNumber password status lastLogin loginCount roleId clinicId primaryClinicId currentClinicId additionalClinics isClinicOwner')
                .lean();

            if (staffMember) {
                isStaffLogin = true;
            }
        }

        // If not found in staff or it's admin login, check the User table
        if (!staffMember && !isStaffLogin) {
            user = await User.findOne({ email })
                .select('userId firstName middleName lastName email phoneNumber password status lastLogin loginCount roleId')
                .lean();

            if (!user && !isAdminLogin) {
                // Log failed login attempt for security monitoring
                console.log(`Failed login attempt for email: ${email} from IP: ${ipAddress}`);
                return sendResponse(res, 404, false, "User not found");
            }
        }

        // Handle password validation and login tracking
        let isPasswordValid = false;
        let userWithoutPassword = null;
        let responseData = {
            token: null,
            user: null,
            staff: null,
            clinics: []
        };

        // Staff login flow
        if (isStaffLogin && staffMember) {
            isPasswordValid = await bcrypt.compare(password, staffMember.password);

            if (!isPasswordValid) {
                // Log failed login attempt
                await Staff.updateOne(
                    { _id: staffMember._id },
                    {
                        $push: {
                            loginHistory: {
                                timestamp: new Date(),
                                ipAddress,
                                userAgent,
                                status: 'failed'
                            }
                        }
                    }
                );
                return sendResponse(res, 401, false, "Invalid credentials");
            }

            // Update login tracking for staff
            await Staff.updateOne(
                { _id: staffMember._id },
                {
                    $set: { lastLogin: new Date() },
                    $inc: { loginCount: 1 },
                    $push: {
                        loginHistory: {
                            timestamp: new Date(),
                            ipAddress,
                            userAgent,
                            status: 'success'
                        }
                    }
                }
            );

            // Remove password from response
            userWithoutPassword = { ...staffMember };
            delete userWithoutPassword.password;

            // Create token for staff - simplified for now
            const token = jwt.sign(
                {
                    userId: staffMember._id,
                    staffId: staffMember._id
                },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );

            responseData.token = token;
            responseData.staff = userWithoutPassword;
            responseData.user = {
                firstName: staffMember.firstName,
                middleName: staffMember.middleName,
                lastName: staffMember.lastName,
                email: staffMember.email,
                phoneNumber: staffMember.phoneNumber,
                userType: 'staff'
            };

            // Update the staff's clinic activity for their primary clinic
            if (staffMember.primaryClinicId) {
                await Staff.findByIdAndUpdate(
                    staffMember._id,
                    {
                        $push: {
                            clinicActivity: {
                                clinicId: parseInt(staffMember.primaryClinicId),
                                lastActive: new Date(),
                                activityCount: 1
                            }
                        }
                    }
                );

                // Get clinic details - use clinicId (Number) instead of _id (ObjectId)
                const primaryClinic = await Clinic.findOne({ clinicId: parseInt(staffMember.primaryClinicId) })
                    .select('clinicId clinicName address phoneNumber email status')
                    .lean();

                // Get additional clinics - use clinicId (Number) instead of _id (ObjectId)
                let additionalClinics = [];
                if (staffMember.additionalClinics && staffMember.additionalClinics.length > 0) {
                    // Convert all IDs to numbers if they aren't already
                    const clinicIds = staffMember.additionalClinics.map(id =>
                        typeof id === 'number' ? id : parseInt(id)
                    );

                    additionalClinics = await Clinic.find({
                        clinicId: { $in: clinicIds },
                        status: 1
                    })
                    .select('clinicId clinicName address phoneNumber email status')
                    .lean();
                }

                // Combine clinics
                responseData.clinics = [primaryClinic, ...additionalClinics].filter(Boolean);

                // Set current clinic to primary clinic
                await Staff.findByIdAndUpdate(
                    staffMember._id,
                    { currentClinicId: parseInt(staffMember.primaryClinicId) }
                );
            }
        }
        // System user login flow (including admin)
        else if (user || isAdminLogin) {
            // For admin login, get the default admin user if not already found
            if (isAdminLogin && !user) {
                user = await User.findOne({ email: '<EMAIL>' })
                    .select('userId firstName middleName lastName email phoneNumber password status lastLogin loginCount roleId')
                    .lean();

                if (!user) {
                    return sendResponse(res, 404, false, "Admin user not found");
                }
            }

            isPasswordValid = await bcrypt.compare(password, user.password);

            if (!isPasswordValid) {
                // Log failed login attempt
                await User.updateOne(
                    { _id: user._id },
                    {
                        $push: {
                            loginHistory: {
                                timestamp: new Date(),
                                ipAddress,
                                userAgent,
                                status: 'failed'
                            }
                        }
                    }
                );
                return sendResponse(res, 401, false, "Invalid credentials");
            }

            // Update login tracking
            await User.updateOne(
                { _id: user._id },
                {
                    $set: { lastLogin: new Date() },
                    $inc: { loginCount: 1 },
                    $push: {
                        loginHistory: {
                            timestamp: new Date(),
                            ipAddress,
                            userAgent,
                            status: 'success'
                        }
                    }
                }
            );

            // Remove password from response
            userWithoutPassword = { ...user };
            delete userWithoutPassword.password;

            responseData.user = userWithoutPassword;

            // Create token for system user - simplified for now
            const token = jwt.sign(
                {
                    userId: user._id
                },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );

            responseData.token = token;

            // For admin, get all active clinics
            if (isAdminLogin) {
                const clinics = await Clinic.find({ status: 1 })
                    .select('clinicId clinicName address phoneNumber email status')
                    .lean();

                responseData.clinics = clinics;

                // Set the current clinic to the first available clinic if any
                if (clinics.length > 0) {
                    await User.findByIdAndUpdate(
                        user._id,
                        { currentClinicId: clinics[0].clinicId } // Use numeric clinicId
                    );
                }
            }
        }

        return sendResponse(res, 200, true, "Login successful", responseData);
    } catch (error) {
        console.error("Login error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
