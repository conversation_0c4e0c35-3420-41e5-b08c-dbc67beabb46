# Clinic Management System Documentation

## Overview

The Clinic Management System is designed to support multi-clinic operations in the Lovable VetCare SaaS platform. It allows clinic owners to manage multiple clinics, staff members to work at different clinics, and provides a seamless way to switch between clinics.

## Key Features

1. **Multi-Clinic Support**: Users can be associated with multiple clinics
2. **Role-Based Access Control**: Different permissions for clinic owners vs. staff
3. **Clinic Switching**: Users can switch between clinics they have access to
4. **Login Tracking**: System tracks user login activity and clinic usage
5. **Clinic-Specific Data**: Medical records and other data are associated with specific clinics

## Database Schema

### User Model

The User model has been enhanced to support multi-clinic operations:

```javascript
const userSchema = new mongoose.Schema({
    // Basic user information
    firstName: String,
    middleName: String,
    lastName: String,
    email: String,
    phoneNumber: String,
    // ...other fields
    
    // User type and status
    userStatus: Number, // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
    userType: {
        type: String,
        enum: ['client', 'staff', 'admin'],
        required: true,
        default: 'client'
    },
    
    // Login tracking
    lastLogin: Date,
    loginCount: Number,
    loginHistory: [{
        timestamp: Date,
        ipAddress: String,
        userAgent: String,
        status: String // 'success' or 'failed'
    }],
    
    // Current clinic
    currentClinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic',
        default: null
    }
});
```

### Clinic Model

The Clinic model includes additional fields for better clinic management:

```javascript
const clinicSchema = new mongoose.Schema({
    clinicName: String,
    ownerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    phoneNumber: String,
    email: String,
    address: String,
    
    // Additional fields
    status: Number, // 0: Inactive, 1: Active
    location: {
        type: {
            type: String,
            enum: ['Point'],
            default: 'Point'
        },
        coordinates: [Number] // [longitude, latitude]
    },
    operatingHours: {
        monday: { open: String, close: String },
        // ...other days
    },
    description: String,
    website: String,
    logo: String
});
```

### Staff Model

The Staff model has been enhanced to support working at multiple clinics:

```javascript
const staffSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    // Primary clinic
    clinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic'
    },
    primaryClinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic'
    },
    // Additional clinics
    additionalClinics: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic'
    }],
    // ...other fields
    
    // Clinic activity tracking
    clinicActivity: [{
        clinicId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Clinic'
        },
        lastActive: Date,
        activityCount: Number
    }]
});
```

### Health Record Model

Health records are associated with specific clinics:

```javascript
const healthRecordSchema = new mongoose.Schema({
    petId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Pet'
    },
    // ...other fields
    
    // Clinic association
    clinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic',
        required: true,
        index: true
    }
});
```

## API Endpoints

### Authentication

- `POST /auth/sign-in`: Enhanced to return available clinics and set the current clinic

### Clinic Management

- `POST /clinics/switch`: Switch the current clinic for a user
- `GET /clinics`: Get all clinics (filtered by user access)
- `GET /clinics/:id`: Get a specific clinic
- `POST /clinics/create`: Create a new clinic
- `PUT /clinics/update/:id`: Update a clinic
- `DELETE /clinics/delete/:id`: Delete a clinic

## Frontend Components

### Clinic Switcher

The `ClinicSwitcher` component allows users to switch between clinics they have access to:

```tsx
<ClinicSwitcher />
```

This component is displayed in the Navbar for users who have access to multiple clinics or are clinic owners.

### Auth Store

The Auth store has been enhanced to support clinic switching:

```typescript
interface AuthState {
    // ...other fields
    availableClinics: Clinic[];
    currentClinic: Clinic | null;
    switchClinic: (clinicId: string) => Promise<StandardResponse<{
        token: string;
        currentClinic: Clinic;
    }>>;
}
```

## User Flows

### Login Flow

1. User logs in with email and password
2. System authenticates the user and retrieves their profile
3. If the user is a staff member, the system retrieves their associated clinics
4. The system sets the user's current clinic to their primary clinic
5. The system returns the user profile, available clinics, and the current clinic

### Clinic Switching Flow

1. User selects a different clinic from the clinic switcher
2. System verifies the user has access to the selected clinic
3. System updates the user's current clinic
4. System generates a new token with the updated clinic information
5. Frontend updates to show data for the selected clinic

## Access Control

### Middleware

Several middleware functions are available to control access:

- `verifyToken`: Verifies the user's authentication token and adds the user and clinic to the request
- `hasClinicAccess`: Checks if the user has access to a specific clinic
- `isClinicOwner`: Checks if the user is the owner of a specific clinic
- `hasPermission`: Checks if the user has specific permissions

### Permission Checking

Staff permissions are checked based on their role and any special permissions:

```javascript
// Check if staff has a specific permission
const hasPermission = await staff.hasPermission(permissionId);

// Check if staff has access to a specific clinic
const hasAccess = staff.hasClinicAccess(clinicId);
```

## Implementation Notes

### Clinic Data Isolation

All data that is clinic-specific (appointments, medical records, inventory, etc.) should include a `clinicId` field and be filtered by the current clinic when retrieved.

### User Types

The system supports three user types:

1. **Client**: Pet owners who use the system to manage their pets
2. **Staff**: Employees of clinics (vets, receptionists, etc.)
3. **Admin**: System administrators with full access

### Clinic Owner vs. Staff

Clinic owners have additional permissions:

- Can manage clinic settings
- Can add/remove staff
- Can view financial reports
- Can manage multiple clinics

Regular staff members can only:

- View and interact with data for clinics they are assigned to
- Perform actions based on their role permissions

## Future Enhancements

1. **Clinic Groups**: Allow clinics to be organized into groups for easier management
2. **Staff Scheduling**: Enhanced scheduling system for staff across multiple clinics
3. **Cross-Clinic Reporting**: Aggregate reports across multiple clinics
4. **Clinic-Specific Settings**: Allow different settings for each clinic
5. **Clinic-Specific Branding**: Custom branding for each clinic
