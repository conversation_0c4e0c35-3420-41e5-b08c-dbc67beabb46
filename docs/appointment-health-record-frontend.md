# Appointment and Health Record Frontend Implementation

This document provides detailed guidance for implementing the appointment scheduling and health record management features in the Vet-Care SaaS system frontend.

## Table of Contents

1. [Appointment Management](#appointment-management)
   - [Appointment Booking](#appointment-booking)
   - [Appointment Calendar](#appointment-calendar)
   - [Appointment Details](#appointment-details)
2. [Health Record Management](#health-record-management)
   - [Health Record Creation](#health-record-creation)
   - [Health Record Viewing](#health-record-viewing)
   - [Medical History](#medical-history)
3. [Integration with Workflows](#integration-with-workflows)
   - [Appointment to Health Record](#appointment-to-health-record)
   - [Health Record to Invoice](#health-record-to-invoice)
4. [UI Components](#ui-components)
5. [Implementation Examples](#implementation-examples)

## Appointment Management

### Appointment Booking

#### Booking Flow

Create a multi-step appointment booking process:

1. **Client and Pet Selection**
   - Search for existing client
   - Select pet(s) for appointment
   - Option to add new client/pet

2. **Service Selection**
   - Choose appointment type/service
   - Select staff member (optional)
   - Add special instructions

3. **Date and Time Selection**
   - Calendar view of available slots
   - Time slot selection based on service duration
   - Conflict detection

4. **Confirmation**
   - Appointment summary
   - Client contact information
   - Confirmation options (email, SMS)

#### Implementation Considerations

- Use context to maintain state across booking steps
- Implement real-time availability checking
- Add conflict detection for staff and resources
- Provide clear visual feedback for selected options

### Appointment Calendar

#### Calendar Views

Implement multiple calendar views:

1. **Day View**
   - Hour-by-hour schedule
   - Color-coded by appointment type
   - Staff column options

2. **Week View**
   - Weekly overview
   - Quick appointment creation
   - Resource allocation view

3. **Month View**
   - Monthly overview
   - Appointment count indicators
   - Quick navigation to day/week views

#### Appointment Management

Implement appointment management features:

- Drag-and-drop rescheduling
- Right-click context menu for actions
- Quick view of appointment details
- Status updates (confirmed, checked-in, completed)

### Appointment Details

#### Detail View

Create a comprehensive appointment detail view:

1. **Header Section**
   - Appointment type and time
   - Client and pet information
   - Status indicator and actions

2. **Details Section**
   - Service information
   - Staff assigned
   - Special instructions
   - Related records

3. **Action Buttons**
   - Check-in
   - Reschedule
   - Cancel
   - Create health record

#### Check-in Process

Implement appointment check-in workflow:

```tsx
const AppointmentCheckIn = ({ appointmentId }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: appointment, isLoading } = useQuery({
    queryKey: ["appointment", appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
  });
  
  const checkInMutation = useMutation({
    mutationFn: checkInAppointment,
    onSuccess: () => {
      queryClient.invalidateQueries(["appointment", appointmentId]);
      queryClient.invalidateQueries(["appointments"]);
      toast({
        title: "Success",
        description: "Appointment checked in successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to check in appointment",
        variant: "destructive",
      });
    },
  });
  
  const handleCheckIn = async () => {
    await checkInMutation.mutate({
      appointmentId,
      checkInTime: new Date(),
    });
  };
  
  if (isLoading) return <LoadingSpinner />;
  
  if (!appointment?.data) {
    return <div>Appointment not found</div>;
  }
  
  const isCheckedIn = appointment.data.status === 'checked_in';
  
  return (
    <div className="p-6 bg-card rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Appointment Check-In</h2>
        <Badge variant={isCheckedIn ? "success" : "default"}>
          {isCheckedIn ? "Checked In" : appointment.data.status}
        </Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Client Information</h3>
          <ClientCard client={appointment.data.clientId} />
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Pet Information</h3>
          <PetCard pet={appointment.data.petId} />
        </div>
      </div>
      
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Appointment Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Service</p>
            <p>{appointment.data.serviceId.name}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Date & Time</p>
            <p>{format(new Date(appointment.data.appointmentDate), 'PPP p')}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Duration</p>
            <p>{appointment.data.duration} minutes</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Staff</p>
            <p>{appointment.data.staffId?.firstName} {appointment.data.staffId?.lastName}</p>
          </div>
        </div>
      </div>
      
      {appointment.data.notes && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Notes</h3>
          <p className="p-3 bg-muted rounded">{appointment.data.notes}</p>
        </div>
      )}
      
      <div className="flex justify-end space-x-4">
        <Button
          variant="outline"
          onClick={() => navigate(`/appointments/${appointmentId}`)}
        >
          Cancel
        </Button>
        
        <Button
          onClick={handleCheckIn}
          disabled={isCheckedIn || checkInMutation.isPending}
        >
          {checkInMutation.isPending ? "Processing..." : "Check In"}
        </Button>
      </div>
    </div>
  );
};
```

## Health Record Management

### Health Record Creation

#### Creation Form

Create a comprehensive health record form:

1. **Basic Information**
   - Record type (consultation, vaccination, surgery, etc.)
   - Date and time
   - Performing staff member

2. **Medical Details**
   - Diagnosis
   - Treatment
   - Medications
   - Lab results
   - Vital signs

3. **Follow-up Information**
   - Follow-up date
   - Instructions
   - Attachments

#### Dynamic Form Fields

Implement dynamic form fields based on record type:

```tsx
const HealthRecordForm = ({ petId, appointmentId }) => {
  const [recordType, setRecordType] = useState("consultation");
  
  // Fetch pet data
  const { data: petData } = useQuery({
    queryKey: ["pet", petId],
    queryFn: () => getPet(petId),
  });
  
  // Fetch appointment data if available
  const { data: appointmentData } = useQuery({
    queryKey: ["appointment", appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
    enabled: !!appointmentId,
  });
  
  // Form setup
  const form = useForm({
    defaultValues: {
      petId,
      appointmentId,
      recordType: "consultation",
      date: new Date(),
      description: "",
      diagnosis: "",
      treatment: "",
      medications: [],
      labResults: [],
      vitalSigns: {
        temperature: "",
        heartRate: "",
        respiratoryRate: "",
        weight: "",
        bloodPressure: ""
      },
      followUpDate: "",
      followUpInstructions: "",
      notes: ""
    }
  });
  
  // Watch record type to show/hide fields
  const watchedRecordType = form.watch("recordType");
  
  // Pre-fill form when appointment data is available
  useEffect(() => {
    if (appointmentData?.data) {
      const appointment = appointmentData.data;
      form.setValue("clinicId", appointment.clinicId);
      form.setValue("serviceId", appointment.serviceId._id);
      form.setValue("performedBy", appointment.staffId?._id);
      form.setValue("description", `${appointment.serviceId.name} for ${petData?.data?.name}`);
    }
  }, [appointmentData, form, petData]);
  
  // Handle form submission
  const onSubmit = async (values) => {
    try {
      const result = await createHealthRecord(values);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Health record created successfully",
        });
        navigate(`/health-records/${result.data._id}`);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Failed to create health record",
        variant: "destructive",
      });
    }
  };
  
  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <div className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="recordType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Record Type</FormLabel>
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    setRecordType(value);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select record type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="vaccination">Vaccination</SelectItem>
                    <SelectItem value="surgery">Surgery</SelectItem>
                    <SelectItem value="laboratory">Laboratory</SelectItem>
                    <SelectItem value="imaging">Imaging</SelectItem>
                    <SelectItem value="prescription">Prescription</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date</FormLabel>
                <DateTimePicker
                  value={field.value}
                  onChange={field.onChange}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          
          <StaffSelector
            control={form.control}
            name="performedBy"
            label="Performed By"
          />
        </div>
        
        {/* Basic fields for all record types */}
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <Textarea
                  placeholder="Enter description"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Conditional fields based on record type */}
          {(watchedRecordType === "consultation" || watchedRecordType === "surgery") && (
            <>
              <FormField
                control={form.control}
                name="diagnosis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Diagnosis</FormLabel>
                    <Textarea
                      placeholder="Enter diagnosis"
                      {...field}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="treatment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment</FormLabel>
                    <Textarea
                      placeholder="Enter treatment"
                      {...field}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <VitalSignsFields control={form.control} />
            </>
          )}
          
          {/* Medication fields */}
          {(watchedRecordType === "consultation" || 
            watchedRecordType === "prescription" || 
            watchedRecordType === "surgery") && (
            <MedicationsFieldArray control={form.control} name="medications" />
          )}
          
          {/* Lab results fields */}
          {(watchedRecordType === "laboratory" || watchedRecordType === "consultation") && (
            <LabResultsFieldArray control={form.control} name="labResults" />
          )}
          
          {/* Follow-up fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="followUpDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Follow-up Date</FormLabel>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="followUpInstructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Follow-up Instructions</FormLabel>
                  <Textarea
                    placeholder="Enter follow-up instructions"
                    {...field}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Additional Notes</FormLabel>
                <Textarea
                  placeholder="Enter additional notes"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => navigate(-1)}>
            Cancel
          </Button>
          <Button type="submit">Save Health Record</Button>
        </div>
      </div>
    </form>
  );
};
```

### Health Record Viewing

#### Detail View

Create a comprehensive health record detail view:

1. **Header Section**
   - Record type and date
   - Pet and client information
   - Clinic and staff information

2. **Medical Details**
   - Diagnosis and treatment
   - Medications with dosage
   - Lab results with interpretation
   - Vital signs with normal ranges

3. **Follow-up Information**
   - Follow-up date and instructions
   - Reminder status
   - Related appointments

### Medical History

#### Timeline View

Implement a chronological timeline of medical events:

1. **Timeline Layout**
   - Vertical timeline with date markers
   - Color-coded by record type
   - Expandable record cards

2. **Filtering Options**
   - Filter by date range
   - Filter by record type
   - Filter by clinic

3. **Grouping Options**
   - Group by record type
   - Group by clinic
   - Group by year/month

## Integration with Workflows

### Appointment to Health Record

#### Workflow Integration

Implement UI for the appointment to health record workflow:

1. **Appointment Check-in**
   - Update appointment status
   - Create draft health record
   - Show notification to staff

2. **Health Record Creation**
   - Pre-fill from appointment data
   - Link to appointment
   - Validate required fields

3. **Appointment Completion**
   - Update appointment status
   - Finalize health record
   - Trigger invoice generation

### Health Record to Invoice

#### Invoice Generation

Implement UI for the health record to invoice workflow:

1. **Service Recording**
   - Record billable services
   - Add medications and supplies
   - Calculate costs

2. **Invoice Preview**
   - Show line items and totals
   - Apply discounts if needed
   - Finalize invoice

3. **Payment Processing**
   - Accept payment methods
   - Generate receipt
   - Update account balance

## UI Components

### Appointment Components

1. **AppointmentCard**
   - Compact display of appointment information
   - Shows time, client, pet, and service
   - Status indicator and quick actions

2. **AppointmentCalendarEvent**
   - Calendar event representation
   - Color-coded by type
   - Drag-and-drop enabled

3. **TimeSlotPicker**
   - Visual time slot selection
   - Shows availability
   - Handles conflicts

### Health Record Components

1. **HealthRecordCard**
   - Compact display of health record
   - Shows type, date, and key information
   - Expandable for details

2. **MedicalTimelineItem**
   - Timeline representation of medical event
   - Icon based on record type
   - Preview of key information

3. **VitalSignsDisplay**
   - Visual representation of vital signs
   - Shows normal ranges
   - Highlights abnormal values

## Implementation Examples

### Appointment Calendar

```tsx
const AppointmentCalendar = () => {
  const [view, setView] = useState("week");
  const [currentDate, setCurrentDate] = useState(new Date());
  
  // Fetch appointments based on date range
  const { data: appointmentsData, isLoading } = useQuery({
    queryKey: ["appointments", getDateRangeForView(view, currentDate)],
    queryFn: () => getAppointments(getDateRangeForView(view, currentDate)),
  });
  
  // Format appointments for calendar
  const events = useMemo(() => {
    if (!appointmentsData?.data) return [];
    
    return appointmentsData.data.map(appointment => ({
      id: appointment._id,
      title: `${appointment.petId.name} - ${appointment.serviceId.name}`,
      start: new Date(appointment.appointmentDate),
      end: addMinutes(new Date(appointment.appointmentDate), appointment.duration),
      resourceId: appointment.staffId?._id,
      status: appointment.status,
      color: getStatusColor(appointment.status)
    }));
  }, [appointmentsData]);
  
  // Handle event click
  const handleEventClick = (info) => {
    navigate(`/appointments/${info.event.id}`);
  };
  
  // Handle date navigation
  const handleDateChange = (action) => {
    if (action === "prev") {
      setCurrentDate(prevDate => {
        if (view === "day") return subDays(prevDate, 1);
        if (view === "week") return subWeeks(prevDate, 1);
        return subMonths(prevDate, 1);
      });
    } else if (action === "next") {
      setCurrentDate(prevDate => {
        if (view === "day") return addDays(prevDate, 1);
        if (view === "week") return addWeeks(prevDate, 1);
        return addMonths(prevDate, 1);
      });
    } else if (action === "today") {
      setCurrentDate(new Date());
    }
  };
  
  if (isLoading) return <LoadingSpinner />;
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDateChange("prev")}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDateChange("today")}
          >
            Today
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDateChange("next")}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          <h2 className="text-xl font-bold">
            {formatCalendarTitle(view, currentDate)}
          </h2>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={view === "day" ? "default" : "outline"}
            size="sm"
            onClick={() => setView("day")}
          >
            Day
          </Button>
          
          <Button
            variant={view === "week" ? "default" : "outline"}
            size="sm"
            onClick={() => setView("week")}
          >
            Week
          </Button>
          
          <Button
            variant={view === "month" ? "default" : "outline"}
            size="sm"
            onClick={() => setView("month")}
          >
            Month
          </Button>
          
          <Button onClick={() => navigate("/appointments/new")}>
            New Appointment
          </Button>
        </div>
      </div>
      
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        headerToolbar={false}
        initialView={getCalendarViewType(view)}
        events={events}
        eventClick={handleEventClick}
        eventTimeFormat={{
          hour: "numeric",
          minute: "2-digit",
          meridiem: "short"
        }}
        slotMinTime="08:00:00"
        slotMaxTime="18:00:00"
        allDaySlot={false}
        initialDate={currentDate}
        height="auto"
        eventContent={renderEventContent}
      />
    </div>
  );
};
```
