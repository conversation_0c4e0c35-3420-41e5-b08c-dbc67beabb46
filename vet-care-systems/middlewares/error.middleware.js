import { toAPIError } from '../utils/errors.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Global error handling middleware
 * Converts all errors to standardized API responses
 */
const errorMiddleware = (err, req, res, next) => {
    try {
        // Log the error for debugging
        console.error('Error:', {
            message: err.message,
            stack: err.stack,
            path: req.path,
            method: req.method,
            timestamp: new Date().toISOString()
        });

        // Convert to standardized API error
        const apiError = toAPIError(err);

        // Send standardized response
        return sendResponse(
            res,
            apiError.statusCode,
            false,
            apiError.message,
            apiError.data
        );
    } catch (unexpectedError) {
        // Fallback for errors in the error handler itself
        console.error('Error in error handler:', unexpectedError);
        return res.status(500).json({
            success: false,
            status: 500,
            message: 'Internal server error',
            data: null
        });
    }
}

export default errorMiddleware;