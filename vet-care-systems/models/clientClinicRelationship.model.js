import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * ClientClinicRelationship Model
 *
 * This model represents the relationship between a client and a clinic.
 * It tracks the history of client interactions with specific clinics and
 * stores clinic-specific client information.
 */
const clientClinicRelationshipSchema = new mongoose.Schema({
    // Auto-increment ID
    clientClinicRelationshipId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Core relationship identifiers
    clientId: {
        type: Number,
        ref: 'Client',
        required: [true, "Client ID is required"],
        index: true
    },
    clinicId: {
        type: Number,
        ref: 'Clinic',
        required: [true, "Clinic ID is required"],
        index: true
    },

    // Relationship metadata
    relationshipStatus: {
        type: String,
        enum: ['active', 'inactive', 'blocked'],
        default: 'active'
    },
    isPreferredClinic: {
        type: Boolean,
        default: false
    },

    // First interaction information
    firstVisitDate: {
        type: Date,
        default: Date.now
    },
    registeredBy: {
        type: Number,
        ref: 'Staff'
    },

    // Visit history
    visitCount: {
        type: Number,
        default: 1
    },
    lastVisitDate: {
        type: Date,
        default: Date.now
    },

    // Clinic-specific client information
    clinicNotes: {
        type: String,
        trim: true
    },

    // Data sharing and consent
    dataConsent: {
        consentGiven: {
            type: Boolean,
            default: true
        },
        consentDate: {
            type: Date,
            default: Date.now
        },
        consentDetails: {
            type: String,
            trim: true
        }
    },

    // Communication preferences specific to this clinic
    communicationPreferences: {
        email: {
            type: Boolean,
            default: true
        },
        sms: {
            type: Boolean,
            default: true
        },
        phone: {
            type: Boolean,
            default: true
        },
        marketingCommunications: {
            type: Boolean,
            default: false
        }
    },

    // Financial information
    accountBalance: {
        type: Number,
        default: 0
    },
    paymentHistory: [{
        date: Date,
        amount: Number,
        paymentMethod: String,
        invoiceId: {
            type: Number,
            ref: 'Invoice'
        }
    }],

    // Referral information
    referralSource: {
        type: String,
        trim: true
    },

    // Custom fields that clinics can define
    customFields: {
        type: Map,
        of: mongoose.Schema.Types.Mixed
    }
}, {
    timestamps: true,
    collection: 'clientClinicRelationships'
});

// Create a compound index for client-clinic pairs
clientClinicRelationshipSchema.index({ clientId: 1, clinicId: 1 }, { unique: true });

// Create indexes for common queries
clientClinicRelationshipSchema.index({ clinicId: 1, relationshipStatus: 1 });
clientClinicRelationshipSchema.index({ clientId: 1, isPreferredClinic: 1 });
clientClinicRelationshipSchema.index({ lastVisitDate: -1 });

// Add auto-increment plugin
clientClinicRelationshipSchema.plugin(AutoIncrement, { inc_field: 'clientClinicRelationshipId', start_seq: 1001 });
clientClinicRelationshipSchema.index({ clientClinicRelationshipId: 1 }, { unique: true });

const ClientClinicRelationship = mongoose.model('ClientClinicRelationship', clientClinicRelationshipSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = ClientClinicRelationship.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await ClientClinicRelationship.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default ClientClinicRelationship;
