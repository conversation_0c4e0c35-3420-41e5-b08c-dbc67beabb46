import HealthRecord from '../models/healthRecord.model.js';
import Pet from '../models/pet.model.js';
import PetClinicRelationship from '../models/petClinicRelationship.model.js';
import Discount from '../models/discount.model.js';
import Receipt from '../models/receipt.model.js';
import ServiceType from '../models/serviceType.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

/**
 * Create a new health record
 * This function creates a health record and links it to the pet-clinic relationship
 */
export const createHealthRecord = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { petId, clinicId, serviceId, performedBy, recordType, ...recordData } = req.body;

        // Check if pet exists
        const pet = await Pet.findOne({ petId }).session(session);
        if (!pet) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Find or create pet-clinic relationship
        let petClinicRelationship = await PetClinicRelationship.findOne({
            petId,
            clinicId
        }).session(session);

        if (!petClinicRelationship) {
            // Create new relationship
            [petClinicRelationship] = await PetClinicRelationship.create([{
                petId,
                clinicId,
                registeredBy: performedBy
            }], { session });

            // Update pet's clinicRelationships array
            await Pet.findOneAndUpdate(
                { petId },
                { $push: { clinicRelationships: petClinicRelationship.petClinicRelationshipId } },
                { session }
            );
        }

        // Create the health record with relationship
        const healthRecord = await HealthRecord.create([{
            petId,
            clinicId,
            serviceId,
            performedBy,
            recordType: recordType || 'consultation',
            petClinicRelationshipId: petClinicRelationship.petClinicRelationshipId,
            ...recordData
        }], { session });

        // Update the pet-clinic relationship with the visit
        await PetClinicRelationship.findOneAndUpdate(
            {petClinicRelationshipId: petClinicRelationship.petClinicRelationshipId},
            {
                $inc: { visitCount: 1 },
                lastVisitDate: new Date()
            },
            { session }
        );

        // Populate the record with related data
        const populatedRecord = await HealthRecord.findOne({ _id: healthRecord[0]._id })
            .populate('petId', 'petName')
            .populate('serviceId', 'name price')
            .populate('performedBy', 'firstName lastName')
            .populate('clinicId', 'clinicName')
            .populate('petClinicRelationshipId')
            .session(session);

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 201, true, "Health record created successfully", populatedRecord);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Create health record error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get all health records for a specific pet
 * This function retrieves health records with minimal data fetching
 */
export const getHealthRecordsByPet = async (req, res) => {
    try {
        const { petId } = req.params;
        const {
            page = 1,
            limit = 10,
            recordType,
            startDate,
            endDate,
            clinicId,
            includeDetails = false
        } = req.query;

        // Build query with filters
        const query = { petId };

        // Add optional filters
        if (recordType) {
            query.recordType = recordType;
        }

        if (startDate || endDate) {
            query.date = {};
            if (startDate) {
                query.date.$gte = new Date(startDate);
            }
            if (endDate) {
                query.date.$lte = new Date(endDate);
            }
        }

        if (clinicId) {
            query.clinicId = clinicId;
        }

        // Determine which fields to select based on detail level
        let projection = 'recordType date description diagnosis clinicId performedBy';
        if (includeDetails === 'true' || includeDetails === true) {
            projection += ' treatment medications labResults vitalSigns attachments followUpDate followUpInstructions notes';
        }

        // Set up population options with minimal fields
        const populateOptions = [
            { path: 'clinicId', select: 'clinicName' },
            { path: 'performedBy', select: 'firstName lastName' },
            { path: 'serviceId', select: 'name' }
        ];

        if (includeDetails === 'true' || includeDetails === true) {
            populateOptions.push({ path: 'appointmentId', select: 'appointmentDate status' });
        }

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const recordsQuery = HealthRecord.find(query)
            .select(projection)
            .populate(populateOptions)
            .sort({ date: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalCount = await HealthRecord.countDocuments(query);

        // Execute query
        const records = await recordsQuery.lean();

        // Format response with pagination
        const paginatedResponse = {
            data: records,
            pagination: {
                totalRecords: totalCount,
                totalPages: Math.ceil(totalCount / parseInt(limit)),
                currentPage: parseInt(page),
                recordsPerPage: parseInt(limit)
            }
        };

        return sendResponse(res, 200, true, "Health records retrieved successfully", paginatedResponse);
    } catch (error) {
        console.error("Get health records error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get a specific health record by ID
 */
export const getHealthRecordById = async (req, res) => {
    try {
        const { recordId } = req.params;

        const record = await HealthRecord.findOne({ healthRecordId: parseInt(recordId) })
            .populate('petId petName')
            .populate('serviceId name price')
            .populate('performedBy firstName lastName')
            .populate('appointmentId appointmentDate status');

        if (!record) {
            return sendResponse(res, 404, false, "Health record not found");
        }

        return sendResponse(res, 200, true, "Health record retrieved successfully", record);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update a health record
 */
export const updateHealthRecord = async (req, res) => {
    try {
        const { recordId } = req.params;

        const record = await HealthRecord.findOneAndUpdate(
            { healthRecordId: parseInt(recordId) },
            req.body,
            { new: true, runValidators: true }
        ).populate(['petId petName', 'serviceId name price', 'performedBy firstName lastName', 'appointmentId appointmentDate status']);

        if (!record) {
            return sendResponse(res, 404, false, "Health record not found");
        }

        return sendResponse(res, 200, true, "Health record updated successfully", record);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete a health record
 */
export const deleteHealthRecord = async (req, res) => {
    try {
        const { recordId } = req.params;

        const record = await HealthRecord.findOneAndDelete({ healthRecordId: parseInt(recordId) });

        if (!record) {
            return sendResponse(res, 404, false, "Health record not found");
        }

        return sendResponse(res, 200, true, "Health record deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get pet's complete medical history
 * This function retrieves a comprehensive medical history across all clinics
 */
export const getPetMedicalHistory = async (req, res) => {
    try {
        const { petId } = req.params;
        const {
            page = 1,
            limit = 20,
            groupByType = false,
            includeDetails = false
        } = req.query;

        // Check if pet exists and if data sharing is allowed
        const pet = await Pet.findOne({ petId }).select('dataSharingPreferences').lean();
        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Check data sharing permissions (simplified for now)
        const canShareMedicalHistory = pet.dataSharingPreferences?.shareAllClinics !== false;
        if (!canShareMedicalHistory) {
            return sendResponse(res, 403, false, "Access to this pet's medical history is restricted");
        }

        // Determine which fields to select based on detail level
        let projection = 'recordType date description diagnosis clinicId performedBy';
        if (includeDetails === 'true' || includeDetails === true) {
            projection += ' treatment medications labResults vitalSigns attachments followUpDate followUpInstructions notes';
        }

        // Set up population options with minimal fields
        const populateOptions = [
            { path: 'clinicId', select: 'clinicName' },
            { path: 'performedBy', select: 'firstName lastName' },
            { path: 'serviceId', select: 'name' }
        ];

        // If grouping by type is requested
        if (groupByType === 'true' || groupByType === true) {
            // Use aggregation to group records by type
            const aggregation = [
                { $match: { petId: mongoose.Types.ObjectId(petId) } },
                { $sort: { date: -1 } },
                { $group: {
                    _id: '$recordType',
                    records: { $push: '$$ROOT' },
                    count: { $sum: 1 }
                }},
                { $project: {
                    recordType: '$_id',
                    records: { $slice: ['$records', 0, parseInt(limit)] },
                    totalRecords: '$count',
                    _id: 0
                }}
            ];

            const groupedRecords = await HealthRecord.aggregate(aggregation);

            // Manually populate the references
            for (const group of groupedRecords) {
                for (let i = 0; i < group.records.length; i++) {
                    const record = group.records[i];

                    // Populate clinicId
                    if (record.clinicId) {
                        const clinic = await mongoose.model('Clinic').findById(record.clinicId).select('clinicName').lean();
                        record.clinicId = clinic;
                    }

                    // Populate performedBy
                    if (record.performedBy) {
                        const staff = await mongoose.model('Staff').findById(record.performedBy).select('firstName lastName').lean();
                        record.performedBy = staff;
                    }

                    // Populate serviceId
                    if (record.serviceId) {
                        const service = await mongoose.model('ServiceType').findById(record.serviceId).select('name').lean();
                        record.serviceId = service;
                    }
                }
            }

            return sendResponse(res, 200, true, "Medical history retrieved successfully", groupedRecords);
        }
        // Standard pagination approach
        else {
            // Execute query with pagination
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const recordsQuery = HealthRecord.find({ petId })
                .select(projection)
                .populate(populateOptions)
                .sort({ date: -1 })
                .skip(skip)
                .limit(parseInt(limit));

            // Get total count for pagination
            const totalCount = await HealthRecord.countDocuments({ petId });

            // Execute query
            const records = await recordsQuery.lean();

            // Format response with pagination
            const paginatedResponse = {
                data: records,
                pagination: {
                    totalRecords: totalCount,
                    totalPages: Math.ceil(totalCount / parseInt(limit)),
                    currentPage: parseInt(page),
                    recordsPerPage: parseInt(limit)
                }
            };

            return sendResponse(res, 200, true, "Medical history retrieved successfully", paginatedResponse);
        }
    } catch (error) {
        console.error("Get pet medical history error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Apply discount to health record
 */
export const applyDiscount = async (req, res) => {
    try {
        const { recordId } = req.params;
        const {
            discountType,
            discountValue,
            reason,
            authorizedBy,
            clientId,
            category = 'other',
            notes
        } = req.body;

        const healthRecord = await HealthRecord.findOne({
            healthRecordId: parseInt(recordId)
        });

        if (!healthRecord) {
            return sendResponse(res, 404, false, "Health record not found");
        }

        const originalAmount = healthRecord.billingDetails.amount;
        let discountAmount = 0;

        if (discountType === 'percentage') {
            discountAmount = (originalAmount * discountValue) / 100;
        } else if (discountType === 'fixed_amount') {
            discountAmount = discountValue;
        } else if (discountType === 'waiver') {
            discountAmount = originalAmount;
        }

        const finalAmount = Math.max(0, originalAmount - discountAmount);

        // Create discount record
        const discount = await Discount.create({
            healthRecordId: parseInt(recordId),
            appointmentId: healthRecord.appointmentId,
            discountType,
            discountValue,
            discountAmount,
            originalAmount,
            finalAmount,
            currency: healthRecord.billingDetails.currency,
            reason,
            authorizedBy,
            clientId,
            clinicId: healthRecord.clinicId,
            category,
            notes
        });

        // Update health record billing
        healthRecord.billingDetails.amount = finalAmount;
        await healthRecord.save();

        // Update receipt if exists
        await Receipt.findOneAndUpdate(
            { healthRecordId: parseInt(recordId) },
            {
                $inc: { totalDiscount: discountAmount },
                $set: { totalAmount: finalAmount },
                $push: {
                    discounts: {
                        discountId: discount.discountId,
                        discountType,
                        discountValue,
                        discountAmount,
                        reason
                    }
                }
            }
        );

        return sendResponse(res, 200, true, "Discount applied successfully", discount);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Generate receipt for health record
 */
export const generateReceipt = async (req, res) => {
    try {
        const { recordId } = req.params;

        const healthRecord = await HealthRecord.findOne({
            healthRecordId: parseInt(recordId)
        }).lean();

        if (!healthRecord) {
            return sendResponse(res, 404, false, "Health record not found");
        }

        // Check if receipt already exists
        let receipt = await Receipt.findOne({
            healthRecordId: parseInt(recordId)
        }).lean();

        if (receipt) {
            return sendResponse(res, 200, true, "Receipt already exists", receipt);
        }

        // Get service details
        const service = await ServiceType.findOne({
            serviceTypeId: healthRecord.serviceId
        }).lean();

        // Get discounts for this health record
        const discounts = await Discount.find({
            healthRecordId: parseInt(recordId)
        }).lean();

        const totalDiscount = discounts.reduce((sum, d) => sum + d.discountAmount, 0);

        // Create receipt
        receipt = await Receipt.create({
            healthRecordId: parseInt(recordId),
            appointmentId: healthRecord.appointmentId,
            clientId: req.body.clientId,
            petId: healthRecord.petId,
            clinicId: healthRecord.clinicId,
            services: [{
                serviceId: healthRecord.serviceId,
                serviceName: service?.name || 'Service',
                description: healthRecord.description,
                quantity: 1,
                unitPrice: healthRecord.billingDetails.amount + totalDiscount,
                totalPrice: healthRecord.billingDetails.amount + totalDiscount
            }],
            subtotal: healthRecord.billingDetails.amount + totalDiscount,
            totalDiscount,
            totalAmount: healthRecord.billingDetails.amount,
            currency: healthRecord.billingDetails.currency,
            discounts: discounts.map(d => ({
                discountId: d.discountId,
                discountType: d.discountType,
                discountValue: d.discountValue,
                discountAmount: d.discountAmount,
                reason: d.reason
            })),
            paymentStatus: healthRecord.billingDetails.paymentStatus,
            issuedBy: healthRecord.performedBy,
            status: 'issued'
        });

        return sendResponse(res, 201, true, "Receipt generated successfully", receipt);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get receipt by health record ID
 */
export const getReceiptByHealthRecord = async (req, res) => {
    try {
        const { recordId } = req.params;

        const receipt = await Receipt.findOne({
            healthRecordId: parseInt(recordId)
        }).lean();

        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found");
        }

        // Get related data
        const [healthRecord, clientData, petData, clinicData, staffData] = await Promise.all([
            HealthRecord.findOne({ healthRecordId: parseInt(recordId) }).lean(),
            Receipt.model('Client').findOne({ clientId: receipt.clientId }).lean(),
            Receipt.model('Pet').findOne({ petId: receipt.petId }).lean(),
            Receipt.model('Clinic').findOne({ clinicId: receipt.clinicId }).lean(),
            Receipt.model('Staff').findOne({ staffId: receipt.issuedBy }).lean()
        ]);

        receipt.healthRecord = healthRecord;
        receipt.clientData = clientData;
        receipt.petData = petData;
        receipt.clinicData = clinicData;
        receipt.staffData = staffData;

        return sendResponse(res, 200, true, "Receipt retrieved successfully", receipt);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};