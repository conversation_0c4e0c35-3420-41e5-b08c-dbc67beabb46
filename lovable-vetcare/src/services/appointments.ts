
import { Appointment, PaginationData } from '@/store/types';
import { api } from './api';
import { useStore } from '@/store';

export interface AppointmentResponse {
  success: boolean;
  status: number;
  message: string;
  data: Appointment | null;
}

export interface AppointmentsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Appointment[];
    pagination: PaginationData;
  };
}

export interface AppointmentSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: number;
  date?: string;
  clinicId?: string;
  petId?: string;
  clientId?: string;
  staffId?: string;
  [key: string]: any;
}

// Helper function to get clinic context
const getClinicId = () => {
  const state = useStore.getState();
  return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

export const getAppointments = async (params: AppointmentSearchParams = {}): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    // Temporarily disable automatic clinicId filtering for debugging
    // const clinicId = getClinicId();
    // if (clinicId && !params.clinicId) {
    //   params.clinicId = clinicId.toString();
    // }

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    const url = `/appointments?${queryParams.toString()}`;
    console.log("🌐 Fetching appointments from URL:", url);

    const response = await api.get<AppointmentsResponse>(url);
    console.log("📡 Raw appointments API response:", response);

    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointments',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

export const getAppointmentById = async (appointmentId: string | number): Promise<AppointmentResponse> => {
  try {
    console.log("🔍 Fetching appointment by ID:", appointmentId);
    const response = await api.get<AppointmentResponse>(`/appointments/${appointmentId}`);
    console.log("📡 Appointment details response:", response);
    return response;
  } catch (error: any) {
    console.error("❌ Error fetching appointment:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment',
      data: null
    };
  }
};

export const createAppointment = async (appointmentData: Partial<Appointment>): Promise<AppointmentResponse> => {
  try {
    return await api.post<AppointmentResponse>('/appointments', appointmentData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create appointment',
      data: null
    };
  }
};

export const updateAppointment = async (
  appointmentId: string,
  appointmentData: Partial<Omit<Appointment, '_id'>>
): Promise<AppointmentResponse> => {
  try {
    return await api.put<AppointmentResponse>(`/appointments/${appointmentId}`, appointmentData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update appointment',
      data: null
    };
  }
};

export const deleteAppointment = async (appointmentId: string): Promise<AppointmentResponse> => {
  try {
    return await api.delete<AppointmentResponse>(`/appointments/${appointmentId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete appointment',
      data: null
    };
  }
};

/**
 * Interface for appointment type response
 */
export interface AppointmentTypesResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Array<{
      appointmentTypeId: number;
      name: string;
      description?: string;
      defaultDuration: number;
      price: number;
      currency: string;
      isActive: boolean;
    }>;
    pagination: {
      totalCount: number;
      page: number;
      limit: number;
      offset: number;
      totalPages: number;
    };
  } | null;
}

/**
 * Get available appointment types
 */
export const getAppointmentTypes = async (): Promise<AppointmentTypesResponse> => {
  try {
    return await api.get<AppointmentTypesResponse>('/appointments/types');
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment types',
      data: null
    };
  }
};

/**
 * Get appointments for a specific client
 */
export const getAppointmentsByClient = async (clientId: string, params: AppointmentSearchParams = {}): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit', 'clientId'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<AppointmentsResponse>(`/appointments/client/${clientId}?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch client appointments',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};
