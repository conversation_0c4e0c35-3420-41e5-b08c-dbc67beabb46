import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import Client from '../models/client.model.js';
import Pet from '../models/pet.model.js';
import Species from '../models/species.model.js';
import Breed from '../models/breed.model.js';
import { DB_URI } from '../config/env.js';

// Test data
const testClients = [
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
    address: '123 Main St, City',
    password: 'password123'
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phoneNumber: '+1234567891',
    address: '456 Oak Ave, City',
    password: 'password123'
  },
  {
    firstName: '<PERSON>',
    lastName: 'Brown',
    email: '<EMAIL>',
    phoneNumber: '+1234567892',
    address: '789 Pine St, City',
    password: 'password123'
  }
];

const testPets = [
  // <PERSON>'s pets
  { name: '<PERSON>', species: 'Dog', breed: 'Golden Retriever', gender: 'male', color: '<PERSON>', weight: 30.5 },
  { name: 'Luna', species: 'Cat', breed: 'Persian', gender: 'female', color: 'White', weight: 4.2 },
  { name: 'Max', species: 'Dog', breed: 'German Shepherd', gender: 'male', color: 'Brown', weight: 35.0 },

  // <PERSON>'s pets
  { name: 'Bella', species: 'Dog', breed: 'Labrador Retriever', gender: 'female', color: 'Black', weight: 28.0 },
  { name: 'Whiskers', species: 'Cat', breed: 'Siamese', gender: 'male', color: 'Cream', weight: 5.1 },

  // Michael Brown's pets
  { name: 'Charlie', species: 'Dog', breed: 'Beagle', gender: 'male', color: 'Tricolor', weight: 15.5 },
  { name: 'Mittens', species: 'Cat', breed: 'Maine Coon', gender: 'female', color: 'Gray', weight: 6.8 },
  { name: 'Rocky', species: 'Dog', breed: 'Bulldog', gender: 'male', color: 'Brindle', weight: 25.0 }
];

const walkInClients = [
  {
    firstName: 'Emma',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phoneNumber: '+1234567893',
    address: 'Walk-in client',
    password: 'temp123',
    isWalkIn: true
  },
  {
    firstName: 'David',
    lastName: 'Davis',
    email: '<EMAIL>',
    phoneNumber: '+1234567894',
    address: 'Walk-in client',
    password: 'temp123',
    isWalkIn: true
  },
  {
    firstName: 'Lisa',
    lastName: 'Garcia',
    email: '<EMAIL>',
    phoneNumber: '+1234567895',
    address: 'Walk-in client',
    password: 'temp123',
    isWalkIn: true
  }
];

async function connectDB() {
  try {
    await mongoose.connect(DB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

async function createTestClients() {
  console.log('Creating test clients...');

  const createdClients = [];

  // Create regular clients
  for (const clientData of testClients) {
    try {
      const hashedPassword = await bcrypt.hash(clientData.password, 10);
      const client = new Client({
        ...clientData,
        password: hashedPassword,
        homeLocation: [0, 0],
        isWalkIn: false
      });

      const savedClient = await client.save();
      createdClients.push(savedClient);
      console.log(`Created client: ${savedClient.firstName} ${savedClient.lastName} (ID: ${savedClient.clientId})`);
    } catch (error) {
      console.error(`Error creating client ${clientData.firstName}:`, error.message);
    }
  }

  // Create walk-in clients
  for (const clientData of walkInClients) {
    try {
      const hashedPassword = await bcrypt.hash(clientData.password, 10);
      const client = new Client({
        ...clientData,
        password: hashedPassword,
        homeLocation: [0, 0],
        isWalkIn: true
      });

      const savedClient = await client.save();
      createdClients.push(savedClient);
      console.log(`Created walk-in client: ${savedClient.firstName} ${savedClient.lastName} (ID: ${savedClient.clientId})`);
    } catch (error) {
      console.error(`Error creating walk-in client ${clientData.firstName}:`, error.message);
    }
  }

  return createdClients;
}

async function createTestPets(clients) {
  console.log('Creating test pets...');

  // Get species and breeds
  const species = await Species.find({}).lean();
  const breeds = await Breed.find({}).lean();

  if (species.length === 0 || breeds.length === 0) {
    console.log('No species or breeds found. Please ensure species and breeds are created first.');
    return;
  }

  const speciesMap = species.reduce((map, s) => {
    map[s.speciesName.toLowerCase()] = s;
    return map;
  }, {});

  const breedMap = breeds.reduce((map, b) => {
    map[b.breedName.toLowerCase()] = b;
    return map;
  }, {});

  let petIndex = 0;
  const regularClients = clients.filter(c => !c.isWalkIn);

  for (let i = 0; i < regularClients.length; i++) {
    const client = regularClients[i];
    const petsPerClient = i === 0 ? 3 : i === 1 ? 2 : 3; // 3, 2, 3 pets respectively

    for (let j = 0; j < petsPerClient; j++) {
      if (petIndex >= testPets.length) break;

      const petData = testPets[petIndex];
      const speciesData = speciesMap[petData.species.toLowerCase()];
      const breedData = breedMap[petData.breed.toLowerCase()];

      if (!speciesData || !breedData) {
        console.log(`Skipping pet ${petData.name} - species or breed not found`);
        petIndex++;
        continue;
      }

      try {
        const pet = new Pet({
          name: petData.name,
          speciesId: speciesData.speciesId,
          breedId: breedData.breedId,
          gender: petData.gender,
          color: petData.color,
          weight: petData.weight,
          dateOfBirth: new Date(2020, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          lifeStatus: 'alive',
          ownerId: client.clientId,
          petStatus: 1
        });

        const savedPet = await pet.save();
        console.log(`Created pet: ${savedPet.name} (${petData.species}) for ${client.firstName} ${client.lastName}`);
      } catch (error) {
        console.error(`Error creating pet ${petData.name}:`, error.message);
      }

      petIndex++;
    }
  }
}

async function generateTestData() {
  try {
    await connectDB();

    console.log('Starting test data generation...');

    // Create clients
    const clients = await createTestClients();

    // Create pets
    await createTestPets(clients);

    console.log('Test data generation completed!');
    console.log(`Created ${clients.length} clients and their pets`);

  } catch (error) {
    console.error('Error generating test data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
generateTestData();
