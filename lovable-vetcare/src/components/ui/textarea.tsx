import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const textareaVariants = cva(
  "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200",
  {
    variants: {
      variant: {
        default: "",
        filled: "bg-muted/50 border-transparent hover:border-input focus-visible:bg-background",
        outline: "border-input",
        ghost: "border-transparent bg-transparent hover:bg-muted/30",
      },
      state: {
        default: "",
        error: "border-destructive focus-visible:ring-destructive",
        success: "border-green-500 focus-visible:ring-green-500",
      },
    },
    defaultVariants: {
      variant: "default",
      state: "default",
    },
  }
)

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {
  showCount?: boolean
  maxCount?: number
  autoResize?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, variant, state, showCount, maxCount, autoResize, onChange, ...props }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null)
    const [charCount, setCharCount] = React.useState(0)

    // Merge refs
    const handleRef = (element: HTMLTextAreaElement) => {
      textareaRef.current = element
      if (typeof ref === 'function') {
        ref(element)
      } else if (ref) {
        ref.current = element
      }
    }

    // Auto-resize functionality
    const resizeTextarea = () => {
      if (autoResize && textareaRef.current) {
        textareaRef.current.style.height = 'auto'
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
      }
    }

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (onChange) {
        onChange(e)
      }

      setCharCount(e.target.value.length)

      if (autoResize) {
        resizeTextarea()
      }
    }

    // Initialize character count and auto-resize
    React.useEffect(() => {
      if (textareaRef.current) {
        setCharCount(textareaRef.current.value.length)
        if (autoResize) {
          resizeTextarea()
        }
      }
    }, [autoResize])

    return (
      <div className="relative">
        <textarea
          className={cn(
            textareaVariants({ variant, state }),
            autoResize && "overflow-hidden",
            className
          )}
          ref={handleRef}
          onChange={handleChange}
          {...props}
        />
        {showCount && (
          <div className="absolute bottom-1 right-2 text-xs text-muted-foreground">
            {charCount}{maxCount ? `/${maxCount}` : ''}
          </div>
        )}
      </div>
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea, textareaVariants }
