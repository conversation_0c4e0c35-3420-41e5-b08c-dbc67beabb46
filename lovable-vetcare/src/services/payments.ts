import { api } from './api';

export interface Payment {
  paymentId: number;
  paymentReference: string;
  invoiceId: number;
  appointmentId: number;
  clientId: number;
  clinicId: number;
  amount: number;
  currency: string;
  paymentMethod: 'cash' | 'mpesa' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'cheque' | 'insurance';
  paymentDetails: {
    mpesaTransactionId?: string;
    mpesaReceiptNumber?: string;
    mpesaPhoneNumber?: string;
    cardLastFourDigits?: string;
    cardType?: string;
    authorizationCode?: string;
    bankReference?: string;
    bankName?: string;
    accountNumber?: string;
    chequeNumber?: string;
    chequeBank?: string;
    chequeDate?: string;
    insuranceProvider?: string;
    insuranceClaimNumber?: string;
    insurancePolicyNumber?: string;
  };
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  paymentDate: string;
  processedDate?: string;
  processedBy: number;
  transactionFee: number;
  netAmount: number;
  description?: string;
  notes?: string;
  refundAmount: number;
  refundDate?: string;
  refundReason?: string;
  refundedBy?: number;
  externalTransactionId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentRequest {
  amount: number;
  paymentMethod: Payment['paymentMethod'];
  paymentDetails?: Payment['paymentDetails'];
  description?: string;
  notes?: string;
}

export interface PaymentResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    payment: Payment;
    invoice: any;
    receipt?: any;
  } | null;
}

export interface PaymentsResponse {
  success: boolean;
  status: number;
  message: string;
  data: Payment[];
}

/**
 * Process payment for invoice
 */
export const processPayment = async (
  invoiceId: number, 
  paymentData: PaymentRequest
): Promise<PaymentResponse> => {
  try {
    const response = await api.post(`/payments/invoice/${invoiceId}`, paymentData);
    return response.data;
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to process payment',
      data: null
    };
  }
};

/**
 * Get payment by ID
 */
export const getPaymentById = async (paymentId: number): Promise<{ success: boolean; data: Payment }> => {
  try {
    const response = await api.get(`/payments/${paymentId}`);
    return response.data;
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch payment',
      data: null
    };
  }
};

/**
 * Get payments for invoice
 */
export const getPaymentsByInvoice = async (invoiceId: number): Promise<PaymentsResponse> => {
  try {
    const response = await api.get(`/payments/invoice/${invoiceId}/payments`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch payments',
      data: []
    };
  }
};

/**
 * Refund payment
 */
export const refundPayment = async (
  paymentId: number, 
  refundData: {
    refundAmount: number;
    refundReason: string;
  }
): Promise<{ success: boolean; data: Payment }> => {
  try {
    const response = await api.post(`/payments/${paymentId}/refund`, refundData);
    return response.data;
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to refund payment',
      data: null
    };
  }
};
