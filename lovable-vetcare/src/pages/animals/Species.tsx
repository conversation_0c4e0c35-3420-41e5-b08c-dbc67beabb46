
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw, MoreHorizontal, Eye } from "lucide-react";
import { getSpecies, deleteSpecies } from "@/services/species.ts";
import DebouncedSearchInput from "@/components/common/DebouncedSearchInput";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import LoadingPage from "@/components/common/LoadingPage";
import { useNavigate } from "react-router-dom";
import AddSpeciesModal from "./components/AddSpeciesModal";
import EditSpeciesModal from "./components/EditSpeciesModal";

const Species = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [speciesIdToDelete, setSpeciesIdToDelete] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<any>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { data: response, isLoading, refetch } = useQuery({
    queryKey: ["species", page, limit, searchTerm],
    queryFn: () => getSpecies({ page, offset: (page - 1), limit, search: searchTerm }),
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteSpecies(id),
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Species deleted successfully",
        });
        queryClient.invalidateQueries({queryKey: ["species"]});
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to delete species",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete species",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return <LoadingPage />;
  }

  const handleRefresh = () => {
    refetch();
  };

  const speciesData = response?.data?.data || [];
  const pagination = response?.data?.pagination || {
    totalCount: 0,
    page: 1,
    limit: 5,
    totalPages: 1
  };

  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (page < pagination.totalPages) {
      setPage(page + 1);
    }
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  const openDeleteDialog = (id: string) => {
    setSpeciesIdToDelete(id);
    setDeleteDialog(true);
  };

  const handleDelete = () => {
    if (speciesIdToDelete) {
      deleteMutation.mutate(speciesIdToDelete);
      setDeleteDialog(false);
    }
  };

  const openEditModal = (species: any) => {
    setSelectedSpecies(species);
    setIsEditModalOpen(true);
  };

  const handleViewBreeds = (speciesId: string) => {
    navigate(`/breeds?speciesId=${speciesId}`);
  };

  // We no longer need handleSearch as DebouncedSearchInput handles this
  // The setSearchTerm is passed directly to the component
  // We'll update the page reset in the onChange handler of DebouncedSearchInput

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Species</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Species
        </Button>
      </div>

      <div className="flex justify-between items-center mb-4">
        <DebouncedSearchInput
          placeholder="Search species by name..."
          value={searchTerm}
          onChange={(value) => {
            setSearchTerm(value);
            setPage(1); // Reset to first page when searching
          }}
          onSearch={refetch}
          className="w-64"
          debounceTime={500}
        />
        <Select
          value={String(limit)}
          onValueChange={handleLimitChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select page size"/>
          </SelectTrigger>
          <SelectContent className="bg-white">
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="15">15 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {speciesData.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">No species found</div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Image</TableHead>
                <TableHead>Species Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="text-right pr-6">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleRefresh}
                    className="ml-auto"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {speciesData.map((species) => (
                <TableRow key={species._id}>
                  <TableCell>
                    {species.imageUrl ? (
                      <div className="h-10 w-10 rounded-md overflow-hidden">
                        <img
                          src={species.imageUrl}
                          alt={species.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-10 w-10 rounded-md bg-gray-100 flex items-center justify-center">
                        <span className="text-xs text-gray-500">No image</span>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="font-medium">{species.name}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {species.description || "No description"}
                  </TableCell>
                  <TableCell>
                    {species.createdAt
                      ? new Date(species.createdAt).toLocaleDateString()
                      : "N/A"}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white">
                        <DropdownMenuItem
                          onClick={() => handleViewBreeds(species._id)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Breeds
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openEditModal(species)}
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => openDeleteDialog(species._id)}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-muted-foreground">
          Showing {speciesData.length > 0 ? ((pagination.page - 1) * limit + 1) : 0} to {Math.min(pagination.page * limit, pagination.totalCount)} of {pagination.totalCount} entries
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handlePreviousPage}
            disabled={pagination.page === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={handleNextPage}
            disabled={pagination.page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      </div>

      <Dialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this species? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              {deleteMutation.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Species Modal */}
      <AddSpeciesModal
        open={isAddModalOpen}
        onOpenChange={setIsAddModalOpen}
      />

      {/* Edit Species Modal */}
      <EditSpeciesModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        species={selectedSpecies}
      />
    </div>
  );
};

export default Species;
