import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Save, Key, Eye, EyeOff, Plus, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface ApiKey {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  key: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
}

interface SystemSettings {
  aiSettings: {
    defaultProvider: string;
    enableAiNotes: boolean;
    maxTokens: number;
    temperature: number;
  };
  notificationSettings: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    appointmentReminders: boolean;
  };
  systemSettings: {
    timezone: string;
    dateFormat: string;
    currency: string;
    language: string;
  };
}

const Settings: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});
  const [isAddingApiKey, setIsAddingApiKey] = useState(false);
  const [editingSettings, setEditingSettings] = useState(false);

  const [newApiKey, setNewApiKey] = useState({
    name: '',
    provider: 'openai' as const,
    key: '',
    description: '',
    isActive: true
  });

  // Fetch API keys
  const { data: apiKeys = [], isLoading: loadingKeys } = useQuery({
    queryKey: ['admin', 'apiKeys'],
    queryFn: async () => {
      const response = await api.get('/admin/api-keys');
      return response.data.data || [];
    }
  });

  // Fetch system settings
  const { data: settings, isLoading: loadingSettings } = useQuery({
    queryKey: ['admin', 'settings'],
    queryFn: async () => {
      const response = await api.get('/admin/settings');
      return response.data.data || {
        aiSettings: {
          defaultProvider: 'openai',
          enableAiNotes: true,
          maxTokens: 1000,
          temperature: 0.7
        },
        notificationSettings: {
          emailNotifications: true,
          smsNotifications: false,
          appointmentReminders: true
        },
        systemSettings: {
          timezone: 'UTC',
          dateFormat: 'MM/DD/YYYY',
          currency: 'KES',
          language: 'en'
        }
      };
    }
  });

  const [localSettings, setLocalSettings] = useState<SystemSettings | null>(null);

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  // Add API key mutation
  const addApiKeyMutation = useMutation({
    mutationFn: async (keyData: typeof newApiKey) => {
      const response = await api.post('/admin/api-keys', keyData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'apiKeys'] });
      setIsAddingApiKey(false);
      setNewApiKey({
        name: '',
        provider: 'openai',
        key: '',
        description: '',
        isActive: true
      });
      toast({
        title: "Success",
        description: "API key added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add API key",
        variant: "destructive",
      });
    }
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (settingsData: SystemSettings) => {
      const response = await api.put('/admin/settings', settingsData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'settings'] });
      setEditingSettings(false);
      toast({
        title: "Success",
        description: "Settings updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update settings",
        variant: "destructive",
      });
    }
  });

  // Delete API key mutation
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (keyId: string) => {
      const response = await api.delete(`/admin/api-keys/${keyId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'apiKeys'] });
      toast({
        title: "Success",
        description: "API key deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete API key",
        variant: "destructive",
      });
    }
  });

  const handleAddApiKey = () => {
    if (!newApiKey.name || !newApiKey.key) {
      toast({
        title: "Error",
        description: "Name and API key are required",
        variant: "destructive",
      });
      return;
    }
    addApiKeyMutation.mutate(newApiKey);
  };

  const handleUpdateSettings = () => {
    if (localSettings) {
      updateSettingsMutation.mutate(localSettings);
    }
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return '*'.repeat(key.length);
    return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
  };

  const getProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case 'openai': return 'bg-green-100 text-green-800';
      case 'anthropic': return 'bg-blue-100 text-blue-800';
      case 'google': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loadingKeys || loadingSettings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Admin Settings</h1>
          <p className="text-gray-600">Manage system settings and API configurations</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Keys Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                API Keys
              </CardTitle>
              <Dialog open={isAddingApiKey} onOpenChange={setIsAddingApiKey}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Key
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add API Key</DialogTitle>
                    <DialogDescription>
                      Add a new API key for AI services integration.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="keyName">Name</Label>
                      <Input
                        id="keyName"
                        value={newApiKey.name}
                        onChange={(e) => setNewApiKey({...newApiKey, name: e.target.value})}
                        placeholder="e.g., OpenAI Production"
                      />
                    </div>
                    <div>
                      <Label htmlFor="provider">Provider</Label>
                      <Select value={newApiKey.provider} onValueChange={(value: any) => setNewApiKey({...newApiKey, provider: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="anthropic">Anthropic</SelectItem>
                          <SelectItem value="google">Google</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="apiKey">API Key</Label>
                      <Input
                        id="apiKey"
                        type="password"
                        value={newApiKey.key}
                        onChange={(e) => setNewApiKey({...newApiKey, key: e.target.value})}
                        placeholder="Enter API key"
                      />
                    </div>
                    <div>
                      <Label htmlFor="description">Description (Optional)</Label>
                      <Textarea
                        id="description"
                        value={newApiKey.description}
                        onChange={(e) => setNewApiKey({...newApiKey, description: e.target.value})}
                        placeholder="Description of this API key"
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsAddingApiKey(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddApiKey} disabled={addApiKeyMutation.isPending}>
                        {addApiKeyMutation.isPending ? 'Adding...' : 'Add Key'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            {apiKeys.length === 0 ? (
              <div className="text-center py-8">
                <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No API keys configured</p>
                <p className="text-sm text-gray-400">Add an API key to enable AI features</p>
              </div>
            ) : (
              <div className="space-y-3">
                {apiKeys.map((apiKey: ApiKey) => (
                  <div key={apiKey.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <h4 className="font-semibold">{apiKey.name}</h4>
                        <Badge className={getProviderBadgeColor(apiKey.provider)}>
                          {apiKey.provider}
                        </Badge>
                        {apiKey.isActive ? (
                          <Badge className="bg-green-100 text-green-800">Active</Badge>
                        ) : (
                          <Badge variant="secondary">Inactive</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleApiKeyVisibility(apiKey.id)}
                        >
                          {showApiKey[apiKey.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteApiKeyMutation.mutate(apiKey.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p className="font-mono">
                        {showApiKey[apiKey.id] ? apiKey.key : maskApiKey(apiKey.key)}
                      </p>
                      {apiKey.description && (
                        <p className="mt-1">{apiKey.description}</p>
                      )}
                      {apiKey.lastUsed && (
                        <p className="mt-1">Last used: {new Date(apiKey.lastUsed).toLocaleDateString()}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* System Settings Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>System Settings</CardTitle>
              <Button
                variant={editingSettings ? "default" : "outline"}
                size="sm"
                onClick={() => editingSettings ? handleUpdateSettings() : setEditingSettings(true)}
              >
                {editingSettings ? (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </>
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {localSettings && (
              <>
                {/* AI Settings */}
                <div>
                  <h4 className="font-semibold mb-3">AI Settings</h4>
                  <div className="space-y-3">
                    <div>
                      <Label>Default AI Provider</Label>
                      <Select
                        value={localSettings.aiSettings.defaultProvider}
                        onValueChange={(value) => setLocalSettings({
                          ...localSettings,
                          aiSettings: { ...localSettings.aiSettings, defaultProvider: value }
                        })}
                        disabled={!editingSettings}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="anthropic">Anthropic</SelectItem>
                          <SelectItem value="google">Google</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Max Tokens</Label>
                        <Input
                          type="number"
                          value={localSettings.aiSettings.maxTokens}
                          onChange={(e) => setLocalSettings({
                            ...localSettings,
                            aiSettings: { ...localSettings.aiSettings, maxTokens: parseInt(e.target.value) }
                          })}
                          disabled={!editingSettings}
                        />
                      </div>
                      <div>
                        <Label>Temperature</Label>
                        <Input
                          type="number"
                          step="0.1"
                          min="0"
                          max="2"
                          value={localSettings.aiSettings.temperature}
                          onChange={(e) => setLocalSettings({
                            ...localSettings,
                            aiSettings: { ...localSettings.aiSettings, temperature: parseFloat(e.target.value) }
                          })}
                          disabled={!editingSettings}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* System Settings */}
                <div>
                  <h4 className="font-semibold mb-3">General Settings</h4>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Timezone</Label>
                        <Select
                          value={localSettings.systemSettings.timezone}
                          onValueChange={(value) => setLocalSettings({
                            ...localSettings,
                            systemSettings: { ...localSettings.systemSettings, timezone: value }
                          })}
                          disabled={!editingSettings}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="UTC">UTC</SelectItem>
                            <SelectItem value="Africa/Nairobi">Africa/Nairobi</SelectItem>
                            <SelectItem value="America/New_York">America/New_York</SelectItem>
                            <SelectItem value="Europe/London">Europe/London</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Currency</Label>
                        <Select
                          value={localSettings.systemSettings.currency}
                          onValueChange={(value) => setLocalSettings({
                            ...localSettings,
                            systemSettings: { ...localSettings.systemSettings, currency: value }
                          })}
                          disabled={!editingSettings}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="KES">KES</SelectItem>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
