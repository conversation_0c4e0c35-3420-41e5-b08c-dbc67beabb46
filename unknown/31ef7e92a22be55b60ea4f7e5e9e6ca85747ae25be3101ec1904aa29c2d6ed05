
import { PawPrint } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="pt-16 pb-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center gap-2 mb-6">
                <PawPrint className="text-primary h-6 w-6" />
                <h2 className="text-2xl font-bold">VetCare</h2>
              </div>
              <p className="text-gray-400 mb-6">
                Modern veterinary practice management software that streamlines operations and enhances patient care.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Products</h3>
              <ul className="space-y-3">
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Practice Management</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Electronic Medical Records</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Client Portal</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Telemedicine</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Analytics</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-3">
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">About Us</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Careers</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Blog</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Press</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Resources</h3>
              <ul className="space-y-3">
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Help Center</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Documentation</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">API</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Community</Link></li>
                <li><Link to="#" className="text-gray-400 hover:text-white transition-colors">Webinars</Link></li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="py-6 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 mb-4 md:mb-0">
            &copy; {currentYear} VetCare. All rights reserved.
          </div>
          <div className="flex gap-6">
            <Link to="#" className="text-gray-400 hover:text-white transition-colors">Terms</Link>
            <Link to="#" className="text-gray-400 hover:text-white transition-colors">Privacy</Link>
            <Link to="#" className="text-gray-400 hover:text-white transition-colors">Cookies</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
