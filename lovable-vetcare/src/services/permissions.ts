
import { Permission, PaginationData } from '@/store/types';
import { api } from './api';

interface PermissionsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Permission[];
    pagination: PaginationData;
  };
}

interface SinglePermissionResponse {
  success: boolean;
  status: number;
  message: string;
  data: Permission | null;
}

export const getPermissions = async (params: { page?: number; limit?: number } = {}): Promise<PermissionsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    return await api.get<PermissionsResponse>(`/permissions?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch permissions',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

export const getPermissionById = async (id: string): Promise<SinglePermissionResponse> => {
  try {
    return await api.get<SinglePermissionResponse>(`/permissions/${id}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch permission',
      data: null
    };
  }
};

export const createPermission = async (permission: Partial<Permission>): Promise<SinglePermissionResponse> => {
  try {
    return await api.post<SinglePermissionResponse>('/permissions', permission);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create permission',
      data: null
    };
  }
};

export const updatePermission = async (id: string, permission: Partial<Permission>): Promise<SinglePermissionResponse> => {
  try {
    return await api.put<SinglePermissionResponse>(`/permissions/${id}`, permission);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update permission',
      data: null
    };
  }
};

export const deletePermission = async (id: string): Promise<SinglePermissionResponse> => {
  try {
    return await api.delete<SinglePermissionResponse>(`/permissions/${id}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete permission',
      data: null
    };
  }
};
