import mongoose from "mongoose";
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const clinicSchema = new mongoose.Schema(
    {
        // Auto-increment ID
        clinicId: {
            type: Number,
            index: true,
            unique: true,
        },
        clinicName: {
            type: String,
            required: [true, "Clinic name is required"],
            trim: true,
            maxLength: 100,
        },
        owner: {
            type: Number,
            ref: "Staff",
            // Make it conditionally required - not required during initial creation
            required: [function() {
                // Skip validation during initial creation in the signup process
                return !this.isNew;
            }, "Owner is required"],
            index: true,
        },
        managerId: {
            type: Number,
            ref: "Staff",
            index: true,
        },
        phoneNumber: {
            type: String,
            required: [true, "Phone number is required"],
            trim: true,
            minLength: 10,
            maxLength: 14,
        },
        email: {
            type: String,
            required: [true, "Email is required"],
            unique: true,
            lowercase: true,
            trim: true,
            match: [/\S+@\S+\.\S+/, "Please enter a valid email address"],
        },
        address: {
            type: String,
            required: [true, "Address is required"],
            trim: true,
            maxLength: 255,
        },
        // Added fields
        status: {
            type: Number,
            enum: [0, 1], // 0: Inactive, 1: Active
            default: 1
        },
        location: {
            type: {
                type: String,
                enum: ['Point'],
                default: 'Point'
            },
            coordinates: {
                type: [Number],
                default: [0, 0]
            }
        },
        operatingHours: {
            monday: { open: String, close: String },
            tuesday: { open: String, close: String },
            wednesday: { open: String, close: String },
            thursday: { open: String, close: String },
            friday: { open: String, close: String },
            saturday: { open: String, close: String },
            sunday: { open: String, close: String }
        },
        description: {
            type: String,
            trim: true
        },
        website: {
            type: String,
            trim: true
        },
        logo: {
            type: String,
            trim: true
        }
    },
    { timestamps: true }
);

// Add geospatial index for location-based queries
clinicSchema.index({ location: '2dsphere' });
clinicSchema.index({ status: 1 });
clinicSchema.index({ owner: 1 });

// Add auto-increment plugin
clinicSchema.plugin(AutoIncrement, { inc_field: 'clinicId', start_seq: 1001 });
clinicSchema.index({ clinicId: 1 }, { unique: true });

const Clinic = mongoose.model("Clinic", clinicSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Clinic.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Clinic.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Clinic;