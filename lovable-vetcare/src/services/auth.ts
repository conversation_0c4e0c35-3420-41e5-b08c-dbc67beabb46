import axios from "axios";


interface Clinic {
    clinicName: string;
    phoneNumber: string;
    email: string;
    address: string;
  }
  
  interface Employee {
    _id: string;
    userId: string;
    clinicId: Clinic;
    roleId: number;
    jobTitle: string;
    employmentDate: string;
    salary: number;
    isClinicOwner: boolean;
    status: number;
    createdAt: string;
    updatedAt: string;
  }
  
  interface User {
    _id: string;
    firstName: string;
    middleName?: string;
    lastName: string;
    email: string;
    phoneNumber: string;
  }
  
  interface AuthResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
      token: string;
      user: User;
      employee?: Employee;
    };
  }
  

interface LoginCredentials {
    email: string;
    password: string;
}

export const signupUser = async (payload: {
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  roleId: number;
  is_clinic_owner: boolean;
  clinic_data: {
    name: string;
    phoneNumber: string;
    email: string;
    address: string;
  };
}): Promise<AuthResponse> => {
  try {
    const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/auth/sign-up`, payload);
    return response.data;
  } catch (error) {
    console.log("Signup error: ", error);
    throw error;
  }
};

export const loginUser = async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
        const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/auth/sign-in`, credentials);
        return response.data;
    } catch (error) {
        console.log("Login error: ", error);
        throw error;
    }
};
