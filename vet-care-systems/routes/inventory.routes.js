import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createInventoryItem,
    getAllInventoryItems,
    getInventoryItemById,
    updateInventoryItem,
    deleteInventoryItem,
    adjustInventory
} from "../controllers/inventory.controller.js";

const inventoryRouter = Router();

// Apply authorization middleware to all routes
inventoryRouter.use(verifyToken);

// Inventory CRUD routes
inventoryRouter.post('/', createInventoryItem);
inventoryRouter.get('/', getAllInventoryItems);
inventoryRouter.get('/:itemId', getInventoryItemById);
inventoryRouter.put('/:itemId', updateInventoryItem);
inventoryRouter.delete('/:itemId', deleteInventoryItem);

// Inventory adjustment route
inventoryRouter.post('/:itemId/adjust', adjustInventory);

export default inventoryRouter;
