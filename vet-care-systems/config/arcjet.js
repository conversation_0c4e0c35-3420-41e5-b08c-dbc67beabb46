import arcjet, { shield, detectBot, tokenBucket } from "@arcjet/node";
import { ARCJET_KEY, ARCJET_ENV } from "./env.js";

/**
 * Arcjet Configuration
 *
 * This configures Arcjet for rate limiting, bot detection, and security.
 * See https://docs.arcjet.com for more details.
 */
const aj = arcjet({
    key: ARCJET_KEY,
    characteristics: ["ip.src"], // Track requests by IP
    env: ARCJET_ENV || 'development',
    rules: [
        // Shield protects your app from common attacks e.g. SQL injection
        shield({
            mode: "LIVE"
        }),

        // Bot detection - block malicious bots
        detectBot({
            mode: "LIVE", // Blocks requests. Use "DRY_RUN" to log only
            // Block all bots except the following
            allow: [
                "CATEGORY:SEARCH_ENGINE", // Google, Bing, etc
                "CATEGORY:MONITOR",       // Uptime monitoring services
                "CATEGORY:PREVIEW",       // Link previews e.g. Slack, Discord
            ],
        }),

        // Global rate limiting - 100 requests per minute per IP
        tokenBucket({
            mode: "LIVE",
            refillRate: 100,  // 100 requests per minute
            interval: "60s",  // 60 seconds
            capacity: 100,    // Maximum burst capacity
        }),

        // Stricter rate limiting for authentication endpoints
        tokenBucket({
            mode: "LIVE",
            refillRate: 10,   // 10 requests per minute
            interval: "60s",  // 60 seconds
            capacity: 10,     // Maximum burst capacity
            match: {
                url: [
                    "/api/v1/auth/sign-in",
                    "/api/v1/auth/sign-up",
                    "/api/v1/auth/forgot-password",
                    "/api/v1/auth/reset-password"
                ]
            }
        }),
        // Create a token bucket rate limit. Other algorithms are supported.
        tokenBucket({
            mode: "LIVE",
            refillRate: 5, // Refill 5 tokens per interval
            interval: 10, // Refill every 10 seconds
            capacity: 10, // Bucket capacity of 10 tokens
        }),
    ],
});

export default aj;