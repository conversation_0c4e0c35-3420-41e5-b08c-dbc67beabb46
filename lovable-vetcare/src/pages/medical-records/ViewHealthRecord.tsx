import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import {
  ArrowLeft,
  Edit,
  Pill,
  FileText,
  Calendar,
  Printer,
  Share2,
  Clipboard,
  Activity,
  Stethoscope,
  User,
  Building,
  Clock,
  AlertCircle,
  CheckCircle2,
  Loader2
} from "lucide-react";
import { getHealthRecordById } from "@/services/healthRecord";
import { getMedicationDispensings } from "@/services/medicationDispensing";
import DispenseMedicationModal from "./components/DispenseMedicationModal";
import LottieAnimation from "@/pages/landing/components/LottieAnimation";
import { cn } from "@/lib/utils";

const ViewHealthRecord = () => {
  const { recordId } = useParams<{ recordId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDispensingModalOpen, setIsDispensingModalOpen] = useState(false);

  // Fetch health record
  const { data: recordData, isLoading: isRecordLoading } = useQuery({
    queryKey: ['healthRecord', recordId],
    queryFn: () => getHealthRecordById(recordId || ''),
    enabled: !!recordId,
  });

  const healthRecord = recordData?.data;

  // Fetch dispensed medications for this health record
  const { data: medicationsData, isLoading: isMedicationsLoading } = useQuery({
    queryKey: ['medicationDispensings', recordId],
    queryFn: () => getMedicationDispensings({ healthRecordId: recordId }),
    enabled: !!recordId,
  });

  const dispensedMedications = medicationsData?.data?.data || [];

  if (isRecordLoading) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex flex-col justify-center items-center h-64">
          <Loader2 className="h-10 w-10 text-primary animate-spin mb-4" />
          <p className="text-muted-foreground">Loading health record...</p>
        </div>
      </div>
    );
  }

  if (!healthRecord) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex flex-col justify-center items-center h-64">
          <AlertCircle className="h-10 w-10 text-destructive mb-4" />
          <p className="text-muted-foreground">Health record not found</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const getRecordTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      consultation: "bg-blue-100 text-blue-800",
      vaccination: "bg-green-100 text-green-800",
      surgery: "bg-red-100 text-red-800",
      laboratory: "bg-purple-100 text-purple-800",
      imaging: "bg-yellow-100 text-yellow-800",
      other: "bg-gray-100 text-gray-800"
    };

    return (
      <Badge className={colors[type.toLowerCase()] || colors.other}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => navigate(-1)} className="h-10 w-10">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">Health Record</h1>
              {getRecordTypeBadge(healthRecord.recordType)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Created on {format(new Date(healthRecord.createdAt), 'PPP')}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="h-9">
            <Printer className="h-4 w-4 mr-2" /> Print
          </Button>
          <Button variant="outline" size="sm" className="h-9">
            <Share2 className="h-4 w-4 mr-2" /> Share
          </Button>
          <Button variant="default" size="sm" className="h-9">
            <Edit className="h-4 w-4 mr-2" /> Edit
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="overflow-hidden border-l-4 border-l-primary">
          <CardHeader className="pb-2 bg-muted/30 flex flex-row items-center gap-2">
            <Stethoscope className="h-4 w-4 text-primary" />
            <CardTitle className="text-sm font-medium">Pet Information</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <Stethoscope className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="text-lg font-semibold">{healthRecord.petId?.name || 'Unknown'}</div>
                <div className="text-sm text-muted-foreground">
                  {healthRecord.petId?.species?.name || 'Unknown'} - {healthRecord.petId?.breed?.name || 'Unknown'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-l-4 border-l-blue-500">
          <CardHeader className="pb-2 bg-muted/30 flex flex-row items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-500" />
            <CardTitle className="text-sm font-medium">Date & Time</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Calendar className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <div className="text-lg font-semibold">
                  {healthRecord.date ? format(new Date(healthRecord.date), 'PPP') : 'Unknown'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {healthRecord.date ? format(new Date(healthRecord.date), 'p') : ''}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-l-4 border-l-green-500">
          <CardHeader className="pb-2 bg-muted/30 flex flex-row items-center gap-2">
            <User className="h-4 w-4 text-green-500" />
            <CardTitle className="text-sm font-medium">Performed By</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <User className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <div className="text-lg font-semibold">
                  {healthRecord.performedBy ?
                    `${healthRecord.performedBy.firstName} ${healthRecord.performedBy.lastName}` :
                    'Unknown'}
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  {healthRecord.clinicId?.clinicName || 'Unknown Clinic'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="details">
            <FileText className="h-4 w-4 mr-2" /> Details
          </TabsTrigger>
          <TabsTrigger value="medications">
            <Pill className="h-4 w-4 mr-2" /> Medications
          </TabsTrigger>
          <TabsTrigger value="vitals">
            <Activity className="h-4 w-4 mr-2" /> Vital Signs
          </TabsTrigger>
          <TabsTrigger value="followup">
            <Calendar className="h-4 w-4 mr-2" /> Follow-up
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Medical Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 relative overflow-hidden">
              {/* Background Lottie animation for Diagnostics */}
              <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
                <LottieAnimation
                  animationPath="/animation/lottie/stethoscope.json"
                  className="w-full h-full"
                  speed={0.5}
                />
              </div>

              {healthRecord.description && (
                <div className="relative z-10">
                  <h3 className="font-semibold mb-1">Description</h3>
                  <p>{healthRecord.description}</p>
                </div>
              )}

              {healthRecord.diagnosis && (
                <div className="relative z-10">
                  <h3 className="font-semibold mb-1">Diagnosis</h3>
                  <p>{healthRecord.diagnosis}</p>
                </div>
              )}

              {healthRecord.treatment && (
                <div className="relative z-10">
                  <h3 className="font-semibold mb-1">Treatment</h3>
                  <p>{healthRecord.treatment}</p>
                </div>
              )}

              {healthRecord.notes && (
                <div className="relative z-10">
                  <h3 className="font-semibold mb-1">Notes</h3>
                  <p>{healthRecord.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medications">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Medications</CardTitle>
                <CardDescription>Medications prescribed and dispensed</CardDescription>
              </div>
              <Button onClick={() => setIsDispensingModalOpen(true)}>
                <Pill className="h-4 w-4 mr-2" /> Dispense Medication
              </Button>
            </CardHeader>
            <CardContent className="relative overflow-hidden">
              {/* Background Lottie animation for Medication */}
              <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
                <LottieAnimation
                  animationPath="/animation/lottie/pill.json"
                  className="w-full h-full"
                  speed={0.5}
                />
              </div>

              {healthRecord.medications && healthRecord.medications.length > 0 ? (
                <div className="space-y-4 relative z-10">
                  <h3 className="font-semibold">Prescribed Medications</h3>
                  <div className="border rounded-md">
                    <table className="w-full">
                      <thead className="bg-muted">
                        <tr>
                          <th className="text-left p-2">Medication</th>
                          <th className="text-left p-2">Dosage</th>
                          <th className="text-left p-2">Frequency</th>
                          <th className="text-left p-2">Duration</th>
                          <th className="text-left p-2">Notes</th>
                        </tr>
                      </thead>
                      <tbody>
                        {healthRecord.medications.map((med, index) => (
                          <tr key={index} className="border-t">
                            <td className="p-2">{med.name}</td>
                            <td className="p-2">{med.dosage}</td>
                            <td className="p-2">{med.frequency}</td>
                            <td className="p-2">{med.duration || 'N/A'}</td>
                            <td className="p-2">{med.notes || 'N/A'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground relative z-10">No medications prescribed</p>
              )}

              {dispensedMedications.length > 0 && (
                <div className="space-y-4 mt-6 relative z-10">
                  <h3 className="font-semibold">Dispensed Medications</h3>
                  <div className="border rounded-md">
                    <table className="w-full">
                      <thead className="bg-muted">
                        <tr>
                          <th className="text-left p-2">Medication</th>
                          <th className="text-left p-2">Quantity</th>
                          <th className="text-left p-2">Dosage</th>
                          <th className="text-left p-2">Dispensed By</th>
                          <th className="text-left p-2">Date</th>
                          <th className="text-left p-2">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dispensedMedications.map((med) => (
                          <tr key={med._id} className="border-t">
                            <td className="p-2">{med.inventoryItemId?.name || 'Unknown'}</td>
                            <td className="p-2">{med.quantity} {med.unit}</td>
                            <td className="p-2">{med.dosage}, {med.frequency}</td>
                            <td className="p-2">
                              {med.dispensedBy ?
                                `${med.dispensedBy.firstName} ${med.dispensedBy.lastName}` :
                                'Unknown'}
                            </td>
                            <td className="p-2">
                              {med.dispensedDate ? format(new Date(med.dispensedDate), 'PPP') : 'Unknown'}
                            </td>
                            <td className="p-2">
                              <Badge className={
                                med.status === 'dispensed' ? 'bg-green-100 text-green-800' :
                                med.status === 'returned' ? 'bg-amber-100 text-amber-800' :
                                'bg-red-100 text-red-800'
                              }>
                                {med.status.charAt(0).toUpperCase() + med.status.slice(1)}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              <div className="mt-4 relative z-10">
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" /> Schedule Follow-up
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vitals">
          <Card>
            <CardHeader>
              <CardTitle>Vital Signs</CardTitle>
            </CardHeader>
            <CardContent className="relative overflow-hidden">
              {/* Background Lottie animation for Treatment */}
              <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
                <LottieAnimation
                  animationPath="/animation/lottie/stethoscope.json"
                  className="w-full h-full"
                  speed={0.5}
                />
              </div>

              {healthRecord.vitalSigns && Object.keys(healthRecord.vitalSigns).length > 0 ? (
                <div className="grid grid-cols-2 gap-4 relative z-10">
                  {Object.entries(healthRecord.vitalSigns).map(([key, value]) => (
                    <div key={key} className="border rounded-md p-4">
                      <h3 className="font-semibold capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h3>
                      <p className="text-lg">{value}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground relative z-10">No vital signs recorded</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="followup">
          <Card>
            <CardHeader>
              <CardTitle>Follow-up Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {healthRecord.followUpDate && (
                <div>
                  <h3 className="font-semibold mb-1">Follow-up Date</h3>
                  <p>{format(new Date(healthRecord.followUpDate), 'PPP')}</p>
                </div>
              )}

              {healthRecord.followUpInstructions && (
                <div>
                  <h3 className="font-semibold mb-1">Instructions</h3>
                  <p>{healthRecord.followUpInstructions}</p>
                </div>
              )}

              {(!healthRecord.followUpDate && !healthRecord.followUpInstructions) && (
                <p className="text-muted-foreground">No follow-up information</p>
              )}

              <div className="mt-4">
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" /> Schedule Follow-up
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Medication Dispensing Modal */}
      <DispenseMedicationModal
        open={isDispensingModalOpen}
        onOpenChange={setIsDispensingModalOpen}
        healthRecordId={recordId || ''}
        petId={healthRecord.petId?._id || ''}
        clinicId={healthRecord.clinicId?._id || ''}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['medicationDispensings', recordId] });
          toast({
            title: "Success",
            description: "Medication dispensed successfully",
          });
        }}
      />
    </div>
  );
};

export default ViewHealthRecord;
