import { config } from "dotenv";

// Load environment variables from the appropriate .env file
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

/**
 * Required environment variables
 * Add new required variables to this array
 */
const requiredEnvVars = [
    'PORT',
    'DB_URI',
    'JWT_SECRET',
    'JWT_EXPIRES_IN'
];

/**
 * Validate that all required environment variables are defined
 */
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

/**
 * Environment variables with default values
 */
const NODE_ENV = process.env.NODE_ENV || 'development';
const PORT = parseInt(process.env.PORT, 10) || 3000;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';

/**
 * Extract environment variables
 */
const DB_URI = process.env.DB_URI;
const JWT_SECRET = process.env.JWT_SECRET;
const ARCJET_KEY = process.env.ARCJET_KEY;
const ARCJET_ENV = process.env.ARCJET_ENV;
const QSTASH_URL = process.env.QSTASH_URL;
const QSTASH_TOKEN = process.env.QSTASH_TOKEN;

/**
 * Export all environment variables
 */
export {
    // Server configuration
    PORT,
    NODE_ENV,

    // Database configuration
    DB_URI,

    // Authentication configuration
    JWT_SECRET,
    JWT_EXPIRES_IN,

    // Security configuration (optional)
    ARCJET_KEY,
    ARCJET_ENV,

    // Queue configuration (optional)
    QSTASH_URL,
    QSTASH_TOKEN,
};
