import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { 
  ArrowLeft, 
  Download, 
  Receipt, 
  Calendar,
  User,
  PawPrint,
  Building,
  CreditCard,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getReceiptById } from '@/services/receipts';

const ReceiptDetails = () => {
  const { receiptId } = useParams();
  const navigate = useNavigate();

  // Fetch receipt details
  const { data: receipt, isLoading, error } = useQuery({
    queryKey: ['receipt', receiptId],
    queryFn: () => getReceiptById(receiptId!),
    enabled: !!receiptId
  });

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !receipt) {
    return (
      <div className="p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Receipt Not Found</h2>
          <p className="text-gray-600 mb-4">Unable to load receipt details.</p>
          <Button onClick={() => navigate('/appointments')}>
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  const receiptData = receipt.data;

  return (
    <div className="p-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/appointments/${receiptData.appointmentId}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Appointment
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Receipt #{receiptData.receiptNumber}</h1>
            <p className="text-gray-600">
              Issued on {format(new Date(receiptData.issuedDate), 'PPP')}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Paid
          </Badge>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
        </div>
      </div>

      {/* Receipt Content */}
      <div className="bg-white border rounded-lg p-8 shadow-sm">
        {/* Clinic Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">VetCare Clinic</h2>
          <p className="text-gray-600">Professional Veterinary Services</p>
          <p className="text-sm text-gray-500">
            123 Main Street, Nairobi, Kenya | Phone: +254 700 000 000
          </p>
        </div>

        <Separator className="mb-6" />

        {/* Receipt Details */}
        <div className="grid grid-cols-2 gap-8 mb-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Bill To:</h3>
            <div className="space-y-1 text-sm">
              <p className="flex items-center gap-2">
                <User className="h-4 w-4" />
                {receiptData.clientData?.firstName} {receiptData.clientData?.lastName}
              </p>
              <p className="flex items-center gap-2">
                <PawPrint className="h-4 w-4" />
                {receiptData.petData?.petName || receiptData.petData?.name}
              </p>
              {receiptData.clientData?.email && (
                <p className="text-gray-600">{receiptData.clientData.email}</p>
              )}
              {receiptData.clientData?.phoneNumber && (
                <p className="text-gray-600">{receiptData.clientData.phoneNumber}</p>
              )}
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Receipt Details:</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Receipt Number:</span>
                <span className="font-medium">{receiptData.receiptNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Issue Date:</span>
                <span>{format(new Date(receiptData.issuedDate), 'PP')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Date:</span>
                <span>{format(new Date(receiptData.paymentDate), 'PP')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Method:</span>
                <span className="capitalize">{receiptData.paymentMethod.replace('_', ' ')}</span>
              </div>
              {receiptData.paymentReference && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Ref:</span>
                  <span className="font-mono text-xs">{receiptData.paymentReference}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Services Table */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-900 mb-3">Services Provided:</h3>
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Service</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-900">Qty</th>
                  <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Unit Price</th>
                  <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Total</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {receiptData.services?.map((service: any, index: number) => (
                  <tr key={index}>
                    <td className="px-4 py-3">
                      <div>
                        <p className="font-medium text-gray-900">{service.serviceName}</p>
                        {service.description && (
                          <p className="text-sm text-gray-600">{service.description}</p>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center">{service.quantity}</td>
                    <td className="px-4 py-3 text-right">
                      {receiptData.currency} {service.unitPrice.toLocaleString()}
                    </td>
                    <td className="px-4 py-3 text-right font-medium">
                      {receiptData.currency} {service.totalPrice.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Payment Summary */}
        <div className="flex justify-end">
          <div className="w-80">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{receiptData.currency} {receiptData.subtotal.toLocaleString()}</span>
              </div>
              
              {receiptData.afterHoursCharge > 0 && (
                <div className="flex justify-between">
                  <span>After Hours Charge:</span>
                  <span>{receiptData.currency} {receiptData.afterHoursCharge.toLocaleString()}</span>
                </div>
              )}
              
              {receiptData.totalDiscounts > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Total Discounts:</span>
                  <span>-{receiptData.currency} {receiptData.totalDiscounts.toLocaleString()}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>Tax ({receiptData.taxRate}%):</span>
                <span>{receiptData.currency} {receiptData.taxAmount.toLocaleString()}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-semibold text-lg">
                <span>Total Paid:</span>
                <span>{receiptData.currency} {receiptData.amountPaid.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-6 border-t text-center text-sm text-gray-600">
          <p>Thank you for choosing VetCare Clinic!</p>
          <p>For any questions about this receipt, please contact <NAME_EMAIL></p>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 flex justify-center gap-4">
        <Button variant="outline" onClick={() => window.print()}>
          <Receipt className="h-4 w-4 mr-2" />
          Print Receipt
        </Button>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Email Receipt
        </Button>
      </div>
    </div>
  );
};

export default ReceiptDetails;
