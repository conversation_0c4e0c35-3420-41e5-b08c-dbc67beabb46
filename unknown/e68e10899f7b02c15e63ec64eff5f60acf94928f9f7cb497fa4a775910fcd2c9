
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { updatePermission, getPermissions } from "@/services/permissions";
import { useToast } from "@/components/ui/use-toast";
import { useQueryClient, useQuery, useMutation } from "@tanstack/react-query";
import LoadingPage from "@/components/common/LoadingPage";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  module: z.string().min(1, "Resource is required")
});

type FormValues = z.infer<typeof formSchema>;

const EditPermission = () => {
  const { _id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: permissionData, isLoading, error } = useQuery({
    queryKey: ["permission", _id],
    queryFn: () => {
      if (!_id) throw new Error("Permission ID is required");
      return getPermissions(_id);
    },
    enabled: !!_id,
    retry: false
  });
  
  // Ensure we have the correct data shape
  const permission = permissionData?.data;
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      module: ""
    },
    values: permission ? {
      name: permission?.data[0]?.name || "",
      description: permission?.data[0]?.description || "",
      module: permission?.data[0]?.module || ""
    } : undefined
  });
  
  const { isPending, mutate } = useMutation({
    mutationFn: (values: FormValues) => {
      if (!_id) throw new Error("Permission ID is required");
      return updatePermission(_id, values);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ["permissions"] });
      toast({
        title: "Success",
        description: "Permission updated successfully",
      });
      navigate("/admin/permissions");
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update permission",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    if (!_id) return;
    mutate(values);
  };

  if (isLoading) return <LoadingPage />;
  
  if (error || !permission) {
    toast({
      title: "Error",
      description: "Failed to load permission information",
      variant: "destructive",
    });
    return null;
  }

  return (
    <div className="p-8">
      <Card>
        <CardHeader>
          <CardTitle>Edit Permission</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Permission Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter permission name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter permission description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="module"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Resource</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter resource name (e.g., users, clinics)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => navigate("/admin/permissions")}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Updating...
                    </>
                  ) : (
                    "Update Permission"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditPermission;
