import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * MedicationDispensing Model
 *
 * This model represents medication dispensing records in the veterinary clinic.
 * It links health records to inventory items and tracks medication dispensing.
 */
const medicationDispensingSchema = new mongoose.Schema({
    medicationDispensingId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Core identifiers
    healthRecordId: {
        type: Number,
        required: true,
        index: true
    },
    inventoryItemId: {
        type: Number,
        required: true,
        index: true
    },

    // Pet and clinic information
    petId: {
        type: Number,
        required: true,
        index: true
    },
    clinicId: {
        type: Number,
        required: true,
        index: true
    },

    // Dispensing details
    dispensedBy: {
        type: Number,
        ref: 'Staff',
        required: true,
        index: true
    },
    dispensedDate: {
        type: Date,
        default: Date.now,
        required: true
    },

    // Medication details
    quantity: {
        type: Number,
        required: true,
        min: 0
    },
    unit: {
        type: String,
        required: true
    },
    dosage: {
        type: String,
        required: true
    },
    frequency: {
        type: String,
        required: true
    },
    duration: {
        type: String
    },

    // Pricing
    price: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        required: true,
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },

    // Batch information
    batchNumber: {
        type: String
    },
    expiryDate: {
        type: Date
    },

    // Additional information
    instructions: {
        type: String
    },
    notes: {
        type: String
    },

    // Billing information
    invoiceId: {
        type: Number,
        ref: 'Invoice',
        index: true
    },

    // Status
    status: {
        type: String,
        enum: ['dispensed', 'cancelled', 'returned'],
        default: 'dispensed'
    }
}, { timestamps: true });

// Add indexes for better query performance
medicationDispensingSchema.index({ healthRecordId: 1, inventoryItemId: 1 });
medicationDispensingSchema.index({ petId: 1, dispensedDate: -1 });
medicationDispensingSchema.index({ clinicId: 1, dispensedDate: -1 });
medicationDispensingSchema.index({ dispensedBy: 1 });
medicationDispensingSchema.index({ status: 1 });

// Add auto-increment plugin
medicationDispensingSchema.plugin(AutoIncrement, { inc_field: 'medicationDispensingId', start_seq: 1001 });
medicationDispensingSchema.index({ medicationDispensingId: 1 }, { unique: true });

const MedicationDispensing = mongoose.model('MedicationDispensing', medicationDispensingSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = MedicationDispensing.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await MedicationDispensing.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default MedicationDispensing;
