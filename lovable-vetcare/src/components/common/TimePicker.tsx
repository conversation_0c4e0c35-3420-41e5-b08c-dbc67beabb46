import * as React from "react"
import { Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"

interface TimePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName
  label?: string
  description?: string
  placeholder?: string
  form: UseFormReturn<TFieldValues>
  required?: boolean
  disabled?: boolean
  className?: string
  interval?: number // Time interval in minutes
  format?: "12h" | "24h"
  startTime?: string // Format: "HH:MM" (24h)
  endTime?: string // Format: "HH:MM" (24h)
  disabledTimes?: string[] // Format: "HH:MM" (24h)
  showValidation?: boolean
}

export function TimePicker<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  placeholder = "Select time",
  form,
  required,
  disabled,
  className,
  interval = 15,
  format = "24h",
  startTime = "00:00",
  endTime = "23:59",
  disabledTimes,
  showValidation = true,
}: TimePickerProps<TFieldValues, TName>) {
  const { formState } = form
  const error = formState.errors[name]
  const isValid = formState.touchedFields[name] && !error

  // Generate time options
  const timeOptions = React.useMemo(() => {
    const options: string[] = []
    
    // Parse start and end times
    const [startHour, startMinute] = startTime.split(":").map(Number)
    const [endHour, endMinute] = endTime.split(":").map(Number)
    
    // Convert to minutes for easier calculation
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute
    
    // Generate time options at specified intervals
    for (let minutes = startMinutes; minutes <= endMinutes; minutes += interval) {
      const hour = Math.floor(minutes / 60)
      const minute = minutes % 60
      
      // Format time based on 12h or 24h format
      if (format === "12h") {
        const period = hour >= 12 ? "PM" : "AM"
        const hour12 = hour % 12 || 12
        options.push(`${hour12.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")} ${period}`)
      } else {
        options.push(`${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`)
      }
    }
    
    return options
  }, [interval, startTime, endTime, format])

  // Format time for display
  const formatTimeForDisplay = (time: string) => {
    if (!time) return ""
    
    // If already in the correct format, return as is
    if (format === "24h" && /^\d{2}:\d{2}$/.test(time)) {
      return time
    }
    if (format === "12h" && /^\d{1,2}:\d{2} (AM|PM)$/.test(time)) {
      return time
    }
    
    // Parse time
    const [hours, minutes] = time.split(":").map(Number)
    
    // Format based on 12h or 24h
    if (format === "12h") {
      const period = hours >= 12 ? "PM" : "AM"
      const hour12 = hours % 12 || 12
      return `${hour12}:${minutes.toString().padStart(2, "0")} ${period}`
    } else {
      return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`
    }
  }

  // Check if a time is disabled
  const isTimeDisabled = (time: string) => {
    if (!disabledTimes) return false
    
    // Convert to 24h format for comparison
    let time24h = time
    if (format === "12h") {
      const [timePart, period] = time.split(" ")
      const [hours, minutes] = timePart.split(":").map(Number)
      const isPM = period === "PM"
      
      const hours24 = isPM ? (hours === 12 ? 12 : hours + 12) : (hours === 12 ? 0 : hours)
      time24h = `${hours24.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`
    }
    
    return disabledTimes.includes(time24h)
  }

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground",
                    error && "border-destructive ring-destructive",
                    isValid && showValidation && "border-green-500 ring-green-500",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={disabled}
                >
                  <Clock className="mr-2 h-4 w-4" />
                  {field.value ? (
                    formatTimeForDisplay(field.value)
                  ) : (
                    <span>{placeholder}</span>
                  )}
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <ScrollArea className="h-72">
                <div className="p-1">
                  {timeOptions.map((time) => (
                    <Button
                      key={time}
                      variant={field.value === time ? "default" : "ghost"}
                      className={cn(
                        "w-full justify-start font-normal",
                        isTimeDisabled(time) && "opacity-50 cursor-not-allowed"
                      )}
                      disabled={isTimeDisabled(time) || disabled}
                      onClick={() => {
                        field.onChange(time)
                      }}
                    >
                      {time}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

interface DateTimePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TDateName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TTimeName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  dateName: TDateName
  timeName: TTimeName
  label?: string
  description?: string
  datePlaceholder?: string
  timePlaceholder?: string
  form: UseFormReturn<TFieldValues>
  required?: boolean
  disabled?: boolean
  className?: string
  timeInterval?: number
  timeFormat?: "12h" | "24h"
  disabledDates?: Date[] | ((date: Date) => boolean)
  disabledTimes?: string[]
  showValidation?: boolean
}

export function DateTimePicker<
  TFieldValues extends FieldValues = FieldValues,
  TDateName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TTimeName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  dateName,
  timeName,
  label,
  description,
  datePlaceholder = "Select date",
  timePlaceholder = "Select time",
  form,
  required,
  disabled,
  className,
  timeInterval = 15,
  timeFormat = "24h",
  disabledDates,
  disabledTimes,
  showValidation = true,
}: DateTimePickerProps<TFieldValues, TDateName, TTimeName>) {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <FormLabel className="flex items-center gap-1">
          {label}
          {required && <span className="text-destructive">*</span>}
        </FormLabel>
      )}
      <div className="flex flex-col sm:flex-row gap-2">
        <DatePicker
          name={dateName}
          placeholder={datePlaceholder}
          form={form}
          disabled={disabled}
          disabledDates={disabledDates}
          showValidation={showValidation}
          className="flex-1"
        />
        <TimePicker
          name={timeName}
          placeholder={timePlaceholder}
          form={form}
          disabled={disabled}
          interval={timeInterval}
          format={timeFormat}
          disabledTimes={disabledTimes}
          showValidation={showValidation}
          className="flex-1"
        />
      </div>
      {description && <FormDescription>{description}</FormDescription>}
    </div>
  )
}

// Import DatePicker for DateTimePicker component
import { DatePicker } from "./DatePicker"
