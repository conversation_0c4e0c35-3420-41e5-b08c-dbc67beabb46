import { PaginationData } from '@/store/types';
import { api } from './api';

export interface MedicationDispensing {
  medicationDispensingId: number;
  healthRecordId: number;
  inventoryItemId: number;
  petId: number;
  clinicId: number;
  dispensedBy: number;
  dispensedDate: Date;
  quantity: number;
  unit: string;
  dosage: string;
  frequency: string;
  duration?: string;
  price: number;
  currency: string;
  batchNumber?: string;
  expiryDate?: Date;
  instructions?: string;
  notes?: string;
  invoiceId?: number;
  status: 'dispensed' | 'cancelled' | 'returned';
  createdAt: Date;
  updatedAt: Date;
}

export interface MedicationDispensingResponse {
  success: boolean;
  status: number;
  message: string;
  data: MedicationDispensing | null;
}

export interface MedicationDispensingsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: MedicationDispensing[];
    pagination: PaginationData;
  };
}

export interface MedicationDispensingSearchParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  petId?: string;
  clinicId?: string;
  healthRecordId?: string;
  inventoryItemId?: string;
  dispensedBy?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  [key: string]: any;
}

export interface MedicationDispensingCreateParams {
  healthRecordId: string;
  inventoryItemId: string;
  petId: string;
  clinicId: string;
  quantity: number;
  unit: string;
  dosage: string;
  frequency: string;
  duration?: string;
  price: number;
  currency?: string;
  batchNumber?: string;
  expiryDate?: Date;
  instructions?: string;
  notes?: string;
}

export interface MedicationDispensingUpdateParams {
  status?: 'dispensed' | 'cancelled' | 'returned';
  notes?: string;
}

/**
 * Get medication dispensing records with pagination and filtering
 */
export const getMedicationDispensings = async (params: MedicationDispensingSearchParams = {}): Promise<MedicationDispensingsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<MedicationDispensingsResponse>(`/medication-dispensing?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch medication dispensing records',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get a specific medication dispensing record by ID
 */
export const getMedicationDispensingById = async (dispensingId: string): Promise<MedicationDispensingResponse> => {
  try {
    return await api.get<MedicationDispensingResponse>(`/medication-dispensing/${dispensingId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch medication dispensing record',
      data: null
    };
  }
};

/**
 * Create a new medication dispensing record
 */
export const dispenseMedication = async (data: MedicationDispensingCreateParams): Promise<MedicationDispensingResponse> => {
  try {
    return await api.post<MedicationDispensingResponse>('/medication-dispensing', data);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to dispense medication',
      data: null
    };
  }
};

/**
 * Update a medication dispensing record
 */
export const updateMedicationDispensing = async (
  dispensingId: string,
  data: MedicationDispensingUpdateParams
): Promise<MedicationDispensingResponse> => {
  try {
    return await api.put<MedicationDispensingResponse>(`/medication-dispensing/${dispensingId}`, data);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update medication dispensing record',
      data: null
    };
  }
};
