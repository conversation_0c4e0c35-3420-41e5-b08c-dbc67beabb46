import express from 'express';
import {
    addAppointmentService,
    getAppointmentServices,
    updateAppointmentService,
    deleteAppointmentService,
    updateServiceStatus,
    getServicesByCategory,
    updateServiceNotes
} from '../controllers/appointmentService.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Add a service to an appointment
router.post('/appointment/:appointmentId/services', addAppointmentService);

// Get all services for an appointment
router.get('/appointment/:appointmentId/services', getAppointmentServices);

// Get services by category for an appointment
router.get('/appointment/:appointmentId/services/category/:category', getServicesByCategory);

// Update a specific service
router.put('/services/:serviceId', updateAppointmentService);

// Update service status
router.patch('/services/:serviceId/status', updateServiceStatus);

// Update service notes
router.patch('/appointment-services/:serviceId/notes', updateServiceNotes);

// Delete a specific service
router.delete('/services/:serviceId', deleteAppointmentService);

export default router;
