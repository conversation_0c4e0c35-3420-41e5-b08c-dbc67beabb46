import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Client } from "@/store/types";
import { Upload, X, Search, Plus } from "lucide-react";
import { createPet } from '@/services/pets';
import { getSpecies } from '@/services/species';
import { getBreedsBySpecies } from '@/services/breeds';
import { searchClients } from '@/services/clients';

interface AddPetWithClientModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Define form validation schema
const formSchema = z.object({
  name: z.string().min(1, "Pet name is required"),
  speciesId: z.string().min(1, "Species is required"),
  breedId: z.string().min(1, "Breed is required"),
  color: z.string().min(1, "Color is required"),
  lifeStatus: z.enum(["alive", "deceased"]),
  gender: z.enum(["male", "female"]),
  microchipId: z.string().min(1, "Microchip ID is required"),
  weight: z.number().min(0, "Weight must be positive"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  clientId: z.string().min(1, "Client is required"),
  petStatus: z.number().default(1)
});

type FormValues = z.infer<typeof formSchema>;

const AddPetWithClientModal = ({ open, onOpenChange }: AddPetWithClientModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [selectedSpecies, setSelectedSpecies] = useState<string>("");
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [clientSearch, setClientSearch] = useState("");
  const [showClientSearch, setShowClientSearch] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      speciesId: "",
      breedId: "",
      color: "",
      lifeStatus: "alive",
      gender: "male",
      microchipId: `MC${Date.now()}`, // Generate default microchip ID
      weight: 0,
      dateOfBirth: "",
      clientId: "",
      petStatus: 1
    }
  });

  // Update clientId when client changes
  useEffect(() => {
    if (selectedClient?.clientId) {
      form.setValue("clientId", selectedClient.clientId.toString());
    }
  }, [selectedClient, form]);

  // Fetch species
  const { data: speciesResponse } = useQuery({
    queryKey: ["species"],
    queryFn: () => getSpecies(),
    enabled: open
  });

  // Fetch breeds based on selected species
  const { data: breedsResponse } = useQuery({
    queryKey: ["breeds", selectedSpecies],
    queryFn: () => getBreedsBySpecies({ speciesId: selectedSpecies }),
    enabled: !!selectedSpecies && open
  });

  // Search clients
  const { data: clientsResponse, refetch: searchClientsRefetch } = useQuery({
    queryKey: ["clients-search", clientSearch],
    queryFn: () => searchClients({ name: clientSearch, limit: 10 }),
    enabled: !!clientSearch && clientSearch.length >= 2
  });

  // Watch for species changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "speciesId") {
        setSelectedSpecies(value.speciesId || "");
        form.setValue("breedId", ""); // Reset breed when species changes
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const mutation = useMutation({
    mutationFn: async (data: FormValues) => {
      // Convert the form data to match backend expectations
      const petData = {
        petName: data.name, // Backend expects 'petName', not 'name'
        speciesId: data.speciesId,
        breedId: data.breedId,
        color: data.color,
        lifeStatus: data.lifeStatus,
        gender: data.gender,
        microchipId: data.microchipId,
        weight: data.weight,
        dateOfBirth: new Date(data.dateOfBirth),
        clientId: data.clientId,
        petStatus: data.petStatus
      };
      return createPet(petData);
    },
    onSuccess: async (data) => {
      if (data?.status === 200 || data?.status === 201) {
        toast({
          title: "Success",
          description: "Pet added successfully",
        });
        await queryClient.invalidateQueries({ queryKey: ["pets"] });
        resetForm();
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: data?.message || "Something went wrong",
          variant: "destructive",
          duration: 2500,
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add pet",
        variant: "destructive",
        duration: 2500,
      });
    }
  });

  const resetForm = () => {
    form.reset({
      name: "",
      speciesId: "",
      breedId: "",
      color: "",
      lifeStatus: "alive",
      gender: "male",
      microchipId: `MC${Date.now()}`, // Generate new microchip ID
      weight: 0,
      dateOfBirth: "",
      clientId: "",
      petStatus: 1
    });
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedSpecies("");
    setSelectedClient(null);
    setClientSearch("");
    setShowClientSearch(false);
  };

  const onSubmit = (values: FormValues) => {
    if (!selectedClient) {
      toast({
        title: "Error",
        description: "Please select a client first",
        variant: "destructive",
      });
      return;
    }
    mutation.mutate(values);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setPreviewUrl("");
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setShowClientSearch(false);
    setClientSearch("");
  };

  const species = speciesResponse?.data?.data || [];
  const breeds = breedsResponse?.data?.data || [];
  const clients = clientsResponse?.data?.data || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Pet</DialogTitle>
          <DialogDescription>
            {selectedClient
              ? `Add a new pet for ${selectedClient.firstName} ${selectedClient.lastName}`
              : "Select a client and add a new pet"
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Client Selection Section */}
            <div className="space-y-2 p-4 border rounded-lg bg-gray-50">
              <Label>Pet Owner *</Label>
              {selectedClient ? (
                <div className="flex items-center justify-between p-2 bg-white border rounded">
                  <span>{selectedClient.firstName} {selectedClient.lastName} - {selectedClient.phoneNumber}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedClient(null);
                      setShowClientSearch(true);
                    }}
                  >
                    Change
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search client by name or phone..."
                      value={clientSearch}
                      onChange={(e) => setClientSearch(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowClientSearch(!showClientSearch)}
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>

                  {clientSearch && clients.length > 0 && (
                    <div className="border rounded-md max-h-40 overflow-y-auto">
                      {clients.map((client) => (
                        <div
                          key={client.clientId}
                          className="p-2 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                          onClick={() => handleClientSelect(client)}
                        >
                          <div className="font-medium">{client.firstName} {client.lastName}</div>
                          <div className="text-sm text-gray-600">{client.phoneNumber}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {clientSearch && clientSearch.length >= 2 && clients.length === 0 && (
                    <div className="text-sm text-gray-500 p-2">No clients found</div>
                  )}
                </div>
              )}
            </div>

            {/* Pet Details - Only show if client is selected */}
            {selectedClient && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pet Name *</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter pet name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="speciesId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Species *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select species" />
                            </SelectTrigger>
                            <SelectContent>
                              {species.map((item) => (
                                <SelectItem key={item.speciesId} value={item.speciesId?.toString() || ""}>
                                  {item.speciesName || item.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="breedId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Breed *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            disabled={!selectedSpecies}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={selectedSpecies ? "Select breed" : "Select species first"} />
                            </SelectTrigger>
                            <SelectContent>
                              {breeds.map((breed) => (
                                <SelectItem key={breed.breedId} value={breed.breedId?.toString() || ""}>
                                  {breed.breedName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color *</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter color" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gender *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select gender" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lifeStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Life Status *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="alive">Alive</SelectItem>
                              <SelectItem value="deceased">Deceased</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Weight (kg) *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="Enter weight"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth *</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="microchipId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Microchip ID *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter microchip ID" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <Label>Pet Image</Label>
                  <div className="border-2 border-dashed rounded-md p-4 text-center">
                    {previewUrl ? (
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Preview"
                          className="max-h-40 object-contain mx-auto"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute top-0 right-0 bg-white rounded-full"
                          onClick={handleRemoveImage}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <Label
                        htmlFor="petImage"
                        className="cursor-pointer flex flex-col items-center justify-center gap-2"
                      >
                        <Upload className="h-8 w-8 text-gray-400" />
                        <span>Click to upload a pet image</span>
                        <Input
                          id="petImage"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleFileChange}
                        />
                      </Label>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      resetForm();
                      onOpenChange(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={mutation.isPending}>
                    {mutation.isPending ? "Adding..." : "Add Pet"}
                  </Button>
                </div>
              </>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPetWithClientModal;
