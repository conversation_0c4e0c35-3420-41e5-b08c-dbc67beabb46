import aj from '../config/arcjet.js';

const arcjetMiddleware = async (req, res, next) => {
    try {
        const decision = await aj.protect(req, {requested: 1});

        if (decision.isDenied()) {
            if (decision.reason.isRateLimit())
                return res.status(200).json({
                    success: false,
                    status: 429,
                    message: `Rate limit exceeded ${JSON.stringify(decision.reason)}`
                });

            if (decision.reason.isBot())
                return res.status(200).json({
                    success: false,
                    status: 403,
                    message: `<PERSON><PERSON> detected ${JSON.stringify(decision.reason)}`
                });

            return res.status(200).json({
                success: false,
                status: 403,
                message: `Access denied ${JSON.stringify(decision.reason)}`
            });
        }

        next();

    } catch (error) {
        console.log(`Arcjet Middleware Error: ${error}`);
        next(error);
    }
}

export default arcjetMiddleware;