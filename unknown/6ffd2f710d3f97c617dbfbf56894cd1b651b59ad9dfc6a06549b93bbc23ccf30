
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { ChevronRight, ChevronLeft, User, Mail, Phone, Lock, Building, MapPin, Globe, FileText, MoonStar, Sun } from "lucide-react";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { signupUser } from '@/services/auth';
import { useTheme } from "next-themes";

const ownerFormSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  middleName: z.string().optional(),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  phoneNumber: z.string().min(10, "Phone number must be at least 10 digits"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const clinicFormSchema = z.object({
  clinicName: z.string().min(2, "Clinic name must be at least 2 characters"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  email: z.string().email("Invalid email address"),
  website: z.string().url("Invalid website URL").optional(),
  description: z.string().min(10, "Description must be at least 10 characters"),
});

const RegisterOwnerAndClinic = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [currentBgIndex, setCurrentBgIndex] = useState(0);

  const backgroundImages = [
    "/pets/cat.jpg",
    "/pets/dog.jpg",
    "/pets/parrot.jpg",
    "/pets/horse.jpg",
    "/pets/pig.jpg",
    "/pets/goat.jpg"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBgIndex((prev) => (prev + 1) % backgroundImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const ownerForm = useForm<z.infer<typeof ownerFormSchema>>({
    resolver: zodResolver(ownerFormSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      phoneNumber: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const clinicForm = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinicName: "",
      address: "",
      phone: "",
      email: "",
      website: "",
      description: "",
    },
  });

  const onSubmit = async () => {
    if (step < 3) {
      setStep(step + 1);
      return;
    }
  
    try {
      setIsLoading(true);
      
      const ownerValues = ownerForm.getValues();
      const clinicValues = clinicForm.getValues();
  
      const payload = {
        firstName: ownerValues.firstName,
        middleName: ownerValues.middleName,
        lastName: ownerValues.lastName,
        email: ownerValues.email,
        phoneNumber: ownerValues.phoneNumber,
        password: ownerValues.password,
        roleId: 1002,
        is_clinic_owner: true,
        clinic_data: {
          name: clinicValues.clinicName,
          phoneNumber: clinicValues.phone,
          email: clinicValues.email,
          address: clinicValues.address
        }
      };
  
      const response = await signupUser(payload);
  
      if (!response.success) {
        throw new Error(response.message || 'Registration failed');
      }
  
      setShowSuccessDialog(true);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.response?.data?.message || "Registration failed",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const renderOwnerPersonalInfo = () => (
    <div className="space-y-4">
      <FormField
        control={ownerForm.control}
        name="firstName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">First Name</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <User className="h-4 w-4" />
                </div>
                <Input className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={ownerForm.control}
        name="middleName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Middle Name (Optional)</FormLabel>
            <FormControl>
              <Input className="dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={ownerForm.control}
        name="lastName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Last Name</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <User className="h-4 w-4" />
                </div>
                <Input className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={ownerForm.control}
        name="phoneNumber"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Phone Number</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Phone className="h-4 w-4" />
                </div>
                <Input type="tel" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
    </div>
  );

  const renderOwnerAccountInfo = () => (
    <div className="space-y-4">
      <FormField
        control={ownerForm.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Email</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Mail className="h-4 w-4" />
                </div>
                <Input type="email" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={ownerForm.control}
        name="password"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Password</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Lock className="h-4 w-4" />
                </div>
                <Input type="password" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={ownerForm.control}
        name="confirmPassword"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Confirm Password</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Lock className="h-4 w-4" />
                </div>
                <Input type="password" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
    </div>
  );

  const renderClinicInfo = () => (
    <div className="space-y-4">
      <FormField
        control={clinicForm.control}
        name="clinicName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Clinic Name</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Building className="h-4 w-4" />
                </div>
                <Input className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={clinicForm.control}
        name="address"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Address</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <MapPin className="h-4 w-4" />
                </div>
                <Input className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={clinicForm.control}
        name="phone"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Phone</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Phone className="h-4 w-4" />
                </div>
                <Input type="tel" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={clinicForm.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Clinic Email</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Mail className="h-4 w-4" />
                </div>
                <Input type="email" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={clinicForm.control}
        name="website"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Website (Optional)</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <Globe className="h-4 w-4" />
                </div>
                <Input type="url" className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
      <FormField
        control={clinicForm.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="dark:text-white">Description</FormLabel>
            <FormControl>
              <div className="relative">
                <div className="absolute top-3 left-3 pointer-events-none text-gray-400">
                  <FileText className="h-4 w-4" />
                </div>
                <Textarea className="pl-10 pt-2 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600" {...field} />
              </div>
            </FormControl>
            <FormMessage className="dark:text-red-400" />
          </FormItem>
        )}
      />
    </div>
  );

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Theme toggle button */}
      <button 
        onClick={toggleTheme} 
        className="absolute top-5 right-5 p-2 rounded-full bg-background/50 backdrop-blur-sm border border-border z-50"
        aria-label="Toggle theme"
      >
        {theme === "dark" ? <Sun className="h-5 w-5" /> : <MoonStar className="h-5 w-5" />}
      </button>

      {/* Background with changing images */}
      <div className="absolute inset-0 overflow-hidden">
        {backgroundImages.map((img, index) => (
          <div
            key={img}
            className={`absolute inset-0 transition-opacity duration-1000 bg-cover bg-center ${
              index === currentBgIndex ? 'opacity-20' : 'opacity-0'
            }`}
            style={{ backgroundImage: `url(${img})` }}
          />
        ))}
        <div className="absolute inset-0 bg-gradient-to-b from-primary-600/40 via-primary-600/20 to-background dark:from-gray-900/70 dark:via-gray-900/50 dark:to-background" />
      </div>

      <div className="relative z-10 flex flex-col justify-center min-h-screen py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-xl">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-primary dark:text-primary-200">VetCare Platform</h1>
            <h2 className="mt-2 text-2xl font-semibold text-foreground dark:text-white">
              Register as Clinic Owner
            </h2>
            <p className="mt-2 text-foreground/90 dark:text-white/90">
              Join our platform and manage your veterinary clinic efficiently
            </p>
          </div>
          
          <div className="mt-6 flex justify-center space-x-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className={`flex flex-col items-center ${i < step ? "text-primary dark:text-primary-200" : "text-gray-400 dark:text-gray-500"}`}
              >
                <div
                  className={`h-10 w-10 rounded-full flex items-center justify-center border-2 transition-colors duration-300 ${
                    i < step ? "border-primary bg-primary text-white dark:border-primary-200 dark:bg-primary-200 dark:text-gray-900" : 
                    i === step ? "border-primary text-primary dark:border-primary-200 dark:text-primary-200" : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {i}
                </div>
                <span className="mt-2 text-xs text-foreground dark:text-white">
                  {i === 1 ? "Personal" : i === 2 ? "Account" : "Clinic"}
                </span>
              </div>
            ))}
          </div>
        </div>

        <Card className="mt-8 sm:mx-auto sm:w-full sm:max-w-xl shadow-lg border-0 bg-white/90 backdrop-blur-sm dark:bg-gray-800/90">
          <CardHeader>
            <CardTitle className="text-center text-xl dark:text-white">
              {step === 1
                ? "Personal Information"
                : step === 2
                ? "Account Details"
                : "Clinic Information"}
            </CardTitle>
            <CardDescription className="text-center dark:text-gray-300">
              {step === 1
                ? "Tell us about yourself"
                : step === 2
                ? "Create your account credentials"
                : "Provide details about your clinic"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step <= 2 ? (
              <Form {...ownerForm}>
                <form onSubmit={ownerForm.handleSubmit(onSubmit)} className="space-y-4">
                  <div
                    className={`transition-opacity duration-300 ${
                      step === 1 ? "opacity-100" : "hidden opacity-0"
                    }`}
                  >
                    {step === 1 && renderOwnerPersonalInfo()}
                  </div>
                  <div
                    className={`transition-opacity duration-300 ${
                      step === 2 ? "opacity-100" : "hidden opacity-0"
                    }`}
                  >
                    {step === 2 && renderOwnerAccountInfo()}
                  </div>
                  <div className="flex justify-between mt-6">
                    {step > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setStep(step - 1)}
                        className="flex items-center dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"
                      >
                        <ChevronLeft className="w-4 h-4 mr-2" />
                        Back
                      </Button>
                    )}
                    <Button
                      type="button"
                      className={`flex items-center ${step === 1 ? "ml-auto" : ""}`}
                      disabled={isLoading}
                      onClick={() => {
                        if (step === 1) {
                          ownerForm.trigger(['firstName', 'middleName', 'lastName', 'phoneNumber'])
                            .then((isValid) => {
                              if (isValid) setStep(step + 1);
                            });
                        } else if (step === 2) {
                          ownerForm.trigger(['email', 'password', 'confirmPassword'])
                            .then((isValid) => {
                              if (isValid) setStep(step + 1);
                            });
                        }
                      }}
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </form>
              </Form>
            ) : (
              <Form {...clinicForm}>
                <form onSubmit={clinicForm.handleSubmit(onSubmit)} className="space-y-4">
                  {renderClinicInfo()}
                  <div className="flex justify-between mt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setStep(step - 1)}
                      className="flex items-center dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      Back
                    </Button>
                    <Button type="submit" disabled={isLoading} className="bg-primary hover:bg-primary/90 dark:bg-primary-500 dark:hover:bg-primary-600">
                      {isLoading ? "Registering..." : "Register"}
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>

        {/* Sign in link */}
        <div className="text-center mt-6">
          <Button variant="link" onClick={() => navigate("/login")} className="text-gray-700 dark:text-gray-300">
            Already have an account? <span className="text-primary dark:text-primary-200 font-medium ml-1">Sign in</span>
          </Button>
        </div>
      </div>

      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-semibold text-green-600 dark:text-green-400">Registration Successful!</DialogTitle>
            <DialogDescription className="text-center dark:text-gray-300">
              Your clinic owner account has been created successfully
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-4">
            <div className="bg-green-100 dark:bg-green-900/30 rounded-full p-3 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-center text-gray-600 dark:text-gray-400">
              You can now sign in to your account and start managing your clinic
            </p>
            <Button 
              onClick={() => navigate("/login")} 
              className="mt-4 bg-primary hover:bg-primary/90 dark:bg-primary-500 dark:hover:bg-primary-600"
            >
              Go to Login
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RegisterOwnerAndClinic;
