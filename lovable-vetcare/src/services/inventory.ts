import { PaginationData } from '@/store/types';
import { api } from './api';

export interface InventoryItem {
  _id: string;
  name: string;
  itemCode?: string;
  description?: string;
  category: 'medication' | 'vaccine' | 'supply' | 'equipment' | 'food' | 'other';
  isMedication: boolean;
  dosageForm?: 'tablet' | 'capsule' | 'liquid' | 'injection' | 'cream' | 'ointment' | 'powder' | 'other' | null;
  activeIngredient?: string;
  concentration?: string;
  quantity: number;
  unit: string;
  reorderLevel: number;
  costPrice: number;
  sellingPrice: number;
  currency: string;
  supplier?: {
    name?: string;
    contactPerson?: string;
    phoneNumber?: string;
    email?: string;
  };
  expiryDate?: Date;
  batchNumber?: string;
  location?: string;
  clinicId: string;
  status: 'active' | 'discontinued' | 'expired' | 'recalled';
  lastUsedDate?: Date;
  transactions?: Array<{
    type: 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'expired' | 'damaged';
    quantity: number;
    date: Date;
    performedBy?: string;
    notes?: string;
    relatedRecord?: {
      recordType?: 'healthRecord' | 'purchase' | 'transfer' | 'other';
      recordId?: string;
    };
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface InventoryItemResponse {
  success: boolean;
  status: number;
  message: string;
  data: InventoryItem | null;
}

export interface InventoryItemsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: InventoryItem[];
    pagination: PaginationData;
  };
}

export interface InventorySearchParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isMedication?: boolean;
  status?: string;
  clinicId?: string;
  lowStock?: boolean;
  expired?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

export interface InventoryAdjustmentParams {
  adjustmentType: 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'expired' | 'damaged';
  quantity: number;
  notes?: string;
  relatedRecordType?: 'healthRecord' | 'purchase' | 'transfer' | 'other';
  relatedRecordId?: string;
}

/**
 * Get inventory items with pagination and filtering
 */
export const getInventoryItems = async (params: InventorySearchParams = {}): Promise<InventoryItemsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<InventoryItemsResponse>(`/inventory?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch inventory items',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get a specific inventory item by ID
 */
export const getInventoryItemById = async (itemId: string): Promise<InventoryItemResponse> => {
  try {
    return await api.get<InventoryItemResponse>(`/inventory/${itemId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch inventory item',
      data: null
    };
  }
};

/**
 * Create a new inventory item
 */
export const createInventoryItem = async (itemData: Partial<InventoryItem>): Promise<InventoryItemResponse> => {
  try {
    return await api.post<InventoryItemResponse>('/inventory', itemData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create inventory item',
      data: null
    };
  }
};

/**
 * Update an inventory item
 */
export const updateInventoryItem = async (
  itemId: string,
  itemData: Partial<Omit<InventoryItem, '_id' | 'createdAt' | 'updatedAt'>>
): Promise<InventoryItemResponse> => {
  try {
    return await api.put<InventoryItemResponse>(`/inventory/${itemId}`, itemData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update inventory item',
      data: null
    };
  }
};

/**
 * Delete an inventory item
 */
export const deleteInventoryItem = async (itemId: string): Promise<InventoryItemResponse> => {
  try {
    return await api.delete<InventoryItemResponse>(`/inventory/${itemId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete inventory item',
      data: null
    };
  }
};

/**
 * Adjust inventory quantity
 */
export const adjustInventory = async (
  itemId: string,
  adjustmentData: InventoryAdjustmentParams
): Promise<InventoryItemResponse> => {
  try {
    return await api.post<InventoryItemResponse>(`/inventory/${itemId}/adjust`, adjustmentData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to adjust inventory',
      data: null
    };
  }
};
