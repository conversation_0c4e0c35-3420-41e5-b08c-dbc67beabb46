import { ManagementDashboard } from "@/components/management/ManagementDashboard";
import { useAuth } from "@/store";
import { Navigate } from "react-router-dom";

const ManagementPage = () => {
  const { user, staff } = useAuth();

  // Check if user has admin access
  const isAdmin = user?.role === 'super_admin' || staff?.isClinicOwner;
  
  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ManagementDashboard 
        userRole={user?.role || staff?.role?.roleName}
        isAdmin={user?.role === 'super_admin'}
      />
    </div>
  );
};

export default ManagementPage;
