import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import Staff from '../models/staff.model.js';
import Clinic from "../models/clinic.model.js";
import Role from "../models/role.model.js";
import Permission from "../models/permission.model.js";
import mongoose from 'mongoose';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import { JWT_SECRET, JWT_EXPIRES_IN } from '../config/env.js';

// Login Staff
// export const


export const registerStaff = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const {
            firstName,
            middleName,
            lastName,
            email,
            phoneNumber,
            address,
            dob,
            roleId,
            jobTitle,
            employmentDate,
            salary,
            clinicId,
            isClinicOwner = false,
            isManager = false,
            emergencyContact,
            schedule
        } = req.body;

        // Check if staff already exists with this email
        const existingStaff = await Staff.findOne({ email }).exec();
        if (existingStaff) {
            return sendResponse(res, 409, false, "Staff with this email already exists");
        }

        // Check if clinic exists - use clinicId (Number) instead of _id (ObjectId)
        let clinicQuery = {};
        if (!isNaN(clinicId)) {
            clinicQuery.clinicId = parseInt(clinicId);
        } else {
            // Try to find by ObjectId if it's not a number
            clinicQuery.clinicId = parseInt(clinicId.toString().replace(/^new ObjectId\(['"](.+)['"]\)$/, '$1'));
        }

        const existingClinic = await Clinic.findOne(clinicQuery).lean();
        if (!existingClinic) {
            await session.abortTransaction();
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Create password
        const password = 'pass123';
        // const password = Math.random().toString(36).slice(-8);

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create new staff member
        const [newStaff] = await Staff.create([{
            // Authentication fields
            email,
            password: hashedPassword,

            // Personal information
            firstName,
            middleName,
            lastName,
            phoneNumber,
            address,
            dob,

            // Clinic information
            clinicId: existingClinic.clinicId, // Use the numeric clinicId from the found clinic
            primaryClinicId: existingClinic.clinicId, // Use the numeric clinicId from the found clinic
            additionalClinics: [],

            // Job information
            roleId,
            jobTitle,
            employmentDate,
            salary,
            isClinicOwner,
            isManager,
            emergencyContact,
            schedule,

            // Status
            status: 1,

            // Initialize clinic activity
            clinicActivity: [{
                clinicId: existingClinic.clinicId, // Use the numeric clinicId from the found clinic
                lastActive: new Date(),
                activityCount: 1
            }]
        }], { session });

        // Generate token
        const token = jwt.sign(
            {
                userId: newStaff.userId,
                staffId: newStaff.staffId,
                userType: 'staff'
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        await session.commitTransaction();
        await session.endSession();

        // Remove password from response
        const staffResponse = newStaff.toObject();
        delete staffResponse.password;

        return sendResponse(res, 201, true, "Staff registered successfully", {
            token,
            user: {
                ...staffResponse,
                userType: 'staff'
            },
            staff: staffResponse
        });
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Staff registration error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};


//
export const getAllStaff = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "createdAt",
            sortOrder = "desc",
            clinicId,
            roleId,
            status,
            search,
            createdBy,
            updatedBy,
            isClinicOwner
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};

        // Add filters
        if (clinicId) {
            // Ensure clinicId is treated as a number
            if (!isNaN(clinicId)) {
                query.clinicId = parseInt(clinicId);
            } else {
                // If it's not a number, try to use it as an ObjectId
                try {
                    query.clinicId = clinicId;
                } catch (error) {
                    console.error("Invalid clinicId format:", error);
                }
            }
        }
        if (roleId) query.roleId = parseInt(roleId);
        if (status !== undefined) query.status = parseInt(status);
        if (createdBy) query.createdBy = createdBy;
        if (updatedBy) query.updatedBy = updatedBy;
        if (isClinicOwner !== undefined) {
            // Convert string to boolean
            query.isClinicOwner = isClinicOwner === 'true' || isClinicOwner === true;
        }

        // Handle search across multiple fields
        if (search) {
            // Build search query for staff fields
            const searchQuery = [
                { firstName: { $regex: search, $options: "i" } },
                { lastName: { $regex: search, $options: "i" } },
                { email: { $regex: search, $options: "i" } },
                { phoneNumber: { $regex: search, $options: "i" } },
                { jobTitle: { $regex: search, $options: "i" } },
                { $expr: { $regexMatch: {
                    input: { $concat: ["$firstName", " ", "$lastName"] },
                    regex: search,
                    options: "i"
                }}}
            ];

            // Add search conditions to the main query
            query.$or = searchQuery;
        }

        // Get all data in parallel for efficiency
        const [staffMembers, totalCount, roles, clinics] = await Promise.all([
            Staff.find(query)
                .select('-password -__v')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Staff.countDocuments(query),
            Role.find().lean(),
            Clinic.find().lean()
        ]);

        // Log for debugging
        console.log("Staff query:", query);
        console.log("Found staff members:", staffMembers.length);

        // Create maps for efficient lookups
        const roleMap = roles.reduce((map, role) => {
            map[role.roleId] = role;
            return map;
        }, {});

        const clinicMap = clinics.reduce((map, clinic) => {
            map[clinic.clinicId] = clinic;
            return map;
        }, {});

        // Populate role and clinic data
        const populatedStaff = staffMembers.map(staff => {
            const role = roleMap[staff.roleId];
            const clinic = clinicMap[staff.clinicId];
            const primaryClinic = clinicMap[staff.primaryClinicId];

            return {
                ...staff,
                role: role ? {
                    roleId: role.roleId,
                    roleName: role.name || role.roleName,
                    description: role.description,
                    category: role.category,
                    permissions: role.permissions
                } : null,
                clinic: clinic ? {
                    clinicId: clinic.clinicId,
                    clinicName: clinic.clinicName,
                    address: clinic.address
                } : null,
                primaryClinic: primaryClinic ? {
                    clinicId: primaryClinic.clinicId,
                    clinicName: primaryClinic.clinicName,
                    address: primaryClinic.address
                } : null
            };
        });

        return sendResponse(res, 200, true, "Staff members retrieved successfully",
            paginateResults(populatedStaff, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get all staff error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const getStaffById = async (req, res) => {
    try {
        const { staffId } = req.params;

        // Use numeric staffId only
        const query = { staffId: parseInt(staffId) };

        // Find the staff member
        const staffMember = await Staff.findOne(query)
            .select('-password -__v')
            .lean();

        if (!staffMember) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Get role and clinic information separately
        const [role, clinic, primaryClinic] = await Promise.all([
            Role.findOne({ roleId: staffMember.roleId }).lean(),
            Clinic.findOne({ clinicId: staffMember.clinicId }).lean(),
            Clinic.findOne({ clinicId: staffMember.primaryClinicId }).lean()
        ]);

        // Populate the staff member data
        const populatedStaffMember = {
            ...staffMember,
            role: role ? {
                roleId: role.roleId,
                roleName: role.name || role.roleName,
                description: role.description,
                category: role.category,
                permissions: role.permissions
            } : null,
            clinic: clinic ? {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName,
                address: clinic.address,
                phoneNumber: clinic.phoneNumber,
                email: clinic.email
            } : null,
            primaryClinic: primaryClinic ? {
                clinicId: primaryClinic.clinicId,
                clinicName: primaryClinic.clinicName,
                address: primaryClinic.address,
                phoneNumber: primaryClinic.phoneNumber,
                email: primaryClinic.email
            } : null
        };

        // Add name field for convenience
        if (staffMember.userId) {
            staffMember.name = `${staffMember.userId.firstName || ''} ${staffMember.userId.lastName || ''}`.trim();
        } else if (staffMember.firstName || staffMember.lastName) {
            staffMember.name = `${staffMember.firstName || ''} ${staffMember.lastName || ''}`.trim();
        }

        return sendResponse(res, 200, true, "Staff member retrieved successfully", populatedStaffMember);
    } catch (error) {
        console.error("Get staff by ID error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateStaff = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const { staffId } = req.params;
        const updateData = req.body;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Find the staff member
        const staffMember = await Staff.findOne(query);
        if (!staffMember) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Create update object with only the fields that are provided
        const updateFields = {};

        // Personal information
        if (updateData.firstName) updateFields.firstName = updateData.firstName;
        if (updateData.middleName !== undefined) updateFields.middleName = updateData.middleName;
        if (updateData.lastName) updateFields.lastName = updateData.lastName;
        if (updateData.email) updateFields.email = updateData.email;
        if (updateData.phoneNumber) updateFields.phoneNumber = updateData.phoneNumber;
        if (updateData.address !== undefined) updateFields.address = updateData.address;
        if (updateData.dob) updateFields.dob = updateData.dob;

        // Job information
        if (updateData.jobTitle) updateFields.jobTitle = updateData.jobTitle;
        if (updateData.salary) updateFields.salary = updateData.salary;
        if (updateData.roleId) updateFields.roleId = updateData.roleId;
        if (updateData.status !== undefined) updateFields.status = updateData.status;
        if (updateData.isClinicOwner !== undefined) updateFields.isClinicOwner = updateData.isClinicOwner;
        if (updateData.isManager !== undefined) updateFields.isManager = updateData.isManager;
        if (updateData.emergencyContact) updateFields.emergencyContact = updateData.emergencyContact;
        if (updateData.schedule) updateFields.schedule = updateData.schedule;

        // Clinic information
        if (updateData.clinicId) {
            // Ensure clinicId is a number
            if (!isNaN(updateData.clinicId)) {
                updateFields.clinicId = parseInt(updateData.clinicId);
            } else {
                // Try to find clinic by ID first to get the numeric clinicId
                const clinic = await Clinic.findOne({
                    $or: [
                        { clinicId: parseInt(updateData.clinicId) },
                        { _id: updateData.clinicId }
                    ]
                }).lean();

                if (clinic) {
                    updateFields.clinicId = clinic.clinicId;
                } else {
                    throw new Error("Invalid clinic ID provided");
                }
            }
        }

        if (updateData.primaryClinicId) {
            // Ensure primaryClinicId is a number
            if (!isNaN(updateData.primaryClinicId)) {
                updateFields.primaryClinicId = parseInt(updateData.primaryClinicId);
            } else {
                // Try to find clinic by ID first to get the numeric clinicId
                const clinic = await Clinic.findOne({
                    $or: [
                        { clinicId: parseInt(updateData.primaryClinicId) },
                        { _id: updateData.primaryClinicId }
                    ]
                }).lean();

                if (clinic) {
                    updateFields.primaryClinicId = clinic.clinicId;
                } else {
                    throw new Error("Invalid primary clinic ID provided");
                }
            }
        }

        if (updateData.additionalClinics) {
            // Ensure all additionalClinics are numbers
            if (Array.isArray(updateData.additionalClinics)) {
                const processedClinics = [];

                for (const clinicId of updateData.additionalClinics) {
                    if (!isNaN(clinicId)) {
                        processedClinics.push(parseInt(clinicId));
                    } else {
                        // Try to find clinic by ID to get the numeric clinicId
                        const clinic = await Clinic.findOne({
                            $or: [
                                { clinicId: parseInt(clinicId) },
                                { _id: clinicId }
                            ]
                        }).lean();

                        if (clinic) {
                            processedClinics.push(clinic.clinicId);
                        }
                    }
                }

                updateFields.additionalClinics = processedClinics;
            }
        }

        // Update password if provided
        if (updateData.password) {
            const salt = await bcrypt.genSalt(10);
            updateFields.password = await bcrypt.hash(updateData.password, salt);
        }

        // Update the staff member
        const updatedStaff = await Staff.findOneAndUpdate(
            query,
            updateFields,
            { new: true, runValidators: true, session }
        )
        .populate('clinicId', 'clinicName address clinicId')
        .populate('primaryClinicId', 'clinicName address clinicId')
        .populate('userId', 'firstName lastName email phoneNumber userId')
        .select('-password');

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 200, true, "Staff member updated successfully", updatedStaff);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Update staff error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

// Get clinic owners for assignment to clinics
export const getClinicOwners = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            search,
            status = 1,
            hasClinic = false // Filter owners who already have a clinic or not
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { firstName: 1, lastName: 1 }; // Sort by name

        let query = {
            isClinicOwner: true,
            status: parseInt(status)
        };

        // Filter by whether they already have a clinic
        if (hasClinic !== undefined) {
            if (hasClinic === 'false' || hasClinic === false) {
                // Get owners without a clinic (not assigned as clinic owner)
                const clinicsWithOwners = await Clinic.find({ owner: { $exists: true, $ne: null } }).distinct('owner');
                query.staffId = { $nin: clinicsWithOwners };
            } else if (hasClinic === 'true' || hasClinic === true) {
                // Get owners who already have a clinic
                const clinicsWithOwners = await Clinic.find({ owner: { $exists: true, $ne: null } }).distinct('owner');
                query.staffId = { $in: clinicsWithOwners };
            }
        }

        // Handle search by name or phone number
        if (search) {
            const searchQuery = [
                { firstName: { $regex: search, $options: "i" } },
                { lastName: { $regex: search, $options: "i" } },
                { phoneNumber: { $regex: search, $options: "i" } },
                { email: { $regex: search, $options: "i" } },
                { $expr: { $regexMatch: {
                    input: { $concat: ["$firstName", " ", "$lastName"] },
                    regex: search,
                    options: "i"
                }}}
            ];

            query.$or = searchQuery;
        }

        // Get clinic owners and total count
        const [clinicOwners, totalCount] = await Promise.all([
            Staff.find(query)
                .select('staffId firstName lastName email phoneNumber jobTitle clinicId primaryClinicId isClinicOwner status createdAt')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Staff.countDocuments(query)
        ]);

        // Get clinic information for owners who have clinics
        const clinicIds = [...new Set(clinicOwners.map(owner => owner.clinicId).filter(Boolean))];
        const clinics = await Clinic.find({ clinicId: { $in: clinicIds } })
            .select('clinicId clinicName address owner')
            .lean();

        const clinicMap = clinics.reduce((map, clinic) => {
            map[clinic.clinicId] = clinic;
            return map;
        }, {});

        // Populate clinic owners with clinic information
        const populatedOwners = clinicOwners.map(owner => {
            const clinic = clinicMap[owner.clinicId];
            const isAssignedAsOwner = clinic && clinic.owner === owner.staffId;

            return {
                ...owner,
                fullName: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
                clinic: clinic ? {
                    clinicId: clinic.clinicId,
                    clinicName: clinic.clinicName,
                    address: clinic.address,
                    isOwner: isAssignedAsOwner
                } : null,
                availableForAssignment: !isAssignedAsOwner
            };
        });

        return sendResponse(res, 200, true, "Clinic owners retrieved successfully",
            paginateResults(populatedOwners, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get clinic owners error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const deleteStaff = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const { staffId } = req.params;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Find the staff member
        const staffMember = await Staff.findOne(query);
        if (!staffMember) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Check if staff is a clinic owner
        if (staffMember.isClinicOwner) {
            // Find clinics owned by this staff
            const ownedClinics = await Clinic.find({ owner: staffId });

            if (ownedClinics.length > 0) {
                await session.abortTransaction();
                await session.endSession();
                return sendResponse(res, 400, false, "Cannot delete a staff member who owns clinics. Please transfer ownership first.");
            }
        }

        // Delete the staff member
        await Staff.findOneAndDelete(query, { session });

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 200, true, "Staff member deleted successfully");
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Delete staff error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Grant special permission to staff member
 */
export const grantSpecialPermission = async (req, res) => {
    try {
        const { staffId } = req.params;
        const { permissionIds } = req.body;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Verify staff exists
        const staff = await Staff.findOne(query);
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Authorization check will be reimplemented later
        // For now, all authenticated users can modify staff permissions

        // Verify permissions exist
        const permissions = await Permission.find({
            permissionId: { $in: permissionIds },
            status: 1
        });

        if (permissions.length !== permissionIds.length) {
            return sendResponse(res, 400, false, "Some permissions are invalid or inactive");
        }

        // Add to special permissions (avoid duplicates)
        staff.specialPermissions = [...new Set([...staff.specialPermissions, ...permissionIds])];

        // Remove from revoked if they exist there
        staff.revokedPermissions = staff.revokedPermissions.filter(
            p => !permissionIds.includes(p)
        );

        await staff.save();

        return sendResponse(res, 200, true, "Special permissions granted successfully", staff);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Revoke permission from staff member
 */
export const revokePermission = async (req, res) => {
    try {
        const { staffId } = req.params;
        const { permissionIds } = req.body;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Verify staff exists
        const staff = await Staff.findOne(query);
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Authorization check will be reimplemented later
        // For now, all authenticated users can modify staff permissions

        // Verify permissions exist
        const permissions = await Permission.find({
            permissionId: { $in: permissionIds }
        });

        if (permissions.length !== permissionIds.length) {
            return sendResponse(res, 400, false, "Some permissions are invalid");
        }

        // Get role to check if permissions are part of the role
        const role = await Role.findOne({ roleId: staff.roleId }).lean();
        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        // Only add to revoked if they exist in the role
        const permissionsToRevoke = permissionIds.filter(p => role.permissions.includes(p));

        // Add to revoked permissions (avoid duplicates)
        staff.revokedPermissions = [...new Set([...staff.revokedPermissions, ...permissionsToRevoke])];

        // Remove from special if they exist there
        staff.specialPermissions = staff.specialPermissions.filter(
            p => !permissionIds.includes(p)
        );

        await staff.save();

        return sendResponse(res, 200, true, "Permissions revoked successfully", staff);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Reset staff permissions to role defaults
 */
export const resetStaffPermissions = async (req, res) => {
    try {
        const { staffId } = req.params;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Verify staff exists
        const staff = await Staff.findOne(query);
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Authorization check will be reimplemented later
        // For now, all authenticated users can modify staff permissions

        // Reset special and revoked permissions
        staff.specialPermissions = [];
        staff.revokedPermissions = [];

        await staff.save();

        return sendResponse(res, 200, true, "Staff permissions reset to role defaults", staff);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update staff profile (comprehensive profile management)
 */
export const updateStaffProfile = async (req, res) => {
    try {
        const { staffId } = req.params;
        const updateData = req.body;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Verify staff exists
        const existingStaff = await Staff.findOne(query);
        if (!existingStaff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // If role is being updated, validate it exists
        if (updateData.roleId) {
            const role = await Role.findOne({ roleId: updateData.roleId });
            if (!role) {
                return sendResponse(res, 404, false, "Invalid role specified");
            }
            // Update permissions based on new role
            updateData.permissions = role.permissions;
        }

        // If clinic is being updated, validate it exists
        if (updateData.clinicId) {
            const clinic = await Clinic.findOne({ clinicId: updateData.clinicId });
            if (!clinic) {
                return sendResponse(res, 404, false, "Invalid clinic specified");
            }
        }

        // Hash password if it's being updated
        if (updateData.password) {
            const salt = await bcrypt.genSalt(10);
            updateData.password = await bcrypt.hash(updateData.password, salt);
        }

        // Update the staff member
        const updatedStaff = await Staff.findOneAndUpdate(
            query,
            { $set: updateData },
            { new: true, runValidators: true }
        ).select('-password -__v').lean();

        // Get role and clinic information
        const [role, clinic, primaryClinic] = await Promise.all([
            Role.findOne({ roleId: updatedStaff.roleId }).lean(),
            Clinic.findOne({ clinicId: updatedStaff.clinicId }).lean(),
            Clinic.findOne({ clinicId: updatedStaff.primaryClinicId }).lean()
        ]);

        // Populate the response data
        const populatedStaff = {
            ...updatedStaff,
            role: role ? {
                roleId: role.roleId,
                roleName: role.name || role.roleName,
                description: role.description,
                category: role.category,
                permissions: role.permissions
            } : null,
            clinic: clinic ? {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName,
                address: clinic.address
            } : null,
            primaryClinic: primaryClinic ? {
                clinicId: primaryClinic.clinicId,
                clinicName: primaryClinic.clinicName,
                address: primaryClinic.address
            } : null
        };

        return sendResponse(res, 200, true, "Staff profile updated successfully", populatedStaff);
    } catch (error) {
        console.error("Update staff profile error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get staff permissions (combined role and special permissions)
 */
export const getStaffPermissions = async (req, res) => {
    try {
        const { staffId } = req.params;

        // Check if staffId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(staffId)) {
            query.staffId = parseInt(staffId);
        } else {
            query._id = staffId;
        }

        // Verify staff exists
        const staff = await Staff.findOne(query).lean();
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Get role information
        const role = await Role.findOne({ roleId: staff.roleId }).lean();

        // Get all permissions from role that aren't revoked
        const rolePermissions = staff.roleId.permissions.filter(
            p => !staff.revokedPermissions.includes(p)
        );

        // Combine with special permissions
        const allPermissionIds = [...new Set([...rolePermissions, ...staff.specialPermissions])];

        // Get permission details
        const permissions = await Permission.find({
            permissionId: { $in: allPermissionIds },
            status: 1
        }).lean();

        // Create name from user data if available
        let name = '';
        if (staff.userId && typeof staff.userId !== 'string') {
            name = `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
        } else if (staff.firstName || staff.lastName) {
            name = `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
        }

        return sendResponse(res, 200, true, "Staff permissions retrieved successfully", {
            staff: {
                _id: staff._id,
                staffId: staff.staffId,
                userId: staff.userId && typeof staff.userId !== 'string' ? staff.userId.userId : staff.userId,
                name: name,
                role: staff.roleId.name
            },
            permissions: permissions,
            specialPermissions: staff.specialPermissions,
            revokedPermissions: staff.revokedPermissions
        });
    } catch (error) {
        console.error("Get staff permissions error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

// Helper function removed as it's not being used
