import express from 'express';
import {
    createPermission,
    getAllPermissions,
    getPermissionById,
    updatePermission,
    deletePermission,
    getPermissionsByModule,
    initializePermissions
} from '../controllers/permission.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Base route: /api/v1/permissions

// Apply authentication middleware to all routes
router.use(verifyToken);

// All routes are now protected
router.get('/', getAllPermissions);
router.get('/:permissionId', getPermissionById);
router.get('/module/:module', getPermissionsByModule);
router.post('/', createPermission);
router.put('/:permissionId', updatePermission);
router.delete('/:permissionId', deletePermission);
router.post('/initialize', initializePermissions);

export default router;