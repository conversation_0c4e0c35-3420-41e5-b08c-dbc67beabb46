import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const appointmentTypeSchema = new mongoose.Schema({
    // Auto-increment ID
    appointmentTypeId: {
        type: Number,
        index: true,
        unique: true,
    },
    name: {
        type: String,
        required: [true, "Appointment type name is required"],
        unique: true,
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    defaultDuration: {
        type: Number,
        required: [true, "Default duration is required"],
        min: 15,  // minimum 15 minutes
    },
    price: {
        type: Number,
        required: [true, "Price is required"],
        min: 0,
    },
    currency: {
        type: String,
        required: [true, "Currency is required"],
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },
    isActive: {
        type: Boolean,
        default: true,
    }
}, { timestamps: true });

// Add auto-increment plugin
appointmentTypeSchema.plugin(AutoIncrement, { inc_field: 'appointmentTypeId', start_seq: 1001 });
appointmentTypeSchema.index({ appointmentTypeId: 1 }, { unique: true });

const AppointmentType = mongoose.model('AppointmentType', appointmentTypeSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = AppointmentType.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        // Check if we need to recreate the collection
        let needToRecreate = false;

        if (collectionExists) {
            // Check if all appointment types exist
            const count = await AppointmentType.countDocuments();
            if (count < 19) {
                console.log(`Found only ${count} appointment types, need to recreate collection`);
                needToRecreate = true;

                // Check for any records with null appointmentTypeId
                const nullIdRecords = await AppointmentType.find({ appointmentTypeId: null });
                if (nullIdRecords.length > 0) {
                    console.log(`Found ${nullIdRecords.length} records with null appointmentTypeId, deleting them first`);
                    await AppointmentType.deleteMany({ appointmentTypeId: null });
                }

                // Also check for the counter collection
                const countersCollection = mongoose.connection.db.collection('counters');
                const counterExists = await countersCollection.findOne({ _id: 'appointmentTypeId' });
                if (counterExists) {
                    console.log('Resetting the auto-increment counter for appointmentTypeId');
                    await countersCollection.deleteOne({ _id: 'appointmentTypeId' });
                }

                // Now drop the collection
                await mongoose.connection.db.dropCollection(collectionName);
            } else {
                console.log(`Found ${count} appointment types, no need to recreate collection`);
                return; // All types exist, no need to recreate
            }
        } else {
            needToRecreate = true;
        }

        if (needToRecreate) {
            // Ensure collection is created again
            await AppointmentType.init();

            // Create the counter document first
            const countersCollection = mongoose.connection.db.collection('counters');
            await countersCollection.insertOne({
                _id: 'appointmentTypeId',
                seq: 1000  // Start at 1000 so next value will be 1001
            });

            // Add default appointment types with their corresponding default services
            const appointmentTypes = [
                {
                    name: 'Vaccination',
                    description: 'Routine vaccination services',
                    defaultDuration: 30,
                    price: 50,
                    currency: 'KES',
                    defaultServices: ['Rabies Vaccination', 'DHPP Vaccination', 'FVRCP Vaccination']
                },
                {
                    name: 'Consultation',
                    description: 'General health consultation',
                    defaultDuration: 45,
                    price: 75,
                    currency: 'KES',
                    defaultServices: ['General Consultation']
                },
                {
                    name: 'Laboratory',
                    description: 'Laboratory tests and diagnostics',
                    defaultDuration: 60,
                    price: 100,
                    currency: 'KES',
                    defaultServices: ['Blood Test', 'Urinalysis', 'Fecal Examination']
                },
                {
                    name: 'Surgery',
                    description: 'Surgical procedures',
                    defaultDuration: 120,
                    price: 300,
                    currency: 'KES',
                    defaultServices: ['Spay Surgery (Female)', 'Neuter Surgery (Male)', 'Tumor Removal']
                },
                {
                    name: 'Hospitalization',
                    description: 'In-patient care services',
                    defaultDuration: 1440,
                    price: 500,
                    currency: 'KES',
                    defaultServices: ['Hospitalization Care', 'Medication Administration']
                },
                {
                    name: 'Grooming',
                    description: 'Pet grooming services',
                    defaultDuration: 90,
                    price: 80,
                    currency: 'KES',
                    defaultServices: ['Basic Grooming', 'Full Grooming with Haircut']
                },
                {
                    name: 'Boarding',
                    description: 'Pet boarding services',
                    defaultDuration: 1440,
                    price: 50,
                    currency: 'KES',
                    defaultServices: ['Pet Boarding']
                },
                {
                    name: 'Dental',
                    description: 'Dental cleaning and procedures',
                    defaultDuration: 90,
                    price: 150,
                    currency: 'KES',
                    defaultServices: ['Dental Cleaning', 'Tooth Extraction']
                },
                {
                    name: 'Wellness Check',
                    description: 'Routine health check-up',
                    defaultDuration: 45,
                    price: 60,
                    currency: 'KES',
                    defaultServices: ['Annual Wellness Exam', 'Puppy/Kitten Wellness Package']
                },
                {
                    name: 'Emergency',
                    description: 'Emergency medical services',
                    defaultDuration: 60,
                    price: 200,
                    currency: 'KES',
                    defaultServices: ['Emergency Consultation']
                },
                {
                    name: 'Follow-up',
                    description: 'Follow-up consultation',
                    defaultDuration: 30,
                    price: 40,
                    currency: 'KES',
                    defaultServices: ['Follow-up Consultation']
                },
                {
                    name: 'Microchipping',
                    description: 'Pet identification service',
                    defaultDuration: 30,
                    price: 45,
                    currency: 'KES',
                    defaultServices: ['Microchip Implantation']
                },
                {
                    name: 'Deworming',
                    description: 'Parasite treatment',
                    defaultDuration: 30,
                    price: 35,
                    currency: 'KES',
                    defaultServices: ['Deworming Treatment']
                },
                {
                    name: 'Physical Therapy',
                    description: 'Rehabilitation services',
                    defaultDuration: 60,
                    price: 90,
                    currency: 'KES',
                    defaultServices: ['Physical Therapy Session']
                },
                {
                    name: 'X-ray/Imaging',
                    description: 'Diagnostic imaging services',
                    defaultDuration: 45,
                    price: 120,
                    currency: 'KES',
                    defaultServices: ['X-ray', 'Ultrasound']
                },
                {
                    name: 'Behavioral Consultation',
                    description: 'Pet behavior consultation',
                    defaultDuration: 60,
                    price: 85,
                    currency: 'KES',
                    defaultServices: ['Behavioral Assessment']
                },
                {
                    name: 'Nutrition Consultation',
                    description: 'Diet and nutrition advice',
                    defaultDuration: 45,
                    price: 70,
                    currency: 'KES',
                    defaultServices: ['Nutritional Assessment']
                },
                {
                    name: 'Euthanasia',
                    description: 'End-of-life services',
                    defaultDuration: 60,
                    price: 150,
                    currency: 'KES',
                    defaultServices: ['Euthanasia Service']
                },
                {
                    name: 'Other',
                    description: 'Other veterinary services',
                    defaultDuration: 30,
                    price: 50,
                    currency: 'KES',
                    defaultServices: ['General Service']
                },
            ];

            // Insert types one by one to ensure auto-increment works properly
            for (const type of appointmentTypes) {
                await AppointmentType.create(type);
            }

            console.log(`Recreated collection: ${collectionName} with all 19 appointment types`);
        }
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default AppointmentType;