
import {useQuery} from "@tanstack/react-query";
import {useNavigate} from "react-router-dom";
import {But<PERSON>} from "@/components/ui/button";
import {getClients} from "@/services/api";
import {MoreHorizontal, Plus, RefreshCw, Search, Filter} from "lucide-react";
import {format} from "date-fns";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import LoadingPage from "@/components/common/LoadingPage";
import {useState} from "react";
import {Input} from "@/components/ui/input";
import {Card, CardContent} from "@/components/ui/card";
import AddPetModal from "./components/AddPetModal";

const Clients = () => {
    const navigate = useNavigate();
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [searchName, setSearchName] = useState("");
    const [email, setEmail] = useState("");
    const [phone, setPhone] = useState("");
    const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
    const [isAddPetModalOpen, setIsAddPetModalOpen] = useState(false);
    const [selectedClient, setSelectedClient] = useState<any>(null);

    const {data: response, isLoading, refetch} = useQuery({
        queryKey: ["clients", page, limit, searchName, email, phone],
        queryFn: async () => {
            const response = await getClients({
                page,
                offset: (page - 1),
                limit,
                name: searchName || undefined,
                email: email || undefined,
                phoneNumber: phone || undefined
            });
            if (!response.success) {
              throw new Error(response.message);
            }
            return response;
          }
    });

    if (isLoading) {
        return <LoadingPage/>;
    }

    const handleRefresh = () => {
        refetch();
    };

    const handleSearch = () => {
        setPage(1); // Reset to first page when searching
        refetch();
    };

    const resetFilters = () => {
        setSearchName("");
        setEmail("");
        setPhone("");
        setPage(1);
        refetch();
    };

    const clients = response?.data?.data || [];
    const pagination = response?.data?.pagination;
    const totalPages = pagination?.totalPages || 1;
    const currentPage = pagination?.page || 1;

    const handlePreviousPage = () => {
        if (page > 1) {
            setPage(page - 1);
        }
    };

    const handleNextPage = () => {
        if (page < totalPages) {
            setPage(page + 1);
        }
    };

    const handleLimitChange = (value: string) => {
        setLimit(Number(value));
        setPage(1);
    };

    return (
        <div className="p-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-semibold">Clients</h1>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={() => navigate("/pets")}>
                        View All Pets
                    </Button>
                    <Button onClick={() => navigate("/clients/add")}>
                        <Plus className="mr-2 h-4 w-4"/> Add Client
                    </Button>
                </div>
            </div>

            <Card className="mb-6">
                <CardContent className="pt-6">
                    <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-4">
                            <div className="relative flex-grow">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by client name..."
                                    value={searchName}
                                    onChange={(e) => setSearchName(e.target.value)}
                                    className="pl-8"
                                />
                            </div>

                            <Button
                                variant="outline"
                                onClick={() => setIsAdvancedSearchOpen(!isAdvancedSearchOpen)}
                                className="flex items-center gap-2"
                            >
                                <Filter className="h-4 w-4" />
                                {isAdvancedSearchOpen ? "Hide Filters" : "Show Filters"}
                            </Button>

                            <Select
                    value={String(limit)}
                    onValueChange={handleLimitChange}
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select page size"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="5">5 per page</SelectItem>
                        <SelectItem value="10">10 per page</SelectItem>
                        <SelectItem value="15">15 per page</SelectItem>
                        <SelectItem value="20">20 per page</SelectItem>
                    </SelectContent>
                </Select>

                        </div>

                        {isAdvancedSearchOpen && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Email</label>
                                    <Input
                                        placeholder="Search by email..."
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Phone Number</label>
                                    <Input
                                        placeholder="Search by phone number..."
                                        value={phone}
                                        onChange={(e) => setPhone(e.target.value)}
                                    />
                                </div>
                            </div>
                        )}
                    </div>

                    {isAdvancedSearchOpen && (  <div className="flex justify-end gap-4 items-center mt-4">

                            <Button variant="outline" onClick={resetFilters}>
                                Reset
                            </Button>

                         <Button onClick={handleSearch}>
                                Apply Filters
                        </Button>

                    </div> )}
                </CardContent>
            </Card>

            {/* <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-muted-foreground">
                    Total Clients: {pagination?.totalCount || 0}
                </div>
                <Select
                    value={String(limit)}
                    onValueChange={handleLimitChange}
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select page size"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="5">5 per page</SelectItem>
                        <SelectItem value="10">10 per page</SelectItem>
                        <SelectItem value="15">15 per page</SelectItem>
                        <SelectItem value="20">20 per page</SelectItem>
                    </SelectContent>
                </Select>
            </div> */}

            {Array.isArray(clients) && clients.length === 0 ? (
                <div className="text-center text-gray-500 mt-8">No clients found</div>
            ) : (
                <>
                    <div className="border rounded-lg">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>ID</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Email</TableHead>
                                    <TableHead>Phone</TableHead>
                                    <TableHead>Created At</TableHead>
                                    <TableHead className="text-right pr-6">
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={handleRefresh}
                                            className="ml-auto"
                                        >
                                            <RefreshCw className="h-4 w-4" />
                                        </Button>
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {Array.isArray(clients) && clients.map((client) => (
                                    <TableRow key={client._id}>
                                        <TableCell>{client.accountId}</TableCell>
                                        <TableCell>
                                            <Button
                                                variant="link"
                                                className="p-0 h-auto font-medium text-left"
                                                onClick={() => navigate(`/clients/${client.accountId}`)}
                                            >
                                                {client.firstName} {client.lastName}
                                            </Button>
                                        </TableCell>
                                        <TableCell>{client.email}</TableCell>
                                        <TableCell>{client.phoneNumber}</TableCell>
                                        <TableCell>
                                            {client.createdAt
                                                ? format(new Date(client.createdAt), "MMM dd, yyyy")
                                                : "N/A"}
                                        </TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4"/>
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end" className="w-[160px]">
                                                    <DropdownMenuItem
                                                        onClick={() => navigate(`/clients/${client.accountId}`)}
                                                    >
                                                        View Profile
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => navigate(`/clients/pets?accountId=${client.accountId}`)}
                                                    >
                                                        View Pets
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => {
                                                            setSelectedClient(client);
                                                            setIsAddPetModalOpen(true);
                                                        }}
                                                    >
                                                        Add Pet
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => navigate(`/clients/${client.accountId}/edit`)}
                                                    >
                                                        Edit Profile
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                    <div className="flex justify-between items-center mt-4">
                        <div className="text-sm text-muted-foreground">
                            Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, pagination?.totalCount || 0)} of {pagination?.totalCount || 0} entries
                        </div>
                        <div className="flex items-center gap-4">
                            <Button
                                variant="outline"
                                onClick={handlePreviousPage}
                                disabled={currentPage === 1}
                            >
                                Previous
                            </Button>
                            <span className="text-sm">
                                Page {currentPage} of {totalPages}
                            </span>
                            <Button
                                variant="outline"
                                onClick={handleNextPage}
                                disabled={currentPage >= totalPages}
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                </>
            )}

            {/* Add Pet Modal */}
            {selectedClient && (
                <AddPetModal
                    open={isAddPetModalOpen}
                    onOpenChange={setIsAddPetModalOpen}
                    client={selectedClient}
                />
            )}
        </div>
    );
};

export default Clients;
