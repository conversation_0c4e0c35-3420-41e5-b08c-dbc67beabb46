import ClientClinicRelationship from '../models/clientClinicRelationship.model.js';
import Client from '../models/client.model.js';
import Clinic from '../models/clinic.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

/**
 * Create a new client-clinic relationship
 * This is called when a client visits a clinic for the first time
 */
export const createClientClinicRelationship = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { clientId, clinicId, isPreferredClinic, clinicNotes, dataConsent } = req.body;

        // Check if client and clinic exist
        const [client, clinic] = await Promise.all([
            Client.findOne({ clientId }).session(session),
            Clinic.findOne({ clinicId }).session(session)
        ]);

        if (!client) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Client not found");
        }

        if (!clinic) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Check if relationship already exists
        const existingRelationship = await ClientClinicRelationship.findOne({
            clientId,
            clinicId
        }).session(session);

        if (existingRelationship) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 409, false, "Relationship already exists");
        }

        // Create the relationship
        const relationship = await ClientClinicRelationship.create([{
            clientId,
            clinicId,
            isPreferredClinic: isPreferredClinic || false,
            clinicNotes,
            registeredBy: req.staff?.staffId || 0,
            dataConsent: dataConsent || {
                consentGiven: true,
                consentDate: new Date(),
                consentDetails: "Consent given during relationship creation"
            }
        }], { session });

        // Update client's clinicRelationships array
        // and preferredClinicId if this is the preferred clinic
        await Client.findOneAndUpdate(
            { clientId },
            {
                $push: { clinicRelationships: relationship[0].clientClinicRelationshipId },
                // If this is the preferred clinic, update preferredClinicId
                ...(isPreferredClinic ? { preferredClinicId: clinicId } : {})
            },
            { session }
        );

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 201, true, "Client-clinic relationship created successfully", relationship[0]);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Create client-clinic relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all relationships for a specific client
 */
export const getClientRelationships = async (req, res) => {
    try {
        const { clientId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        // Check if client exists
        const client = await Client.findOne({ clientId });
        if (!client) {
            return sendResponse(res, 404, false, "Client not found");
        }

        // Get relationships with pagination
        const relationships = await ClientClinicRelationship.find({ clientId })
            .populate('clinicId', 'clinicName address phoneNumber email')
            .populate('registeredBy', 'firstName lastName')
            .skip((page - 1) * limit)
            .limit(parseInt(limit))
            .sort({ lastVisitDate: -1 })
            .lean();

        const totalCount = await ClientClinicRelationship.countDocuments({ clientId });

        return sendResponse(res, 200, true, "Client relationships retrieved successfully",
            paginateResults(relationships, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get client relationships error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all relationships for a specific clinic
 */
export const getClinicRelationships = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { page = 1, limit = 10, status, search } = req.query;

        // Check if clinic exists
        const clinic = await Clinic.findOne({ clinicId });
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Build query
        let query = { clinicId };
        if (status) {
            query.relationshipStatus = status;
        }

        // Handle search
        if (search) {
            // We need to find clients that match the search and then find relationships for those clients
            const clientIds = await Client.find({
                $or: [
                    { firstName: { $regex: search, $options: 'i' } },
                    { lastName: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } },
                    { phoneNumber: { $regex: search, $options: 'i' } }
                ]
            }).distinct('_id');

            query.clientId = { $in: clientIds };
        }

        // Get relationships with pagination
        const relationships = await ClientClinicRelationship.find(query)
            .populate('clientId', 'firstName lastName email phoneNumber')
            .populate('registeredBy', 'firstName lastName')
            .skip((page - 1) * limit)
            .limit(parseInt(limit))
            .sort({ lastVisitDate: -1 })
            .lean();

        const totalCount = await ClientClinicRelationship.countDocuments(query);

        return sendResponse(res, 200, true, "Clinic relationships retrieved successfully",
            paginateResults(relationships, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get clinic relationships error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get a specific client-clinic relationship
 */
export const getRelationship = async (req, res) => {
    try {
        const { relationshipId } = req.params;

        const relationship = await ClientClinicRelationship.findOne({ clientClinicRelationshipId: parseInt(relationshipId) })
            .populate('clientId', 'firstName lastName email phoneNumber')
            .populate('clinicId', 'clinicName address phoneNumber email')
            .populate('registeredBy', 'firstName lastName')
            .lean();

        if (!relationship) {
            return sendResponse(res, 404, false, "Relationship not found");
        }

        return sendResponse(res, 200, true, "Relationship retrieved successfully", relationship);
    } catch (error) {
        console.error("Get relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update a client-clinic relationship
 */
export const updateRelationship = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { relationshipId } = req.params;
        const updateData = req.body;

        // Find the relationship
        const relationship = await ClientClinicRelationship.findOne({ clientClinicRelationshipId: parseInt(relationshipId) }).session(session);
        if (!relationship) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Relationship not found");
        }

        // If updating preferred clinic status
        if (updateData.isPreferredClinic !== undefined) {
            // If setting as preferred, update client's preferredClinicId
            if (updateData.isPreferredClinic) {
                await Client.findOneAndUpdate(
                    { clientId: relationship.clientId },
                    { preferredClinicId: relationship.clinicId },
                    { session }
                );
            }
            // If removing preferred status, only update if this clinic is the preferred one
            else if (updateData.isPreferredClinic === false) {
                const client = await Client.findOne({ clientId: relationship.clientId }).session(session);
                if (client.preferredClinicId && client.preferredClinicId.toString() === relationship.clinicId.toString()) {
                    await Client.findOneAndUpdate(
                        { clientId: relationship.clientId },
                        { preferredClinicId: null },
                        { session }
                    );
                }
            }
        }

        // Update the relationship
        const updatedRelationship = await ClientClinicRelationship.findOneAndUpdate(
            { clientClinicRelationshipId: parseInt(relationshipId) },
            updateData,
            { new: true, runValidators: true, session }
        )
        .populate('clientId', 'firstName lastName email phoneNumber')
        .populate('clinicId', 'clinicName address phoneNumber email')
        .populate('registeredBy', 'firstName lastName');

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 200, true, "Relationship updated successfully", updatedRelationship);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Update relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
