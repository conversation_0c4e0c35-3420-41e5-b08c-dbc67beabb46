/**
 * CSRF Protection Middleware
 *
 * This middleware adds CSRF protection to routes that modify data.
 * It should be applied to routes that handle POST, PUT, DELETE requests.
 */

import csrf from 'csurf';
import { NODE_ENV } from '../config/env.js';
import { sendResponse } from '../utils/responseHandler.js';

// Configure CSRF protection
const csrfProtection = csrf({
  cookie: {
    key: '_csrf',
    path: '/',
    httpOnly: true,
    secure: NODE_ENV === 'production',
    sameSite: NODE_ENV === 'production' ? 'strict' : 'lax' // Use 'lax' in development
  },
  // In development, we can ignore all methods for easier testing
  ignoreMethods: NODE_ENV === 'development'
    ? ['GET', 'HEAD', 'OPTIONS', 'POST', 'PUT', 'DELETE']
    : ['GET', 'HEAD', 'OPTIONS']
});

// Middleware to handle CSRF errors
const handleCSRFError = (err, _req, res, next) => {
  if (err.code !== 'EBADCSRFTOKEN') {
    return next(err);
  }

  // Handle CSRF token validation failure
  return sendResponse(res, 403, false, 'Invalid or expired CSRF token');
};

// Middleware to provide CSRF token
const provideCSRFToken = (req, res, next) => {
  // Add CSRF token to response locals for templates
  res.locals.csrfToken = req.csrfToken();

  // Also add token to a custom header for API clients
  res.cookie('XSRF-TOKEN', req.csrfToken(), {
    path: '/',
    httpOnly: false, // Client-side JavaScript needs to read this
    secure: NODE_ENV === 'production',
    sameSite: 'strict'
  });

  next();
};

export { csrfProtection, handleCSRFError, provideCSRFToken };
