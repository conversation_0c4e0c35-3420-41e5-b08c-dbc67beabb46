import { useState, useEffect, KeyboardEvent } from "react";
import { useAuth } from "@/store";
import { Clinic, User as UserType, Staff as StaffType } from "@/store/types";
import { api } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";
import { RolePermissionManager } from "@/components/profile/RolePermissionManager";
import { ClinicProfileManager } from "@/components/profile/ClinicProfileManager";

// Declare global window interface to add our update functions
declare global {
  interface Window {
    updateUser?: (user: UserType) => void;
    updateStaff?: (staff: StaffType) => void;
  }
}
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import {
  User, Mail, Phone, Calendar, MapPin, Building2,
  Briefcase, Shield, Edit, Key, Clock, Check, X
} from "lucide-react";
import { format, parse } from "date-fns";
import { Link } from "react-router-dom";

interface EditableField {
  key: string;
  value: string;
  type: 'user' | 'staff';
  inputType?: string;
}

const ViewProfile = () => {
  const { user, staff, clinic: currentClinic, clinics = [], updateUser, updateStaff } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [mainClinic, setMainClinic] = useState<Clinic | null>(null);
  const [editingField, setEditingField] = useState<EditableField | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const [updating, setUpdating] = useState(false);

  // Fetch main clinic if user is a clinic owner
  useEffect(() => {
    const fetchMainClinic = async () => {
      if (!staff?.isClinicOwner || !staff?.primaryClinicId) {
        return;
      }

      setLoading(true);
      try {
        // First check if the primary clinic is in the clinics array
        if (clinics && clinics.length > 0) {
          const primaryClinic = clinics.find(
            (clinic) => clinic._id === staff.primaryClinicId
          );

          if (primaryClinic) {
            setMainClinic(primaryClinic);
            return;
          }
        }

        // If not found in clinics array, fetch from API
        const response = await api.get(`/clinics/${staff.primaryClinicId}`);
        if (response.data?.success) {
          setMainClinic(response.data.data);
        }
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch clinic information",
        });
        console.error("Error fetching clinic:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMainClinic();
  }, [staff, clinics, toast]);

  // Format date function
  const formatDate = (dateString?: string | Date) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "PPP");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Start editing a field
  const startEditing = (key: string, value: string, type: 'user' | 'staff', inputType: string = 'text') => {
    setEditingField({ key, value, type, inputType });
    setEditValue(value || '');
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingField(null);
    setEditValue("");
  };

  // Handle key press in edit field
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      saveEdit();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  // Save the edited field
  const saveEdit = async () => {
    if (!editingField) return;

    setUpdating(true);
    try {
      const { key, type } = editingField;

      // Prepare update data
      const updateData: Record<string, any> = {};

      // Handle special field types
      if (key === 'dob' || key === 'employmentDate') {
        // For date fields, ensure we send a proper ISO date string
        try {
          // If the value is already a valid date string, use it directly
          if (Date.parse(editValue)) {
            updateData[key] = new Date(editValue).toISOString();
          } else {
            // Otherwise, try to parse it using date-fns
            const parsedDate = parse(editValue, 'PP', new Date());
            updateData[key] = parsedDate.toISOString();
          }
        } catch (e) {
          // If parsing fails, use the raw value
          updateData[key] = editValue;
        }
      } else {
        // For regular fields, use the value as is
        updateData[key] = editValue;
      }

      let response;

      // Update user or staff based on the field type
      if (type === 'user' && user?._id) {
        response = await api.put(`/users/${user._id}`, updateData);
      } else if (type === 'staff' && staff?._id) {
        response = await api.put(`/staff/${staff._id}`, updateData);
      }

      if (response?.data?.success && response?.data?.status === 200) {
        // Get the updated data from the response
        const updatedData = response.data.data;

        // Update the auth store with the new data
        if (type === 'user' && updatedData) {
          // Create a shallow copy of the current user
          const updatedUser = { ...user } as UserType;

          // Update the specific field that was changed
          (updatedUser as any)[key] = updatedData[key];

          // Update any other fields that might have changed in the response
          Object.keys(updatedData).forEach(field => {
            if (field !== '_id' && field !== 'createdAt' && field !== 'updatedAt' && field !== '__v') {
              (updatedUser as any)[field] = updatedData[field];
            }
          });

          // Update the user in the auth store using the function from useAuth
          updateUser(updatedUser);
        } else if (type === 'staff' && updatedData) {
          // Create a shallow copy of the current staff
          const updatedStaff = { ...staff } as StaffType;

          // Update the specific field that was changed
          (updatedStaff as any)[key] = updatedData[key];

          // Update any other fields that might have changed in the response
          Object.keys(updatedData).forEach(field => {
            if (field !== '_id' && field !== 'createdAt' && field !== 'updatedAt' && field !== '__v') {
              (updatedStaff as any)[field] = updatedData[field];
            }
          });

          // Update the staff in the auth store using the function from useAuth
          updateStaff(updatedStaff);
        }

        toast({
          title: "Success",
          description: response.data.message || "Profile updated successfully",
        });
      } else {
        // Handle different status codes
        let errorMessage = response?.data?.message || "Failed to update profile";

        if (response?.data?.status && response.data.status !== 200) {
          errorMessage = `Error (${response.data.status}): ${errorMessage}`;
        }

        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage,
        });
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error?.message || "An error occurred while updating profile",
      });
      console.error("Error updating profile:", error);
    } finally {
      setUpdating(false);
      setEditingField(null);
    }
  };

  // Determine user type for display
  const getUserTypeDisplay = () => {
    if (staff?.isClinicOwner) return "Clinic Owner";
    if (staff) return "Staff Member";
    if (user?.role === "super_admin") return "System Administrator";
    if (user?.role === "admin") return "Administrator";
    return "User";
  };

  // Editable field component
  const EditableField = ({
    label,
    value,
    fieldKey,
    type,
    icon: Icon,
    inputType = 'text'
  }: {
    label: string,
    value: string | undefined,
    fieldKey: string,
    type: 'user' | 'staff',
    icon: React.ElementType,
    inputType?: string
  }) => {
    const isEditing = editingField?.key === fieldKey;
    const displayValue = value || "Not provided";

    return (
      <div>
        <h3 className="text-sm font-medium text-muted-foreground flex items-center justify-between">
          {label}
          {!isEditing && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => startEditing(fieldKey, displayValue, type, inputType)}
            >
              <Edit className="h-3 w-3" />
            </Button>
          )}
        </h3>

        {isEditing ? (
          <div className="flex items-center mt-1">
            <Input
              type={inputType}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyPress}
              autoFocus
              className="h-8"
              disabled={updating}
            />
            <div className="flex ml-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-green-500"
                onClick={saveEdit}
                disabled={updating}
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-red-500"
                onClick={cancelEditing}
                disabled={updating}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center mt-1">
            <Icon className="h-4 w-4 mr-2 text-muted-foreground" />
            <span>{displayValue}</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">My Profile</h1>
        <Button asChild variant="outline" className="mt-4 md:mt-0">
          <Link to="/settings/profile">
            <Edit className="mr-2 h-4 w-4" />
            Edit Profile
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Summary Card */}
        <Card className="lg:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={staff?.imageUrl || "https://github.com/shadcn.png"} />
                <AvatarFallback className="text-2xl">
                  {user?.firstName?.charAt(0)}
                  {user?.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-xl">
              {user?.firstName} {user?.middleName} {user?.lastName}
            </CardTitle>
            <CardDescription className="flex justify-center mt-2">
              <Badge variant="outline">{getUserTypeDisplay()}</Badge>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">{user?.email}</span>
              </div>

              <EditableField
                label="Phone Number"
                value={user?.phoneNumber}
                fieldKey="phoneNumber"
                type="user"
                icon={Phone}
                inputType="tel"
              />

              <EditableField
                label="Address"
                value={user?.address}
                fieldKey="address"
                type="user"
                icon={MapPin}
              />

              <EditableField
                label="Date of Birth"
                value={user?.dob ? formatDate(user.dob) : undefined}
                fieldKey="dob"
                type="user"
                icon={Calendar}
                inputType="date"
              />

              {staff?.employmentDate && (
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm">Employed since {formatDate(staff.employmentDate)}</span>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link to="/settings/security">
                <Key className="mr-2 h-4 w-4" />
                Security Settings
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Main Content Area */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="clinic">Clinic</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            {/* Details Tab */}
            <TabsContent value="details" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <EditableField
                        label="First Name"
                        value={user?.firstName}
                        fieldKey="firstName"
                        type="user"
                        icon={User}
                      />
                    </div>
                    <div>
                      <EditableField
                        label="Middle Name"
                        value={user?.middleName}
                        fieldKey="middleName"
                        type="user"
                        icon={User}
                      />
                    </div>
                    <div>
                      <EditableField
                        label="Last Name"
                        value={user?.lastName}
                        fieldKey="lastName"
                        type="user"
                        icon={User}
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                      <div className="flex items-center mt-1">
                        <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{user?.email}</span>
                      </div>
                    </div>
                    <div>
                      <EditableField
                        label="Phone"
                        value={user?.phoneNumber}
                        fieldKey="phoneNumber"
                        type="user"
                        icon={Phone}
                        inputType="tel"
                      />
                    </div>
                    <div>
                      <EditableField
                        label="Date of Birth"
                        value={user?.dob ? formatDate(user.dob) : undefined}
                        fieldKey="dob"
                        type="user"
                        icon={Calendar}
                        inputType="date"
                      />
                    </div>
                    <div>
                      <EditableField
                        label="Address"
                        value={user?.address}
                        fieldKey="address"
                        type="user"
                        icon={MapPin}
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Role</h3>
                      <div className="flex items-center mt-1">
                        <Shield className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{getUserTypeDisplay()}</span>
                      </div>
                    </div>
                  </div>

                  {staff && (
                    <>
                      <Separator className="my-4" />
                      <h3 className="text-lg font-semibold mb-3">Staff Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <EditableField
                            label="Job Title"
                            value={staff.jobTitle}
                            fieldKey="jobTitle"
                            type="staff"
                            icon={Briefcase}
                          />
                        </div>
                        <div>
                          <EditableField
                            label="Employment Date"
                            value={staff.employmentDate ? formatDate(staff.employmentDate) : undefined}
                            fieldKey="employmentDate"
                            type="staff"
                            icon={Calendar}
                            inputType="date"
                          />
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Role</h3>
                          <div className="flex items-center mt-1">
                            <Shield className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{staff.role || "Not specified"}</span>
                          </div>
                        </div>
                        <div>
                          <EditableField
                            label="Specialization"
                            value={staff.specialization}
                            fieldKey="specialization"
                            type="staff"
                            icon={User}
                          />
                        </div>
                        {staff.bio !== undefined && (
                          <div className="md:col-span-2">
                            <EditableField
                              label="Bio"
                              value={staff.bio}
                              fieldKey="bio"
                              type="staff"
                              icon={User}
                            />
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Clinic Tab */}
            <TabsContent value="clinic" className="mt-4">
              {loading ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                </Card>
              ) : staff?.isClinicOwner && mainClinic ? (
                <ClinicProfileManager
                  clinic={mainClinic}
                  onUpdate={setMainClinic}
                  isOwner={true}
                />
              ) : currentClinic ? (
                <ClinicProfileManager
                  clinic={currentClinic}
                  onUpdate={() => {}} // Read-only for non-owners
                  isOwner={false}
                />
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Building2 className="h-12 w-12 mx-auto text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">No Clinic Information</h3>
                      <p className="text-muted-foreground mt-2">
                        You are not currently associated with any clinic.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="mt-4">
              {staff ? (
                <RolePermissionManager
                  staff={staff}
                  onUpdate={updateStaff}
                  isCurrentUser={true}
                />
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <Shield className="h-12 w-12 mx-auto text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">No Permission Information</h3>
                      <p className="text-muted-foreground mt-2">
                        Permission management is only available for staff members.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Activity Tab */}
            <TabsContent value="activity" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="text-sm">Last login: {user?.lastLogin ? formatDate(user.lastLogin) : "Unknown"}</span>
                    </div>
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="text-sm">Login count: {user?.loginCount || 0}</span>
                    </div>

                    {staff?.clinicActivity && staff.clinicActivity.length > 0 && (
                      <div className="mt-6">
                        <h3 className="text-sm font-medium mb-2">Clinic Activity</h3>
                        <div className="space-y-3">
                          {staff.clinicActivity.map((activity, index) => {
                            const clinic = clinics.find(c => c._id === activity.clinicId);
                            return (
                              <div key={index} className="flex justify-between items-center p-2 rounded-md bg-muted/50">
                                <div className="flex items-center">
                                  <Building2 className="h-4 w-4 mr-2 text-primary" />
                                  <span>{clinic?.clinicName || activity.clinicId}</span>
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Last active: {formatDate(activity.lastActive)}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ViewProfile;
