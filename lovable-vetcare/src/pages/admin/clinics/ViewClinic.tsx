import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate, useParams } from "react-router-dom";
import { getClinic, getClinicStaff } from "@/services/clinics";
import { useToast } from "@/components/ui/use-toast";
import { useQuery } from "@tanstack/react-query";
import LoadingPage from "@/components/common/LoadingPage";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { UserPlus, Edit, ArrowLeft, Building2, Mail, Phone, MapPin } from "lucide-react";
import AddStaffToClinicModal from "./components/AddStaffToClinicModal";
import { Badge } from "@/components/ui/badge";

const ViewClinic = () => {
  const { clinicId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isAddStaffModalOpen, setIsAddStaffModalOpen] = useState(false);

  // Fetch clinic details
  const {
    data: clinicResponse,
    isLoading: isClinicLoading,
    error: clinicError
  } = useQuery({
    queryKey: ["clinic", clinicId],
    queryFn: () => getClinic(clinicId!),
    enabled: !!clinicId,
  });

  // Fetch clinic staff using the clinic data
  const {
    data: staffResponse,
    isLoading: isStaffLoading,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ["clinic", clinicId, "staff"],
    queryFn: () => getClinicStaff(clinicId!),
    enabled: !!clinicId && !!clinicResponse?.data,
    retry: 2,
    retryDelay: 1000,
  });

  const clinic = clinicResponse?.data || null;
  const staff = staffResponse?.data?.data || [];

  // Debug logs
  console.log("Clinic data:", clinic);
  console.log("Staff data:", staff);
  console.log("Staff response:", staffResponse);

  if (isClinicLoading) {
    return <LoadingPage />;
  }

  if (clinicError) {
    toast({
      title: "Error",
      description: "Failed to fetch clinic details",
      variant: "destructive",
    });
  }

  if (!clinic) {
    return (
      <div className="p-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p>Clinic not found</p>
              <Button className="mt-4" onClick={() => navigate("/admin/clinics")}>
                Back to Clinics
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8">
      <Card className="mb-8">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" onClick={() => navigate("/admin/clinics")} className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <CardTitle>Clinic Details</CardTitle>
          </div>
          <Button variant="outline" onClick={() => navigate(`/admin/clinics/edit/${clinicId}`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Clinic
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex items-center mb-4">
                <Building2 className="h-6 w-6 mr-2 text-primary" />
                <h2 className="text-2xl font-bold">{clinic.clinicName}</h2>
              </div>
              <div className="space-y-3">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                  <p className="text-muted-foreground">{clinic.address}</p>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-2 text-muted-foreground" />
                  <p className="text-muted-foreground">{clinic.phoneNumber}</p>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
                  <p className="text-muted-foreground">{clinic.email}</p>
                </div>
              </div>
            </div>
            <div className="bg-muted p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Clinic Information</h3>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <Badge variant={clinic.status === 1 ? "success" : "destructive"}>
                    {clinic.status === 1 ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Clinic ID</p>
                  <p>{clinic.clinicId}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Staff Members</CardTitle>
          <AddStaffToClinicModal
            clinicId={clinic.clinicId?.toString() || clinic._id?.toString() || ""}
            clinicName={clinic.clinicName || ""}
            clinicData={clinic}
            open={isAddStaffModalOpen}
            onOpenChange={setIsAddStaffModalOpen}
            onSuccess={() => refetchStaff()}
            trigger={
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Add Staff
              </Button>
            }
          />
        </CardHeader>
        <CardContent>
          {isStaffLoading ? (
            <div className="text-center py-4">Loading staff...</div>
          ) : staff.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No staff members found for this clinic</p>
              <Button onClick={() => setIsAddStaffModalOpen(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                Add Staff
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {staff.map((staffMember) => (
                  <TableRow key={staffMember._id}>
                    <TableCell className="font-medium">
                      {staffMember.firstName} {staffMember.middleName ? staffMember.middleName + ' ' : ''}{staffMember.lastName}
                      {staffMember.isClinicOwner && (
                        <Badge variant="outline" className="ml-2">Owner</Badge>
                      )}
                    </TableCell>
                    <TableCell>{staffMember.email}</TableCell>
                    <TableCell>{staffMember.phoneNumber}</TableCell>
                    <TableCell>
                      {staffMember.roleId && typeof staffMember.roleId === 'object'
                        ? staffMember.roleId.name
                        : 'Unknown Role'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={staffMember.status === 1 ? "success" : "destructive"}>
                        {staffMember.status === 1 ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ViewClinic;
