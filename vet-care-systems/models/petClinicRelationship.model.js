import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * PetClinicRelationship Model
 *
 * This model represents the relationship between a pet and a clinic.
 * It tracks the history of pet visits to specific clinics and
 * stores clinic-specific pet information.
 */
const petClinicRelationshipSchema = new mongoose.Schema({
    // Auto-increment ID
    petClinicRelationshipId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Core relationship identifiers
    petId: {
        type: Number,
        ref: 'Pet',
        required: [true, "Pet ID is required"],
        index: true
    },
    clinicId: {
        type: Number,
        ref: 'Clinic',
        required: [true, "Clinic ID is required"],
        index: true
    },

    // Relationship metadata
    relationshipStatus: {
        type: String,
        enum: ['active', 'inactive', 'deceased'],
        default: 'active'
    },

    // First interaction information
    firstVisitDate: {
        type: Date,
        default: Date.now
    },
    registeredBy: {
        type: Number,
        ref: 'Staff'
    },

    // Visit history
    visitCount: {
        type: Number,
        default: 1
    },
    lastVisitDate: {
        type: Date,
        default: Date.now
    },

    // Clinic-specific pet information
    clinicNotes: {
        type: String,
        trim: true
    },

    // Medical information specific to this clinic
    medicalAlerts: [{
        alertType: {
            type: String,
            enum: ['allergy', 'condition', 'medication', 'behavior', 'other'],
            required: true
        },
        description: {
            type: String,
            required: true,
            trim: true
        },
        severity: {
            type: String,
            enum: ['low', 'medium', 'high', 'critical'],
            default: 'medium'
        },
        dateIdentified: {
            type: Date,
            default: Date.now
        },
        notes: {
            type: String,
            trim: true
        },
        isActive: {
            type: Boolean,
            default: true
        }
    }],

    // Treatment plans specific to this clinic
    treatmentPlans: [{
        name: {
            type: String,
            required: true,
            trim: true
        },
        description: {
            type: String,
            trim: true
        },
        startDate: {
            type: Date,
            required: true
        },
        endDate: Date,
        status: {
            type: String,
            enum: ['planned', 'in_progress', 'completed', 'cancelled'],
            default: 'planned'
        },
        createdBy: {
            type: Number,
            ref: 'Staff'
        },
        treatments: [{
            treatmentType: {
                type: String,
                required: true
            },
            instructions: String,
            frequency: String,
            duration: String,
            completed: {
                type: Boolean,
                default: false
            }
        }]
    }],

    // Vaccination records specific to this clinic
    vaccinationRecords: [{
        vaccineType: {
            type: String,
            required: true
        },
        administeredDate: {
            type: Date,
            required: true
        },
        expiryDate: Date,
        batchNumber: String,
        administeredBy: {
            type: Number,
            ref: 'Staff'
        },
        notes: String
    }],

    // Weight history at this clinic
    weightHistory: [{
        weight: {
            type: Number,
            required: true
        },
        date: {
            type: Date,
            default: Date.now
        },
        notes: String
    }],

    // Custom fields that clinics can define
    customFields: {
        type: Map,
        of: mongoose.Schema.Types.Mixed
    }
}, {
    timestamps: true,
    collection: 'petClinicRelationships'
});

// Create a compound index for pet-clinic pairs
petClinicRelationshipSchema.index({ petId: 1, clinicId: 1 }, { unique: true });

// Create indexes for common queries
petClinicRelationshipSchema.index({ clinicId: 1, relationshipStatus: 1 });
petClinicRelationshipSchema.index({ lastVisitDate: -1 });
petClinicRelationshipSchema.index({ 'medicalAlerts.isActive': 1 });

// Add auto-increment plugin
petClinicRelationshipSchema.plugin(AutoIncrement, { inc_field: 'petClinicRelationshipId', start_seq: 1001 });
petClinicRelationshipSchema.index({ petClinicRelationshipId: 1 }, { unique: true });

const PetClinicRelationship = mongoose.model('PetClinicRelationship', petClinicRelationshipSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = PetClinicRelationship.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await PetClinicRelationship.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default PetClinicRelationship;
