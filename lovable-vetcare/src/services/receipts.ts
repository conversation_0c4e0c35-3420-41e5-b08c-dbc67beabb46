import { api } from './api';

export interface Receipt {
  receiptId: number;
  receiptNumber: string;
  healthRecordId?: number;
  appointmentId: number;
  clientId: number;
  petId: number;
  clinicId: number;
  services: Array<{
    serviceId: number;
    serviceName: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  subtotal: number;
  afterHoursCharge: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  discounts: Array<{
    discountId: number;
    discountType: 'percentage' | 'fixed';
    discountValue: number;
    discountAmount: number;
    reason?: string;
  }>;
  totalDiscounts: number;
  amountPaid: number;
  paymentMethod: string;
  paymentReference: string;
  paymentDate: string;
  issuedBy: number;
  issuedDate: string;
  status: 'issued' | 'cancelled';
  clientData?: any;
  petData?: any;
  staffData?: any;
  createdAt: string;
  updatedAt: string;
}

export interface ReceiptResponse {
  success: boolean;
  status: number;
  message: string;
  data: Receipt | null;
}

/**
 * Get receipt by ID
 */
export const getReceiptById = async (receiptId: string): Promise<ReceiptResponse> => {
  try {
    const response = await api.get(`/receipts/${receiptId}`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch receipt',
      data: null
    };
  }
};

/**
 * Get receipt by appointment ID
 */
export const getReceiptByAppointment = async (appointmentId: number): Promise<ReceiptResponse> => {
  try {
    const response = await api.get(`/receipts/appointment/${appointmentId}`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch receipt',
      data: null
    };
  }
};
