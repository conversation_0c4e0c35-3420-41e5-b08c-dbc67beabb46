import Permission from '../models/permission.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Create a new permission
 */
export const createPermission = async (req, res) => {
    try {
        const { name, description, module } = req.body;

        // Check if permission already exists
        const existingPermission = await Permission.findOne({ name }).lean();
        if (existingPermission) {
            return sendResponse(res, 409, false, "Permission already exists");
        }

        // Create new permission
        const permission = await Permission.create({
            name,
            description,
            module,
            status: 1
        });

        return sendResponse(res, 201, true, "Permission created successfully", permission);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all permissions with pagination, sorting and filtering
 */
export const getAllPermissions = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "name",
            sortOrder = "asc",
            search,
            status,
            module
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        // Build query based on filters
        let query = {};
        if (status !== undefined) query.status = parseInt(status);
        if (module) query.module = module;
        if (search) {
            query.$or = [
                { permissionId: { $regex: search, $options: "i" } },
                { name: { $regex: search, $options: "i" } },
                { description: { $regex: search, $options: "i" } }
            ];
        }

        // Execute query with pagination
        const [permissions, totalCount] = await Promise.all([
            Permission.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Permission.countDocuments(query)
        ]);

        const totalPages = Math.ceil(totalCount / parseInt(limit));

        return sendResponse(res, 200, true, "Permissions retrieved successfully", {
            data:permissions,
            pagination: {
                total: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages
            }
        });
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get permission by ID
 */
export const getPermissionById = async (req, res) => {
    try {
        const { permissionId } = req.params;

        const permission = await Permission.findOne({ permissionId: parseInt(permissionId) }).lean();
        if (!permission) {
            return sendResponse(res, 404, false, "Permission not found");
        }

        return sendResponse(res, 200, true, "Permission retrieved successfully", permission);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update permission
 */
export const updatePermission = async (req, res) => {
    try {
        const { permissionId } = req.params;
        const { name, description, module, status } = req.body;

        // Check if permission exists
        const permission = await Permission.findOne({ permissionId: parseInt(permissionId) });
        if (!permission) {
            return sendResponse(res, 404, false, "Permission not found");
        }

        // Check if name is being changed and if it already exists
        if (name && name !== permission.name) {
            const existingPermission = await Permission.findOne({ name }).lean();
            if (existingPermission) {
                return sendResponse(res, 409, false, "Permission name already exists");
            }
        }

        // Update permission
        const updatedPermission = await Permission.findOneAndUpdate(
            { permissionId: parseInt(permissionId) },
            { $set: { name, description, module, status } },
            { new: true }
        ).lean();

        return sendResponse(res, 200, true, "Permission updated successfully", updatedPermission);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Delete permission
 */
export const deletePermission = async (req, res) => {
    try {
        const { permissionId } = req.params;

        // Check if permission exists
        const permission = await Permission.findOne({ permissionId: parseInt(permissionId) });
        if (!permission) {
            return sendResponse(res, 404, false, "Permission not found");
        }

        // Delete permission
        await Permission.deleteOne({ permissionId: parseInt(permissionId) });

        return sendResponse(res, 200, true, "Permission deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};


/**
 * Get permissions by module
 */
export const getPermissionsByModule = async (req, res) => {
    try {
        const { module } = req.params;

        const permissions = await Permission.find({
            module: module,
            status: 1
        }).lean();

        return sendResponse(res, 200, true, "Permissions retrieved successfully", permissions);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Initialize default permissions
 */
export const initializePermissions = async (req, res) => {
    try {
        await Permission.initializePermissions();
        return sendResponse(res, 200, true, "Permissions initialized successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};