
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw, Edit, UserPlus, MoreHorizontal, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { getClinics } from "@/services/clinics";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/store";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import LoadingPage from "@/components/common/LoadingPage";
import { Clinic } from "@/store/types";
import AddStaffToClinicModal from "./components/AddStaffToClinicModal";

const Clinics = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { staff, user } = useAuth();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);

  // Check if user is super admin
  const isAdmin = user?.role === 'super_admin';

  const { data: response, isLoading, error, refetch } = useQuery({
    queryKey: ["clinics", staff?.staffId],
    queryFn: () => {
      // Filter by ownerId if user is clinic owner and not super admin
      const params = staff?.isClinicOwner && !isAdmin ? { ownerId: staff.staffId } : {};
      return getClinics(params);
    },
  });

  const clinics = response?.data?.data || [];

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error) {
    toast({
      title: "Error",
      description: "Failed to fetch clinics",
      variant: "destructive",
    });
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Clinics</h1>
        <div className="flex gap-2">
          <Button onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" /> Refresh
          </Button>
          <Button onClick={() => navigate("/admin/clinics/add")}>
            <Plus className="mr-2 h-4 w-4" /> Add Clinic
          </Button>
        </div>
      </div>

      {!clinics || clinics.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">No clinics found</div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {clinics.map((clinic: Clinic) => (
                <TableRow key={clinic.clinicId || clinic._id}>
                  <TableCell>{clinic.clinicName}</TableCell>
                  <TableCell>{clinic.address}</TableCell>
                  <TableCell>{clinic.phoneNumber}</TableCell>
                  <TableCell>{clinic.email}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => navigate(`/admin/clinics/view/${clinic.clinicId || clinic._id}`)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Clinic
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => navigate(`/admin/clinics/edit/${clinic.clinicId || clinic._id}`)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Clinic
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <AddStaffToClinicModal
                            clinicId={clinic.clinicId?.toString() || clinic._id?.toString() || ""}
                            clinicName={clinic.clinicName || ""}
                            clinicData={clinic}
                            trigger={
                              <button className="w-full flex items-center px-2 py-1.5 text-sm">
                                <UserPlus className="mr-2 h-4 w-4" />
                                Add Staff
                              </button>
                            }
                          />
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default Clinics;
