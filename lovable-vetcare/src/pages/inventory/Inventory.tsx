
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Search, Filter, RefreshCw, Edit, Trash2, AlertCircle, MoreHorizontal } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getInventoryItems, deleteInventoryItem, InventoryItem } from "@/services/inventory";
import AddInventoryItemModal from "./components/AddInventoryItemModal";
import EditInventoryItemModal from "./components/EditInventoryItemModal";
import AdjustInventoryModal from "./components/AdjustInventoryModal";
import LoadingPage from "@/components/common/LoadingPage";

const Inventory = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAdjustModalOpen, setIsAdjustModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [showLowStock, setShowLowStock] = useState(false);

  // Fetch inventory items
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['inventory', currentPage, searchQuery, categoryFilter, statusFilter, showLowStock],
    queryFn: () => getInventoryItems({
      page: currentPage,
      limit: 10,
      search: searchQuery || undefined,
      category: categoryFilter || undefined,
      status: statusFilter || undefined,
      lowStock: showLowStock || undefined,
      sortBy: "name",
      sortOrder: "asc"
    })
  });

  const inventoryItems = data?.data?.data || [];
  const pagination = data?.data?.pagination || { totalCount: 0 };

  // Stats for summary cards
  const [stats, setStats] = useState({
    totalItems: 0,
    lowStockItems: 0,
    outOfStockItems: 0
  });

  // Calculate stats when data changes
  useEffect(() => {
    if (data?.data?.data) {
      const items = data.data.data;
      const lowStock = items.filter(item => item.quantity <= item.reorderLevel).length;
      const outOfStock = items.filter(item => item.quantity === 0).length;

      setStats({
        totalItems: data.data.pagination.totalCount,
        lowStockItems: lowStock,
        outOfStockItems: outOfStock
      });
    }
  }, [data]);

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteInventoryItem(id),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Inventory item deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete inventory item",
        variant: "destructive",
      });
    }
  });

  const handleDelete = (item: InventoryItem) => {
    if (confirm(`Are you sure you want to delete ${item.name}?`)) {
      deleteMutation.mutate(item._id);
    }
  };

  const handleEdit = (item: InventoryItem) => {
    setSelectedItem(item);
    setIsEditModalOpen(true);
  };

  const handleAdjust = (item: InventoryItem) => {
    setSelectedItem(item);
    setIsAdjustModalOpen(true);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  const handleRefresh = () => {
    refetch();
  };

  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      medication: "bg-blue-100 text-blue-800",
      vaccine: "bg-green-100 text-green-800",
      supply: "bg-purple-100 text-purple-800",
      equipment: "bg-yellow-100 text-yellow-800",
      food: "bg-orange-100 text-orange-800",
      other: "bg-gray-100 text-gray-800"
    };

    return (
      <Badge className={colors[category] || colors.other}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const colors: Record<string, string> = {
      active: "bg-green-100 text-green-800",
      discontinued: "bg-gray-100 text-gray-800",
      expired: "bg-red-100 text-red-800",
      recalled: "bg-orange-100 text-orange-800"
    };

    return (
      <Badge className={colors[status] || colors.other}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Item
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalItems}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.lowStockItems}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.outOfStockItems}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Inventory Items</CardTitle>
            <div className="flex gap-2">
              <form onSubmit={handleSearch}>
                <Input
                  placeholder="Search items..."
                  className="w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </form>
              <Button variant="outline" size="icon" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Unit Price</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inventoryItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No inventory items found
                  </TableCell>
                </TableRow>
              ) : (
                inventoryItems.map((item) => (
                  <TableRow key={item._id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{getCategoryBadge(item.category)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {item.quantity <= item.reorderLevel && (
                          <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                        )}
                        {item.quantity} {item.unit}
                      </div>
                    </TableCell>
                    <TableCell>{item.sellingPrice} {item.currency}</TableCell>
                    <TableCell>
                      {item.expiryDate ? format(new Date(item.expiryDate), 'dd/MM/yyyy') : 'N/A'}
                    </TableCell>
                    <TableCell>{getStatusBadge(item.status)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEdit(item)}>
                            <Edit className="h-4 w-4 mr-2" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleAdjust(item)}>
                            <RefreshCw className="h-4 w-4 mr-2" /> Adjust Stock
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDelete(item)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Modals */}
      <AddInventoryItemModal
        open={isAddModalOpen}
        onOpenChange={setIsAddModalOpen}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['inventory'] });
        }}
      />

      {selectedItem && (
        <>
          <EditInventoryItemModal
            open={isEditModalOpen}
            onOpenChange={setIsEditModalOpen}
            inventoryItem={selectedItem}
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: ['inventory'] });
              setSelectedItem(null);
            }}
          />

          <AdjustInventoryModal
            open={isAdjustModalOpen}
            onOpenChange={setIsAdjustModalOpen}
            inventoryItem={selectedItem}
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: ['inventory'] });
              setSelectedItem(null);
            }}
          />
        </>
      )}
    </div>
  );
};

export default Inventory;
