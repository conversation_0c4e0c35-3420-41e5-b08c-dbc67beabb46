/**
 * Test Setup File
 * 
 * This file contains setup code for running tests.
 * It configures the test environment and provides utility functions.
 */

import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import supertest from 'supertest';
import app from '../app.js';

// Create a supertest client
export const request = supertest(app);

// MongoDB Memory Server instance
let mongoServer;

/**
 * Connect to the in-memory database before tests run
 */
export const setupTestDB = async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  
  await mongoose.connect(uri);
};

/**
 * Clear all test data after each test
 */
export const clearTestData = async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
};

/**
 * Remove and close the db and server after tests run
 */
export const teardownTestDB = async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
};

/**
 * Create a test user and get auth token
 */
export const getAuthToken = async (userData = {}) => {
  // Default test user
  const defaultUser = {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'Password123!',
    roleId: 1001 // Admin role
  };
  
  // Create user
  await request.post('/api/v1/auth/sign-up').send({
    ...defaultUser,
    ...userData
  });
  
  // Login and get token
  const loginResponse = await request.post('/api/v1/auth/sign-in').send({
    email: userData.email || defaultUser.email,
    password: userData.password || defaultUser.password
  });
  
  return loginResponse.body.data.token;
};
