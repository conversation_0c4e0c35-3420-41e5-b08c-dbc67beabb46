
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useStore } from '@/store';

// Define API base URL from environment variables
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Define non-authenticated endpoints
const publicEndpoints = [
  '/auth/login',
  '/auth/sign-in',
  '/auth/register',
  '/auth/sign-up',
  '/auth/forgot-password',
  '/auth/reset-password'
];

// Create a custom axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable cookies for CSRF token
});

// Request interceptor for adding auth token and CSRF token
apiClient.interceptors.request.use(
  async (config) => {
    // Skip token for public endpoints
    const isPublicEndpoint = publicEndpoints.some(endpoint =>
      config.url?.includes(endpoint)
    );

    if (!isPublicEndpoint) {
      const token = useStore.getState().token;
      if (token && token !== 'null' && token !== 'undefined' && token.trim() !== '') {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    // Add CSRF token for non-GET requests
    if (config.method && config.method.toUpperCase() !== 'GET') {
      try {
        // Get CSRF token from meta tag or cookie
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         getCookie('_csrf');

        if (csrfToken) {
          config.headers['X-CSRF-Token'] = csrfToken;
        }
      } catch (error) {
        console.warn('Failed to get CSRF token:', error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Helper function to get cookie value
function getCookie(name: string): string | null {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
}

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors (token expired or missing)
    if (error.response?.status === 401 ||
        (error.response?.data?.success === false &&
         error.response?.data?.status === 401)) {

      const errorMessage = error.response?.data?.message || '';

      // Check for specific auth error messages
      if (errorMessage === 'Token expired' ||
          errorMessage === 'No token provided' ||
          errorMessage === 'Invalid token') {
        console.log(`Auth error: ${errorMessage} - logging out`);
        // Logout user automatically
        useStore.getState().logout();
      }
    }

    // Handle other common errors
    return Promise.reject(error);
  }
);

// Generic API request method
export const apiRequest = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient(config);

    // Check if the response contains an auth error message
    if (response.data &&
        typeof response.data === 'object' &&
        'success' in response.data &&
        'status' in response.data &&
        'message' in response.data) {

      const data = response.data as any;
      if (data.success === false && data.status === 401) {
        const errorMessage = data.message || '';

        // Check for specific auth error messages
        if (errorMessage === 'Token expired' ||
            errorMessage === 'No token provided' ||
            errorMessage === 'Invalid token') {
          console.log(`Auth error in response data: ${errorMessage} - logging out`);
          useStore.getState().logout();
        }
      }
    }

    return response.data;
  } catch (error: any) {
    // Check for 401 errors in the catch block
    if (error.response?.status === 401) {
      const errorMessage = error.response?.data?.message || '';

      // Check for specific auth error messages
      if (errorMessage === 'Token expired' ||
          errorMessage === 'No token provided' ||
          errorMessage === 'Invalid token') {
        console.log(`Auth error in catch block: ${errorMessage} - logging out`);
        useStore.getState().logout();
      }
    }

    // Standardize error handling
    const errorResponse = {
      success: false,
      status: error.response?.status || 500,
      message: error.response?.data?.message || 'An error occurred',
      data: error.response?.data || null
    };
    throw errorResponse;
  }
};

// Function to initialize CSRF token
export const initializeCSRF = async (): Promise<void> => {
  try {
    // Make a GET request to get CSRF token (this should set the cookie)
    await apiClient.get('/csrf-token');
  } catch (error) {
    console.warn('Failed to initialize CSRF token:', error);
  }
};

// Export common API methods
export const api = {
  get: <T>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'GET', url }),

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'POST', url, data }),

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'PUT', url, data }),

  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'PATCH', url, data }),

  delete: <T>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'DELETE', url })
};

// Re-export all services
export * from './staff';
export * from './clients';
export * from './pets';
export * from './clinics';
export * from './appointments';
export * from './species.ts';
export * from './roles';
export * from './permissions';
export * from './breeds';
