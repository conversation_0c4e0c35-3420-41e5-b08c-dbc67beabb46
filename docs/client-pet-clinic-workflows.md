# Client-Pet-Clinic Workflows

This document outlines the key workflows implemented in the Vet-Care SaaS system to support the client-pet-clinic relationship model where:

1. Clients own their pet data
2. Clinics maintain records of clients and pets they've interacted with
3. Clients can visit any clinic with their complete pet medical history
4. Clinics can track appointments, medical records, and inventory for pets they've treated

## 1. Client Registration Workflow

### New Client Registration at a Clinic

```mermaid
sequenceDiagram
    participant Client
    participant Clinic
    participant System
    
    Client->>Clinic: Visits clinic and provides information
    Clinic->>System: Creates client account
    System->>System: Creates Client record
    System->>System: Creates ClientClinicRelationship
    System->>Client: Sends account credentials
    System->>Clinic: Confirms client registration
```

### Implementation Details

1. **Client Creation**:
   - Client provides personal information to clinic
   - Clinic staff creates client account in the system
   - System creates a global Client record
   - System automatically creates a ClientClinicRelationship record

2. **Data Ownership**:
   - Client owns their personal data
   - Client can update their information from any clinic
   - Changes to client information are reflected across all clinics

3. **API Endpoints**:
   - `POST /api/v1/clients` - Create a new client
   - `POST /api/v1/client-clinic-relationships` - Create relationship (automatic)

## 2. Pet Registration Workflow

### Adding a Pet to a Client's Profile

```mermaid
sequenceDiagram
    participant Client
    participant Clinic
    participant System
    
    Client->>Clinic: Brings pet and provides information
    Clinic->>System: Creates pet record
    System->>System: Links pet to client
    System->>System: Creates PetClinicRelationship
    System->>Client: Confirms pet registration
    System->>Clinic: Updates clinic pet registry
```

### Implementation Details

1. **Pet Creation**:
   - Client brings pet to clinic
   - Clinic staff creates pet record in the system
   - System links pet to client (ownerId)
   - System automatically creates a PetClinicRelationship record

2. **Data Ownership**:
   - Client owns their pet's data
   - Pet's basic information is accessible to all clinics (with consent)
   - Pet's medical history is accessible to all clinics (with consent)

3. **API Endpoints**:
   - `POST /api/v1/pets` - Create a new pet
   - `POST /api/v1/pet-clinic-relationships` - Create relationship (automatic)

## 3. Clinic Visit Workflow

### Client Visits a New Clinic with Their Pet

```mermaid
sequenceDiagram
    participant Client
    participant NewClinic
    participant System
    
    Client->>NewClinic: Visits with pet
    NewClinic->>System: Searches for client/pet
    System->>NewClinic: Returns client/pet information
    NewClinic->>System: Creates ClientClinicRelationship (if new)
    NewClinic->>System: Creates PetClinicRelationship (if new)
    System->>NewClinic: Returns complete pet medical history
    NewClinic->>System: Creates new health records as needed
```

### Implementation Details

1. **Client/Pet Identification**:
   - Client visits a new clinic with their pet
   - Clinic searches for client by email, phone, or name
   - System returns client and associated pets

2. **Relationship Creation**:
   - If first visit to this clinic, system creates:
     - New ClientClinicRelationship
     - New PetClinicRelationship
   - If returning visit, system updates existing relationships

3. **Medical History Access**:
   - New clinic can access complete pet medical history
   - History shows which clinic created each record
   - New clinic can add new records that become part of the global history

4. **API Endpoints**:
   - `GET /api/v1/clients?search=<EMAIL>` - Find client
   - `GET /api/v1/pets?ownerId=123` - Find client's pets
   - `POST /api/v1/client-clinic-relationships` - Create client relationship
   - `POST /api/v1/pet-clinic-relationships` - Create pet relationship

## 4. Health Record Creation Workflow

### Creating a New Health Record for a Pet

```mermaid
sequenceDiagram
    participant Vet
    participant Clinic
    participant System
    participant PetRecord
    
    Vet->>Clinic: Examines pet and records findings
    Clinic->>System: Creates health record
    System->>System: Links record to pet
    System->>System: Links record to clinic
    System->>System: Links record to PetClinicRelationship
    System->>PetRecord: Updates pet's global medical history
    System->>Clinic: Confirms record creation
```

### Implementation Details

1. **Record Creation**:
   - Vet examines pet and records findings
   - Clinic creates health record in the system
   - System links record to:
     - Pet (petId)
     - Clinic (clinicId)
     - PetClinicRelationship (petClinicRelationshipId)

2. **Data Attribution**:
   - Record shows which clinic and vet created it
   - Record becomes part of pet's global medical history
   - Original clinic maintains ownership of the record

3. **Access Control**:
   - By default, records are accessible to all clinics
   - Sensitive records can be marked as restricted
   - Client can control sharing preferences

4. **API Endpoints**:
   - `POST /api/v1/health-records` - Create a new health record
   - `GET /api/v1/health-records?petId=123` - Get pet's medical history

## 5. Medical History Access Workflow

### Accessing a Pet's Complete Medical History

```mermaid
sequenceDiagram
    participant Vet
    participant Clinic
    participant System
    participant AllClinics
    
    Vet->>Clinic: Requests pet's medical history
    Clinic->>System: Queries for pet's health records
    System->>System: Checks access permissions
    System->>AllClinics: Retrieves records from all clinics
    AllClinics->>System: Returns authorized records
    System->>Clinic: Returns complete medical history
    Clinic->>Vet: Displays chronological medical history
```

### Implementation Details

1. **History Retrieval**:
   - Vet requests pet's medical history
   - System retrieves all health records for the pet
   - Records are filtered based on access permissions
   - Complete history is presented chronologically

2. **Minimal Data Fetching**:
   - System only fetches essential data needed for the view
   - Pagination is used for large histories
   - Detailed records are loaded on demand

3. **Data Presentation**:
   - Records show which clinic created them
   - Records are categorized by type (exam, vaccination, etc.)
   - Timeline view shows progression of pet's health

4. **API Endpoints**:
   - `GET /api/v1/health-records?petId=123&page=1&limit=20` - Paginated history
   - `GET /api/v1/health-records/:recordId` - Get specific record details

## 6. Client Data Management Workflow

### Client Updates Their Information

```mermaid
sequenceDiagram
    participant Client
    participant AnyClinic
    participant System
    participant AllClinics
    
    Client->>AnyClinic: Updates personal information
    AnyClinic->>System: Submits updated client data
    System->>System: Updates global client record
    System->>AllClinics: Propagates changes to all relationships
    System->>Client: Confirms information update
```

### Implementation Details

1. **Data Update**:
   - Client can update their information at any clinic
   - System updates the global client record
   - Changes are reflected in all clinic relationships

2. **Consent Management**:
   - Client can update data sharing preferences
   - Client can control which clinics see what information
   - Changes to consent are applied immediately

3. **API Endpoints**:
   - `PUT /api/v1/clients/:clientId` - Update client information
   - `PUT /api/v1/clients/:clientId/sharing-preferences` - Update sharing preferences

## 7. Clinic-Specific Data Workflow

### Managing Clinic-Specific Information

```mermaid
sequenceDiagram
    participant Clinic
    participant System
    participant ClientRelationship
    participant PetRelationship
    
    Clinic->>System: Adds clinic-specific notes/data
    System->>ClientRelationship: Updates ClientClinicRelationship
    System->>PetRelationship: Updates PetClinicRelationship
    System->>Clinic: Confirms data update
```

### Implementation Details

1. **Clinic-Specific Data**:
   - Clinics can add notes specific to their relationship with client/pet
   - This data is stored in the relationship models, not the global records
   - Each clinic maintains their own view of the client/pet

2. **Custom Fields**:
   - Clinics can define custom fields for their specific needs
   - Custom data is stored in the relationship models
   - Custom data doesn't affect the global client/pet records

3. **API Endpoints**:
   - `PUT /api/v1/client-clinic-relationships/:relationshipId` - Update client relationship
   - `PUT /api/v1/pet-clinic-relationships/:relationshipId` - Update pet relationship
