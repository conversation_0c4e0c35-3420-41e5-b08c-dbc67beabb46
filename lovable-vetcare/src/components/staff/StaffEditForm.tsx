import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Briefcase,
  Shield,
  Clock,
  Save,
  X,
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign
} from "lucide-react";
import { motion } from "framer-motion";
import { api } from "@/services/api";
import { Staff, Role, Permission } from "@/store/types";

interface StaffEditFormProps {
  staff: Staff;
  onSave: (updatedStaff: Staff) => void;
  onCancel: () => void;
}

const WORK_DAYS = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' }
];

export const StaffEditForm = ({ staff, onSave, onCancel }: StaffEditFormProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [clinics, setClinics] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: staff.firstName || "",
    middleName: staff.middleName || "",
    lastName: staff.lastName || "",
    email: staff.email || "",
    phoneNumber: staff.phoneNumber || "",
    address: staff.address || "",
    dob: staff.dob ? new Date(staff.dob).toISOString().split('T')[0] : "",

    // Employment Info
    clinicId: staff.clinicId || 0,
    roleId: staff.roleId || 0,
    jobTitle: staff.jobTitle || "",
    employmentDate: staff.employmentDate ? new Date(staff.employmentDate).toISOString().split('T')[0] : "",
    salary: staff.salary || 0,

    // Schedule
    schedule: {
      workDays: staff.schedule?.workDays || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      workHours: staff.schedule?.workHours || { start: '08:00', end: '17:00' }
    },

    // Emergency Contact
    emergencyContact: {
      name: staff.emergencyContact?.name || "",
      relationship: staff.emergencyContact?.relationship || "",
      phoneNumber: staff.emergencyContact?.phoneNumber || ""
    },

    // Permissions
    specialPermissions: staff.specialPermissions || [],
    revokedPermissions: staff.revokedPermissions || []
  });

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [rolesRes, permissionsRes, clinicsRes] = await Promise.all([
          api.get('/roles'),
          api.get('/permissions'),
          api.get('/clinics')
        ]);

        if (rolesRes.data?.success) {
          setRoles(rolesRes.data.data.data || []);
        }
        if (permissionsRes.data?.success) {
          setPermissions(permissionsRes.data.data.data || []);
        }
        if (clinicsRes.data?.success) {
          setClinics(clinicsRes.data.data.data || []);
        }
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch required data",
        });
      }
    };

    fetchData();
  }, [toast]);

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value
      }
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const response = await api.put(`/staff/${staff.staffId}/profile`, formData);

      if (response.data?.success) {
        toast({
          title: "Success!",
          description: "Staff member updated successfully",
        });
        onSave(response.data.data);
      } else {
        throw new Error(response.data?.message || "Failed to update staff member");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update staff member",
      });
    } finally {
      setLoading(false);
    }
  };

  const selectedRole = roles.find(role => role.roleId === formData.roleId);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="max-w-4xl mx-auto p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Staff Member</h1>
          <p className="text-gray-600">Update staff information, role, and permissions</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Form Tabs */}
      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="personal">Personal</TabsTrigger>
          <TabsTrigger value="employment">Employment</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => updateFormData('firstName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="middleName">Middle Name</Label>
                  <Input
                    id="middleName"
                    value={formData.middleName}
                    onChange={(e) => updateFormData('middleName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => updateFormData('lastName', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number *</Label>
                  <Input
                    id="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={(e) => updateFormData('phoneNumber', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => updateFormData('address', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dob">Date of Birth</Label>
                  <Input
                    id="dob"
                    type="date"
                    value={formData.dob}
                    onChange={(e) => updateFormData('dob', e.target.value)}
                  />
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <Phone className="mr-2 h-5 w-5" />
                  Emergency Contact
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="emergencyName">Contact Name</Label>
                    <Input
                      id="emergencyName"
                      value={formData.emergencyContact.name}
                      onChange={(e) => updateNestedFormData('emergencyContact', 'name', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyRelationship">Relationship</Label>
                    <Input
                      id="emergencyRelationship"
                      value={formData.emergencyContact.relationship}
                      onChange={(e) => updateNestedFormData('emergencyContact', 'relationship', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyPhone">Phone Number</Label>
                    <Input
                      id="emergencyPhone"
                      value={formData.emergencyContact.phoneNumber}
                      onChange={(e) => updateNestedFormData('emergencyContact', 'phoneNumber', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employment" className="mt-6">
          <EmploymentTab
            formData={formData}
            updateFormData={updateFormData}
            roles={roles}
            clinics={clinics}
          />
        </TabsContent>

        <TabsContent value="permissions" className="mt-6">
          <PermissionsTab
            formData={formData}
            updateFormData={updateFormData}
            selectedRole={selectedRole}
            permissions={permissions}
          />
        </TabsContent>

        <TabsContent value="schedule" className="mt-6">
          <ScheduleTab
            formData={formData}
            updateNestedFormData={updateNestedFormData}
          />
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

// Tab Components
const EmploymentTab = ({ formData, updateFormData, roles, clinics }: any) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <Briefcase className="mr-2 h-5 w-5" />
        Employment Details
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="clinicId">Clinic *</Label>
          <Select
            value={formData.clinicId.toString()}
            onValueChange={(value) => updateFormData('clinicId', parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a clinic" />
            </SelectTrigger>
            <SelectContent>
              {clinics.map((clinic: any) => (
                <SelectItem key={clinic.clinicId} value={clinic.clinicId.toString()}>
                  <div className="flex items-center">
                    <Building2 className="mr-2 h-4 w-4" />
                    {clinic.clinicName}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="roleId">Role *</Label>
          <Select
            value={formData.roleId.toString()}
            onValueChange={(value) => updateFormData('roleId', parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              {roles.map((role: any) => (
                <SelectItem key={role.roleId} value={role.roleId.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span>{role.name}</span>
                    <Badge variant="outline" className="ml-2">
                      {role.category}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="jobTitle">Job Title *</Label>
          <Input
            id="jobTitle"
            value={formData.jobTitle}
            onChange={(e) => updateFormData('jobTitle', e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="employmentDate">Employment Date *</Label>
          <Input
            id="employmentDate"
            type="date"
            value={formData.employmentDate}
            onChange={(e) => updateFormData('employmentDate', e.target.value)}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="salary">Annual Salary (USD)</Label>
        <Input
          id="salary"
          type="number"
          value={formData.salary}
          onChange={(e) => updateFormData('salary', parseInt(e.target.value) || 0)}
        />
      </div>
    </CardContent>
  </Card>
);

const PermissionsTab = ({ formData, updateFormData, selectedRole, permissions }: any) => {
  const getEffectivePermissions = () => {
    const rolePermissions = selectedRole?.permissions || [];
    const effective = new Set([
      ...rolePermissions,
      ...formData.specialPermissions
    ]);

    // Remove revoked permissions
    formData.revokedPermissions.forEach((id: number) => effective.delete(id));

    return Array.from(effective);
  };

  const effectivePermissions = getEffectivePermissions();

  return (
    <div className="space-y-6">
      {/* Current Role */}
      {selectedRole && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Current Role: {selectedRole.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">{selectedRole.description}</p>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Role Permissions:</Label>
              <div className="flex flex-wrap gap-2">
                {selectedRole.permissions?.map((permId: number) => {
                  const permission = permissions.find((p: any) => p.permissionId === permId);
                  return permission ? (
                    <Badge key={permId} variant="default">
                      {permission.name}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Effective Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Effective Permissions ({effectivePermissions.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {effectivePermissions.map((permId) => {
              const permission = permissions.find((p: any) => p.permissionId === permId);
              return permission ? (
                <Badge key={permId} variant="default">
                  {permission.name}
                </Badge>
              ) : null;
            })}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Special Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Grant Special Permissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {permissions.map((permission: any) => {
                const isRolePermission = selectedRole?.permissions?.includes(permission.permissionId);
                const isSpecial = formData.specialPermissions.includes(permission.permissionId);

                if (isRolePermission) return null;

                return (
                  <div key={permission.permissionId} className="flex items-center space-x-2">
                    <Checkbox
                      id={`special-${permission.permissionId}`}
                      checked={isSpecial}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFormData('specialPermissions', [...formData.specialPermissions, permission.permissionId]);
                        } else {
                          updateFormData('specialPermissions', formData.specialPermissions.filter((id: number) => id !== permission.permissionId));
                        }
                      }}
                    />
                    <label
                      htmlFor={`special-${permission.permissionId}`}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {permission.name}
                      <span className="text-muted-foreground ml-1">({permission.module})</span>
                    </label>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Revoked Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Revoke Role Permissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {selectedRole?.permissions?.map((permId: number) => {
                const permission = permissions.find((p: any) => p.permissionId === permId);
                const isRevoked = formData.revokedPermissions.includes(permId);

                if (!permission) return null;

                return (
                  <div key={permId} className="flex items-center space-x-2">
                    <Checkbox
                      id={`revoke-${permId}`}
                      checked={isRevoked}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFormData('revokedPermissions', [...formData.revokedPermissions, permId]);
                        } else {
                          updateFormData('revokedPermissions', formData.revokedPermissions.filter((id: number) => id !== permId));
                        }
                      }}
                    />
                    <label
                      htmlFor={`revoke-${permId}`}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {permission.name}
                      <span className="text-muted-foreground ml-1">({permission.module})</span>
                    </label>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

const ScheduleTab = ({ formData, updateNestedFormData }: any) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <Clock className="mr-2 h-5 w-5" />
        Work Schedule
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="workStart">Work Hours Start</Label>
          <Input
            id="workStart"
            type="time"
            value={formData.schedule.workHours.start}
            onChange={(e) => updateNestedFormData('schedule', 'workHours', {
              ...formData.schedule.workHours,
              start: e.target.value
            })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="workEnd">Work Hours End</Label>
          <Input
            id="workEnd"
            type="time"
            value={formData.schedule.workHours.end}
            onChange={(e) => updateNestedFormData('schedule', 'workHours', {
              ...formData.schedule.workHours,
              end: e.target.value
            })}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Work Days</Label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {WORK_DAYS.map((day) => (
            <div key={day.key} className="flex items-center space-x-2">
              <Checkbox
                id={`workday-${day.key}`}
                checked={formData.schedule.workDays.includes(day.key)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    updateNestedFormData('schedule', 'workDays', [...formData.schedule.workDays, day.key]);
                  } else {
                    updateNestedFormData('schedule', 'workDays', formData.schedule.workDays.filter((d: string) => d !== day.key));
                  }
                }}
              />
              <label htmlFor={`workday-${day.key}`} className="text-sm cursor-pointer">
                {day.label}
              </label>
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);
