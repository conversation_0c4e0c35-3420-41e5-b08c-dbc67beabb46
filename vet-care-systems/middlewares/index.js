/**
 * Middleware Index
 *
 * This file centralizes all middleware exports to standardize imports across the application.
 * Middleware execution order is important for proper request processing.
 *
 * Recommended middleware execution order:
 * 1. Security middleware (CORS, helmet, etc.)
 * 2. Request parsing middleware (body-parser, etc.)
 * 3. Authentication middleware
 * 4. Route-specific middleware
 */

import { verifyToken } from '../middleware/auth.middleware.js';
import errorMiddleware from './error.middleware.js';
import arcjetMiddleware from './arcjet.middleware.js';
import { csrfProtection, handleCSRFError, provideCSRFToken } from './csrf.middleware.js';

// Export all middleware with consistent naming
export {
    verifyToken,         // Authentication middleware
    errorMiddleware,     // Global error handling middleware
    arcjetMiddleware,    // Rate limiting and bot protection middleware
    csrfProtection,      // CSRF protection middleware
    handleCSRFError,     // CSRF error handling middleware
    provideCSRFToken,    // CSRF token provider middleware
};

// Export default object for convenience
export default {
    verifyToken,
    errorMiddleware,
    arcjetMiddleware,
    csrfProtection,
    handleCSRFError,
    provideCSRFToken,
};
