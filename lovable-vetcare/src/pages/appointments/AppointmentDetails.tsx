import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Clock, User, Stethoscope, FileText, History, Receipt, Edit, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getAppointmentById, getAppointmentTypes } from '@/services/appointments';
import { getHealthRecordsByPet } from '@/services/healthRecords';
import { Appointment } from '@/store/types';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import CompleteAppointmentModal from './components/CompleteAppointmentModal';
import AppointmentNotes from '@/components/appointments/AppointmentNotes';
import AppointmentServices from '@/components/appointments/AppointmentServices';
import ReassignAppointment from '@/components/appointments/ReassignAppointment';

const AppointmentDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showCompleteModal, setShowCompleteModal] = useState(false);

  const { data: appointmentResponse, isLoading, error } = useQuery({
    queryKey: ['appointment', id],
    queryFn: () => getAppointmentById(parseInt(id!)),
    enabled: !!id
  });

  const { data: appointmentTypesResponse } = useQuery({
    queryKey: ['appointmentTypes'],
    queryFn: getAppointmentTypes
  });

  const appointment = appointmentResponse?.data as Appointment;
  const appointmentTypes = appointmentTypesResponse?.data?.data || [];

  const getAppointmentTypeName = (appointmentTypeId: number) => {
    const type = appointmentTypes.find((t: any) => t.appointmentTypeId === appointmentTypeId);
    return type?.name || `Type #${appointmentTypeId}`;
  };

  const getStatusBadgeClass = (status: string) => {
    const statusClasses = {
      scheduled: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
      default: "bg-gray-100 text-gray-800"
    };
    return statusClasses[status as keyof typeof statusClasses] || statusClasses.default;
  };

  const formatDateTime = (dateTime: string | Date) => {
    const date = new Date(dateTime);
    return {
      date: format(date, "EEEE, MMMM d, yyyy"),
      time: format(date, "h:mm a")
    };
  };

  const handleViewMedicalHistory = () => {
    if (appointment?.petId) {
      navigate(`/pets/${appointment.petId}/medical-history`);
    }
  };

  const handleCompleteAppointment = (healthRecord: any) => {
    // Navigate to the health record or show success message
    console.log('Appointment completed, health record created:', healthRecord);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Failed to load appointment details</p>
          <Button onClick={() => navigate('/appointments')} className="mt-4">
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  const { date, time } = formatDateTime(appointment.dateTime);

  return (
    <div className="container mx-auto px-4 py-4 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/appointments')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Appointments
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Appointment Details</h1>
            <p className="text-gray-600">Appointment #{appointment.appointmentId}</p>
          </div>
        </div>
        <Badge className={getStatusBadgeClass(appointment.status)}>
          {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Patient & Client Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Patient & Client Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Pet Name</label>
                  <p className="text-lg font-semibold">
                    {appointment.petName || appointment.petData?.petName || appointment.petData?.name || 'Unknown Pet'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Client Name</label>
                  <p className="text-lg">
                    {appointment.clientName ||
                     (appointment.clientData ? `${appointment.clientData.firstName} ${appointment.clientData.lastName}` : '') ||
                     'Unknown Client'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appointment Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Appointment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Date</label>
                    <p className="font-medium">{date}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div>
                    <label className="text-sm font-medium text-gray-600">Time</label>
                    <p className="font-medium">{time}</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Duration</label>
                  <p className="font-medium">{appointment.duration} minutes</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Appointment Type</label>
                  <p className="font-medium">
                    {appointment.appointmentTypes?.[0]?.appointmentTypeId
                      ? getAppointmentTypeName(appointment.appointmentTypes[0].appointmentTypeId)
                      : 'Not specified'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Visit Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Visit Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Reason for Visit</label>
                <p className="mt-1">{appointment.reason}</p>
              </div>

              {appointment.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Notes</label>
                  <p className="mt-1 text-gray-700">{appointment.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Veterinarian */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                Veterinarian
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="font-medium">
                  {appointment.staffName ||
                   (appointment.staffData ? `Dr. ${appointment.staffData.firstName} ${appointment.staffData.lastName}` : '') ||
                   'Not assigned'}
                </p>
                {appointment.status === 'scheduled' && appointment.staffId && (
                  <ReassignAppointment
                    appointmentId={appointment.appointmentId}
                    currentStaffId={appointment.staffId}
                    currentStaffName={appointment.staffName || 'Unknown Staff'}
                    onReassignSuccess={() => window.location.reload()}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={handleViewMedicalHistory}
                className="w-full flex items-center gap-2"
                variant="outline"
              >
                <History className="h-4 w-4" />
                View Medical History
              </Button>

              <Button
                onClick={() => navigate(`/appointments/${appointment.appointmentId}/edit`)}
                className="w-full"
                variant="outline"
              >
                Edit Appointment
              </Button>

              {appointment.status === 'scheduled' && (
                <>
                  <Button
                    onClick={() => navigate(`/appointments/${appointment.appointmentId}/start`)}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Start Appointment
                  </Button>

                  <Button
                    onClick={() => setShowCompleteModal(true)}
                    className="w-full"
                    variant="outline"
                  >
                    Complete Visit
                  </Button>
                </>
              )}

              {appointment.status === 'completed' && (
                <>
                  <Button
                    onClick={() => navigate(`/invoices/appointment/${appointment.appointmentId}`)}
                    className="w-full"
                    variant="outline"
                  >
                    <Receipt className="h-4 w-4 mr-2" />
                    View Invoice
                  </Button>

                  <Button
                    onClick={async () => {
                      try {
                        const response = await api.post(`/invoices/generate/${appointment.appointmentId}`);

                        if (response.data.success) {
                          toast({
                            title: "Invoice Generated",
                            description: "Invoice has been generated successfully.",
                          });
                          // Navigate to the invoice
                          navigate(`/invoices/appointment/${appointment.appointmentId}`);
                        } else {
                          toast({
                            title: "Error",
                            description: response.data.message || "Failed to generate invoice.",
                            variant: "destructive"
                          });
                        }
                      } catch (error: any) {
                        console.error('Error generating invoice:', error);
                        toast({
                          title: "Error",
                          description: error.response?.data?.message || "Failed to generate invoice. Please try again.",
                          variant: "destructive"
                        });
                      }
                    }}
                    className="w-full"
                    variant="outline"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Invoice
                  </Button>

                  <Button
                    onClick={async () => {
                      try {
                        const response = await api.get(`/receipts/appointment/${appointment.appointmentId}`);

                        if (response.data.success && response.data.data) {
                          navigate(`/receipts/${response.data.data.receiptId}`);
                        } else {
                          toast({
                            title: "No Receipt Found",
                            description: "No receipt has been generated for this appointment yet.",
                            variant: "destructive"
                          });
                        }
                      } catch (error: any) {
                        console.error('Error fetching receipt:', error);
                        toast({
                          title: "Error",
                          description: error.response?.data?.message || "Failed to fetch receipt. Please try again.",
                          variant: "destructive"
                        });
                      }
                    }}
                    className="w-full"
                    variant="outline"
                  >
                    <Receipt className="h-4 w-4 mr-2" />
                    View Receipt
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          {/* Appointment Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Appointment ID:</span>
                <span className="font-medium">#{appointment.appointmentId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <Badge className={getStatusBadgeClass(appointment.status)} variant="secondary">
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Badge>
              </div>
              {appointment.appointmentTypes?.[0]?.price && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Cost:</span>
                  <span className="font-medium">
                    {appointment.appointmentTypes[0].currency} {appointment.appointmentTypes[0].price}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Appointment Services Section */}
      <div className="mt-8">
        <AppointmentServices appointmentId={appointment.appointmentId} />
      </div>

      {/* Appointment Notes Section */}
      <div className="mt-8">
        <AppointmentNotes appointmentId={appointment.appointmentId} />
      </div>

      {/* Complete Appointment Modal */}
      {showCompleteModal && (
        <CompleteAppointmentModal
          appointment={appointment}
          isOpen={showCompleteModal}
          onClose={() => setShowCompleteModal(false)}
          onCompleted={handleCompleteAppointment}
        />
      )}
    </div>
  );
};

export default AppointmentDetails;
