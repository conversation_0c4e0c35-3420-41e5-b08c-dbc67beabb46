# Minimal Data Fetching Guide

This document provides guidelines and best practices for implementing minimal data fetching in the Vet-Care SaaS system, ensuring optimal performance while maintaining the client-pet-clinic relationship model.

## Core Principles

1. **Fetch Only What You Need**: Only request the specific fields required for the current operation
2. **Paginate Large Data Sets**: Use pagination for lists and search results
3. **Lazy Load Related Data**: Load related data only when needed
4. **Use Projections**: Limit the fields returned from database queries
5. **Implement Caching**: Cache frequently accessed data to reduce database load

## Backend Implementation

### Using Mongoose Projections

```javascript
// BAD: Fetching all fields
const clients = await Client.find({ status: 1 });

// GOOD: Using projection to fetch only needed fields
const clients = await Client.find(
  { status: 1 },
  'firstName lastName email phoneNumber' // Only fetch these fields
);
```

### Pagination Implementation

```javascript
// Controller function with pagination
export const getClinicClients = async (req, res) => {
  try {
    const { clinicId } = req.params;
    const { page = 1, limit = 10, search } = req.query;
    
    // Build query
    let query = { clinicId };
    if (search) {
      query.$or = [
        { 'clientId.firstName': { $regex: search, $options: 'i' } },
        { 'clientId.lastName': { $regex: search, $options: 'i' } },
        { 'clientId.email': { $regex: search, $options: 'i' } }
      ];
    }
    
    // Execute query with pagination
    const relationships = await ClientClinicRelationship.find(query)
      .populate('clientId', 'firstName lastName email phoneNumber') // Minimal fields
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ lastVisitDate: -1 });
      
    // Get total count for pagination metadata
    const totalCount = await ClientClinicRelationship.countDocuments(query);
    
    // Return paginated results
    return sendResponse(res, 200, true, "Clients retrieved successfully", 
      paginateResults(relationships, totalCount, page, limit)
    );
  } catch (error) {
    console.error("Get clinic clients error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};
```

### Selective Population

```javascript
// BAD: Deep population with all fields
const pet = await Pet.findById(petId)
  .populate('ownerId')
  .populate('speciesId')
  .populate('breedId')
  .populate('clinicRelationships');

// GOOD: Selective population with field filtering
const pet = await Pet.findById(petId)
  .populate('ownerId', 'firstName lastName email') // Only needed client fields
  .populate('speciesId', 'name') // Only species name
  .populate('breedId', 'breedName') // Only breed name
  // Don't populate clinicRelationships unless needed
```

### Conditional Population

```javascript
// Function to conditionally populate fields based on need
const getPetDetails = async (petId, includeFullHistory = false) => {
  const query = Pet.findById(petId)
    .populate('ownerId', 'firstName lastName email')
    .populate('speciesId', 'name')
    .populate('breedId', 'breedName');
  
  // Only populate full history if explicitly requested
  if (includeFullHistory) {
    query.populate({
      path: 'clinicRelationships',
      populate: {
        path: 'clinicId',
        select: 'clinicName'
      }
    });
  }
  
  return await query.exec();
};
```

## Frontend Implementation

### API Service Layer

```typescript
// Pet service with minimal data fetching
export const getPet = async (petId: string, options: { 
  includeOwner?: boolean,
  includeHistory?: boolean
} = {}): Promise<PetResponse> => {
  // Build query parameters based on what data is needed
  const params = new URLSearchParams();
  
  if (options.includeOwner) {
    params.append('includeOwner', 'true');
  }
  
  if (options.includeHistory) {
    params.append('includeHistory', 'true');
  }
  
  // Make API call with query parameters
  const url = `/pets/${petId}${params.toString() ? `?${params.toString()}` : ''}`;
  return await api.get<PetResponse>(url);
};
```

### React Query Implementation

```typescript
// Component that uses React Query for data fetching
const PetProfile = ({ petId }) => {
  // Only fetch basic pet info initially
  const { data: petData, isLoading } = useQuery({
    queryKey: ["pet", petId, "basic"],
    queryFn: () => getPet(petId),
  });
  
  // Fetch medical history only when tab is selected
  const [showHistory, setShowHistory] = useState(false);
  const { data: historyData } = useQuery({
    queryKey: ["pet", petId, "history"],
    queryFn: () => getPetMedicalHistory(petId),
    enabled: showHistory, // Only fetch when needed
  });
  
  if (isLoading) return <LoadingSpinner />;
  
  return (
    <div>
      <PetBasicInfo pet={petData.data} />
      
      <Tabs onSelect={(index) => setShowHistory(index === 1)}>
        <Tab label="Basic Info">
          <PetDetails pet={petData.data} />
        </Tab>
        <Tab label="Medical History">
          {showHistory ? (
            <MedicalHistory data={historyData?.data || []} />
          ) : (
            <LoadingSpinner />
          )}
        </Tab>
      </Tabs>
    </div>
  );
};
```

### Infinite Scrolling for Large Lists

```typescript
// Component with infinite scrolling for medical records
const PetMedicalHistory = ({ petId }) => {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useInfiniteQuery({
    queryKey: ["pet", petId, "medicalRecords"],
    queryFn: ({ pageParam = 1 }) => 
      getHealthRecords({ petId, page: pageParam, limit: 10 }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });
  
  // Intersection observer for infinite scrolling
  const observer = useRef();
  const lastRecordRef = useCallback(node => {
    if (isFetchingNextPage) return;
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasNextPage) {
        fetchNextPage();
      }
    });
    
    if (node) observer.current.observe(node);
  }, [isFetchingNextPage, fetchNextPage, hasNextPage]);
  
  // Flatten pages of results
  const records = data?.pages.flatMap(page => page.data) || [];
  
  return (
    <div className="medical-history">
      {records.map((record, index) => (
        <div 
          key={record._id}
          ref={index === records.length - 1 ? lastRecordRef : null}
          className="record-card"
        >
          <RecordSummary record={record} />
        </div>
      ))}
      
      {isFetchingNextPage && <LoadingSpinner />}
    </div>
  );
};
```

## Database Optimization

### Indexing Strategy

```javascript
// Create indexes for common query patterns
petSchema.index({ ownerId: 1 }); // Find pets by owner
petSchema.index({ name: 1 }); // Search by name
petSchema.index({ 'allergies.allergen': 1 }); // Search by allergies
petSchema.index({ clinicRelationships: 1 }); // Find by clinic relationship

// Compound indexes for more complex queries
healthRecordSchema.index({ petId: 1, date: -1 }); // Pet's records by date
healthRecordSchema.index({ clinicId: 1, date: -1 }); // Clinic's records by date
clientClinicRelationshipSchema.index({ clientId: 1, clinicId: 1 }, { unique: true }); // Unique relationship
```

### Lean Queries

```javascript
// Use lean() for read-only operations to improve performance
const clients = await Client.find({ status: 1 })
  .select('firstName lastName email')
  .lean(); // Returns plain JavaScript objects instead of Mongoose documents
```

## Caching Strategy

### Redis Implementation

```javascript
import Redis from 'ioredis';
const redis = new Redis();

// Cache wrapper for database queries
const cachedQuery = async (key, ttl, queryFn) => {
  // Try to get from cache first
  const cached = await redis.get(key);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // If not in cache, execute query
  const result = await queryFn();
  
  // Store in cache for future requests
  await redis.set(key, JSON.stringify(result), 'EX', ttl);
  
  return result;
};

// Example usage in a controller
export const getPetById = async (req, res) => {
  try {
    const { petId } = req.params;
    
    const pet = await cachedQuery(
      `pet:${petId}`, // Cache key
      300, // TTL: 5 minutes
      () => Pet.findById(petId)
        .populate('ownerId', 'firstName lastName email')
        .populate('speciesId', 'name')
        .populate('breedId', 'breedName')
        .lean()
    );
    
    if (!pet) {
      return sendResponse(res, 404, false, "Pet not found");
    }
    
    return sendResponse(res, 200, true, "Pet retrieved successfully", pet);
  } catch (error) {
    console.error("Get pet error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};
```

### Cache Invalidation

```javascript
// Invalidate cache when data changes
export const updatePet = async (req, res) => {
  try {
    const { petId } = req.params;
    const updateData = req.body;
    
    const updatedPet = await Pet.findByIdAndUpdate(
      petId,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!updatedPet) {
      return sendResponse(res, 404, false, "Pet not found");
    }
    
    // Invalidate cache
    await redis.del(`pet:${petId}`);
    
    return sendResponse(res, 200, true, "Pet updated successfully", updatedPet);
  } catch (error) {
    console.error("Update pet error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};
```

## Best Practices Summary

1. **Use Projections**: Always specify which fields to return
2. **Implement Pagination**: Use skip/limit for all list endpoints
3. **Selective Population**: Only populate related documents when needed
4. **Use Lean Queries**: Use `.lean()` for read-only operations
5. **Create Proper Indexes**: Index fields used in queries and sorts
6. **Cache Frequently Accessed Data**: Implement Redis caching
7. **Lazy Load Related Data**: Only fetch detailed data when needed
8. **Use Compound Queries**: Combine multiple conditions in a single query
9. **Implement Data Aggregation**: Use MongoDB aggregation for complex reports
10. **Monitor Query Performance**: Use MongoDB profiling to identify slow queries
