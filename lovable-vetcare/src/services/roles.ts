
import { api } from './api';
import { PaginationData, Role } from '@/store/types';

interface RoleResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Role[];
    pagination: PaginationData;
  };
}

interface SingleRoleResponse {
  success: boolean;
  status: number;
  message: string;
  data: Role;
}

export const getRoles = async (): Promise<RoleResponse> => {
  try {
    const response = await api.get<any>('/roles');
    console.log('Full roles response:', JSON.stringify(response));

    // The backend should return: { success: true, data: { data: [...], pagination: {...} } }
    // But if it's returning: { success: true, data: [...] }, we need to handle both

    if (response && response.success) {
      // Check if response.data is an array (incorrect structure from backend)
      if (Array.isArray(response.data)) {
        console.log('Handling array response structure');
        return {
          success: true,
          status: response.status || 200,
          message: response.message || 'Roles retrieved successfully',
          data: {
            data: response.data,
            pagination: {
              page: 1,
              limit: response.data.length,
              total: response.data.length,
              totalPages: 1
            }
          }
        };
      }
      // Check if response.data has the correct nested structure
      else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        console.log('Handling correct nested structure');
        return {
          success: true,
          status: response.status || 200,
          message: response.message || 'Roles retrieved successfully',
          data: response.data
        };
      }
      // Handle case where response.data is a single object
      else if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
        console.log('Handling single object response');
        return {
          success: true,
          status: response.status || 200,
          message: response.message || 'Roles retrieved successfully',
          data: {
            data: [response.data],
            pagination: {
              page: 1,
              limit: 1,
              total: 1,
              totalPages: 1
            }
          }
        };
      }
    }

    // Fallback for unexpected structure
    console.log('Unexpected response structure, using fallback');
    return {
      success: false,
      status: 500,
      message: 'Unexpected response structure',
      data: { data: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } }
    };
  } catch (error: any) {
    console.error('Error fetching roles:', error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch roles',
      data: { data: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } }
    };
  }
};

export const getRole = async (id: string): Promise<Role> => {
  try {
    const response = await api.get<any>(`/roles/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch role',
      data: null
    };
  }
};

export const createRole = async (role: Omit<Role, never>): Promise<SingleRoleResponse> => {
  try {
    const response = await api.post<any>('/roles', role);
    return {
      success: true,
      status: response.status,
      message: response.data.message || 'Role created successfully',
      data: response.data.data
    };
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create role',
      data: null
    };
  }
};

export const updateRole = async (id: string, role: Partial<Role>): Promise<SingleRoleResponse> => {
  try {
    const response = await api.put<any>(`/roles/${id}`, role);
    return {
      success: true,
      status: response.status,
      message: response.data.message || 'Role updated successfully',
      data: response.data.data
    };
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update role',
      data: null
    };
  }
};

export const deleteRole = async (id: string): Promise<SingleRoleResponse> => {
  try {
    const response = await api.delete<any>(`/roles/${id}`);
    return {
      success: true,
      status: response.status,
      message: response.data.message || 'Role deleted successfully',
      data: response.data.data
    };
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete role',
      data: null
    };
  }
};
