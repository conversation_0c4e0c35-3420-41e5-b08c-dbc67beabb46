import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const appointmentServiceSchema = new mongoose.Schema({
    // Auto-increment ID
    appointmentServiceId: {
        type: Number,
        index: true,
        unique: true,
    },
    appointmentId: {
        type: Number,
        required: true,
        index: true,
    },
    serviceId: {
        type: Number,
        required: true,
        index: true,
    },
    isCustomService: {
        type: Boolean,
        default: false
    },
    serviceName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    serviceDescription: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    category: {
        type: String,
        required: true,
        enum: [
            'consultation',
            'vaccination',
            'surgery',
            'laboratory',
            'imaging',
            'dental',
            'grooming',
            'boarding',
            'emergency',
            'wellness',
            'therapy',
            'nutrition',
            'behavioral',
            'medication',
            'follow_up',
            'other'
        ],
        default: 'consultation'
    },
    quantity: {
        type: Number,
        required: true,
        min: 1,
        default: 1
    },
    unitPrice: {
        type: Number,
        required: true,
        min: 0
    },
    totalPrice: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        required: true,
        default: 'KES',
        enum: ['KES', 'USD', 'EUR', 'GBP']
    },
    discount: {
        percentage: {
            type: Number,
            min: 0,
            max: 100,
            default: 0
        },
        amount: {
            type: Number,
            min: 0,
            default: 0
        },
        reason: String
    },
    finalAmount: {
        type: Number,
        required: true,
        min: 0
    },
    performedBy: {
        type: Number,
        required: true,
        index: true
    },
    assistedBy: [{
        type: Number,
        index: true
    }],
    duration: {
        estimated: Number, // in minutes
        actual: Number     // in minutes
    },
    status: {
        type: String,
        enum: ['pending', 'in_progress', 'completed', 'cancelled'],
        default: 'pending'
    },
    startTime: Date,
    endTime: Date,
    notes: {
        type: String,
        maxlength: 2000
    },
    complications: {
        type: String,
        maxlength: 1000
    },
    followUpRequired: {
        type: Boolean,
        default: false
    },
    followUpDate: Date,
    followUpInstructions: {
        type: String,
        maxlength: 1000
    },
    materials: [{
        itemName: String,
        quantity: Number,
        unitCost: Number,
        totalCost: Number
    }],
    equipment: [{
        equipmentName: String,
        usageDuration: Number, // in minutes
        cost: Number
    }],
    isEmergency: {
        type: Boolean,
        default: false
    },
    emergencyCharge: {
        type: Number,
        min: 0,
        default: 0
    },
    afterHoursCharge: {
        type: Number,
        min: 0,
        default: 0
    },
    isAfterHours: {
        type: Boolean,
        default: false
    },
    billingStatus: {
        type: String,
        enum: ['pending', 'billed', 'paid', 'refunded'],
        default: 'pending'
    },
    invoiceId: {
        type: Number,
        index: true
    },
    addedBy: {
        type: Number,
        required: true,
        index: true
    },
    modifiedBy: {
        type: Number,
        index: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
appointmentServiceSchema.plugin(AutoIncrement, { inc_field: 'appointmentServiceId', start_seq: 1001 });
appointmentServiceSchema.index({ appointmentServiceId: 1 }, { unique: true });

// Indexes for efficient queries
appointmentServiceSchema.index({ appointmentId: 1, status: 1 });
appointmentServiceSchema.index({ serviceId: 1, category: 1 });
appointmentServiceSchema.index({ performedBy: 1, status: 1 });
appointmentServiceSchema.index({ billingStatus: 1, invoiceId: 1 });
appointmentServiceSchema.index({ createdAt: -1 });

// Virtual for staff data
appointmentServiceSchema.virtual('performedByData', {
    ref: 'Staff',
    localField: 'performedBy',
    foreignField: 'staffId',
    justOne: true
});

// Virtual for appointment data
appointmentServiceSchema.virtual('appointmentData', {
    ref: 'Appointment',
    localField: 'appointmentId',
    foreignField: 'appointmentId',
    justOne: true
});

// Pre-save middleware to calculate totals
appointmentServiceSchema.pre('save', function(next) {
    // Calculate total price
    this.totalPrice = this.quantity * this.unitPrice;

    // Apply discount
    let discountAmount = 0;
    if (this.discount.percentage > 0) {
        discountAmount = (this.totalPrice * this.discount.percentage) / 100;
    } else if (this.discount.amount > 0) {
        discountAmount = this.discount.amount;
    }

    // Calculate final amount with charges
    this.finalAmount = this.totalPrice - discountAmount +
                      (this.emergencyCharge || 0) +
                      (this.afterHoursCharge || 0);

    next();
});

// Pre-update middleware
appointmentServiceSchema.pre('findOneAndUpdate', function(next) {
    const update = this.getUpdate();
    if (update.quantity || update.unitPrice || update.discount || update.emergencyCharge || update.afterHoursCharge) {
        // Recalculate totals
        const quantity = update.quantity || this.quantity;
        const unitPrice = update.unitPrice || this.unitPrice;
        const totalPrice = quantity * unitPrice;

        let discountAmount = 0;
        const discount = update.discount || this.discount || {};
        if (discount.percentage > 0) {
            discountAmount = (totalPrice * discount.percentage) / 100;
        } else if (discount.amount > 0) {
            discountAmount = discount.amount;
        }

        const finalAmount = totalPrice - discountAmount +
                           (update.emergencyCharge || this.emergencyCharge || 0) +
                           (update.afterHoursCharge || this.afterHoursCharge || 0);

        update.totalPrice = totalPrice;
        update.finalAmount = finalAmount;
    }
    next();
});

const AppointmentService = mongoose.model('AppointmentService', appointmentServiceSchema);

export default AppointmentService;
