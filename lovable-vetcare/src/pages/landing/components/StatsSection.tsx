
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, Users, Clock, Award } from "lucide-react";
import { motion } from "framer-motion";
import LottieAnimation from "./LottieAnimation";
import { petAnimations } from "./animationData";

const stats = [
  {
    icon: TrendingUp,
    percentage: 90,
    title: "Client Satisfaction",
    description: "of clients report improved efficiency"
  },
  {
    icon: Users,
    percentage: 98,
    title: "Successful Implementations",
    description: "success rate for new practice onboarding"
  },
  {
    icon: Clock,
    percentage: 45,
    title: "Time Saved",
    description: "average hours saved per month per practice"
  },
  {
    icon: Award,
    percentage: 100,
    title: "Quality Service",
    description: "commitment to excellence in veterinary software"
  }
];

const StatsSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const petAnimationPaths = [
    '/animation/lottie/cat.json',
    '/animation/lottie/dog.json',
    '/animation/lottie/bird.json',
    '/animation/lottie/cat.json'
  ];

  return (
    <section id="statistics" className="py-20 bg-gradient-to-b from-background to-background/90 scroll-mt-20 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10 opacity-5">
        <div className="absolute top-[10%] left-[5%] w-[300px] h-[300px] rounded-full bg-primary blur-[100px]"></div>
        <div className="absolute bottom-[20%] right-[10%] w-[250px] h-[250px] rounded-full bg-secondary blur-[80px]"></div>
      </div>

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            Our Impact
          </h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            See how VetCare is transforming veterinary practice management
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {stats.map((stat, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="overflow-hidden border-none shadow-xl bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl">
                <div className="bg-gradient-to-r from-primary to-secondary p-4 text-white relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-full opacity-20">
                    <LottieAnimation
                      animationPath={petAnimationPaths[index % petAnimationPaths.length]}
                      className="w-full h-full"
                      speed={0.5}
                    />
                  </div>
                  <stat.icon className="h-8 w-8 mb-2 relative z-10" />
                </div>
                <CardContent className="p-6">
                  <div className="relative mb-6">
                    <Progress value={stat.percentage} className="h-4" />
                    <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm font-bold">
                      {stat.percentage}%
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{stat.title}</h3>
                  <p className="text-foreground/70">{stat.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <div className="mt-16 flex justify-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl">
            <img
              src="/pets/cat.jpg"
              alt="Cat"
              className="w-full h-32 object-cover rounded-2xl shadow-lg transform hover:scale-105 transition-transform duration-300"
            />
            <img
              src="/pets/dog.jpg"
              alt="Dog"
              className="w-full h-32 object-cover rounded-2xl shadow-lg transform hover:scale-105 transition-transform duration-300"
            />
            <img
              src="/pets/parrot.jpg"
              alt="Parrot"
              className="w-full h-32 object-cover rounded-2xl shadow-lg transform hover:scale-105 transition-transform duration-300"
            />
            <img
              src="/pets/horse.jpg"
              alt="Horse"
              className="w-full h-32 object-cover rounded-2xl shadow-lg transform hover:scale-105 transition-transform duration-300"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
