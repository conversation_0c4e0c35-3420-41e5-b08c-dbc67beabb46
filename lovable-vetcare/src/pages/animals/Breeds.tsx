
import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw, MoreHorizontal, Search, Filter } from "lucide-react";
import { getBreeds, deleteBreed } from "@/services/breeds.ts";
import { getSpecies } from "@/services/species";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import LoadingPage from "@/components/common/LoadingPage";
import { useNavigate } from "react-router-dom";
import AddBreedModal from "./components/AddBreedModal";
import EditBreedModal from "./components/EditBreedModal";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

const Breeds = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [breedIdToDelete, setBreedIdToDelete] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedBreed, setSelectedBreed] = useState<any>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [originFilter, setOriginFilter] = useState("");
  const [lifespanRange, setLifespanRange] = useState<[number, number]>([0, 30]);
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [speciesList, setSpeciesList] = useState<any[]>([]);

  // Add debounce effect for search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch species data
  const { data: speciesResponse } = useQuery({
    queryKey: ['species'],
    queryFn: () => getSpecies()
  });

  // Update species list when data is fetched
  useEffect(() => {
    if (speciesResponse?.success && speciesResponse?.data?.data) {
      setSpeciesList(speciesResponse.data.data);
    }
  }, [speciesResponse]);

  const { data: response, isLoading, refetch } = useQuery({
    queryKey: ["breeds", page, limit, debouncedSearchTerm, selectedSize, selectedSpecies, originFilter, lifespanRange],
    queryFn: async () => {
      const queryParams = {
        page,
        limit,
        search: debouncedSearchTerm,
        sizeCategory: selectedSize && selectedSize !== "all_sizes" ? selectedSize : undefined,
        speciesId: selectedSpecies && selectedSpecies !== "all_species" ? selectedSpecies : undefined,
        origin: originFilter || undefined,
        minLifespan: lifespanRange[0] > 0 ? lifespanRange[0] : undefined,
        maxLifespan: lifespanRange[1] < 30 ? lifespanRange[1] : undefined
      };

      const response = await getBreeds(queryParams);
      console.log("Breeds response:", JSON.stringify(response));
      if (!response.success) {
        throw new Error(response.message);
      }
      return response;
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteBreed(id),
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Breed deleted successfully",
        });
        queryClient.invalidateQueries({queryKey: ["breeds"]});
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to delete breed",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete breed",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return <LoadingPage />;
  }

  const handleRefresh = () => {
    refetch();
  };

  const breeds = response?.data?.data || [];
  const pagination = response?.data?.pagination || {
    totalCount: 0,
    page: 1,
    limit: 5,
    totalPages: 1
  };

  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (page < pagination.totalPages) {
      setPage(page + 1);
    }
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  const openDeleteDialog = (id: string) => {
    setBreedIdToDelete(id);
    setDeleteDialog(true);
  };

  const handleDelete = () => {
    if (breedIdToDelete) {
      deleteMutation.mutate(breedIdToDelete);
      setDeleteDialog(false);
    }
  };

  const openEditModal = (breed: any) => {
    setSelectedBreed(breed);
    setIsEditModalOpen(true);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1); // Reset to first page when searching
  };

  const resetFilters = () => {
    setSearchTerm("");
    setSelectedSize(null);
    setSelectedSpecies(null);
    setOriginFilter("");
    setLifespanRange([0, 30]);
    setPage(1);
    refetch();
  };

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Breeds</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Breed
        </Button>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-grow">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by breed name, origin, size, temperament..."
                  value={searchTerm}
                  onChange={handleSearch}
                  className="pl-8"
                />
              </div>

              <Button
                variant="outline"
                onClick={() => setIsAdvancedSearchOpen(!isAdvancedSearchOpen)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {isAdvancedSearchOpen ? "Hide Filters" : "Show Filters"}
              </Button>

              <Select
                value={String(limit)}
                onValueChange={handleLimitChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select page size"/>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="15">15 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>

            {isAdvancedSearchOpen && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                <div className="space-y-2">
                  <Label>Species</Label>
                  <Select
                    value={selectedSpecies || undefined}
                    onValueChange={setSelectedSpecies}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_species">All Species</SelectItem>
                      {speciesList.map((species) => (
                        <SelectItem key={species.speciesId} value={species.speciesId}>
                          {species.speciesName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Size Category</Label>
                  <Select
                    value={selectedSize || undefined}
                    onValueChange={setSelectedSize}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_sizes">All Sizes</SelectItem>
                      <SelectItem value="small">Small</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="large">Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Origin</Label>
                  <Input
                    placeholder="Search by origin..."
                    value={originFilter}
                    onChange={(e) => setOriginFilter(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Lifespan Range</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      min="0"
                      max="30"
                      value={lifespanRange[0]}
                      onChange={(e) => setLifespanRange([parseInt(e.target.value), lifespanRange[1]])}
                      className="w-20"
                    />
                    <span>to</span>
                    <Input
                      type="number"
                      min="0"
                      max="30"
                      value={lifespanRange[1]}
                      onChange={(e) => setLifespanRange([lifespanRange[0], parseInt(e.target.value)])}
                      className="w-20"
                    />
                    <span>years</span>
                  </div>
                </div>
              </div>
            )}

            {isAdvancedSearchOpen && (
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={resetFilters}>Reset Filters</Button>
                <Button onClick={() => {
                  setPage(1);
                  refetch();
                }}>Apply Filters</Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {breeds.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">No breeds found</div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-48">Breed Name</TableHead>
                <TableHead className="w-32">Species</TableHead>
                <TableHead className="w-32">Common Color</TableHead>
                <TableHead className="w-32">Origin</TableHead>
                <TableHead className="w-32">Size Category</TableHead>
                <TableHead className="w-24">Lifespan</TableHead>
                <TableHead className="text-right pr-6 w-16">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleRefresh}
                    className="ml-auto"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {breeds.map((breed) => {
                // Find the species name for this breed
                let speciesName = "Unknown";

                if (typeof breed.speciesId === 'object' && breed.speciesId !== null) {
                  // speciesId is populated with Species object
                  speciesName = breed.speciesId.speciesName || breed.speciesId.name || "Unknown";
                } else if (typeof breed.speciesId === 'number') {
                  // speciesId is just a number, find in local list
                  const species = speciesList.find(s => s.speciesId === breed.speciesId);
                  speciesName = species?.speciesName || species?.name || "Unknown";
                }

                return (
                  <TableRow key={breed.breedId || breed._id}>
                    <TableCell className="font-medium">{breed.breedName}</TableCell>
                    <TableCell>{speciesName}</TableCell>
                    <TableCell>{breed.commonColour}</TableCell>
                    <TableCell>{breed.origin}</TableCell>
                    <TableCell className="w-32">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        breed.sizeCategory === 'small' ? 'bg-blue-100 text-blue-800' :
                        breed.sizeCategory === 'medium' ? 'bg-green-100 text-green-800' :
                        'bg-amber-100 text-amber-800'
                      }`}>
                        {breed.sizeCategory}
                      </span>
                    </TableCell>
                    <TableCell>{breed.lifespan} years</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-white z-50">
                          <DropdownMenuItem
                            onClick={() => openEditModal(breed)}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => openDeleteDialog(breed.breedId?.toString() || breed._id || "")}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}

      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-muted-foreground">
          Showing {breeds.length > 0 ? ((pagination.page - 1) * limit + 1) : 0} to {Math.min(pagination.page * limit, pagination.totalCount)} of {pagination.totalCount} entries
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handlePreviousPage}
            disabled={pagination.page === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={handleNextPage}
            disabled={pagination.page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      </div>

      <Dialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this breed? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              {deleteMutation.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Species Modal */}
      <AddBreedModal
        open={isAddModalOpen}
        onOpenChange={setIsAddModalOpen}
      />

      <EditBreedModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        breed={selectedBreed}
      />
    </div>
  );
};

export default Breeds;
