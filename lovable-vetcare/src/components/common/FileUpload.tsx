import * as React from "react"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"
import { cn } from "@/lib/utils"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { But<PERSON> } from "@/components/ui/button"
import { X, Upload, File, Image as ImageIcon, AlertCircle } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface FileUploadProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName
  label?: string
  description?: string
  form: UseFormReturn<TFieldValues>
  required?: boolean
  disabled?: boolean
  className?: string
  accept?: string
  multiple?: boolean
  maxSize?: number // in bytes
  maxFiles?: number
  showPreview?: boolean
  previewType?: "image" | "list"
  onUploadProgress?: (progress: number) => void
  showValidation?: boolean
}

export function FileUpload<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  form,
  required,
  disabled,
  className,
  accept,
  multiple = false,
  maxSize,
  maxFiles = 1,
  showPreview = true,
  previewType = "image",
  onUploadProgress,
  showValidation = true,
}: FileUploadProps<TFieldValues, TName>) {
  const { formState } = form
  const error = formState.errors[name]
  const isValid = formState.touchedFields[name] && !error
  
  const [dragActive, setDragActive] = React.useState(false)
  const [uploadProgress, setUploadProgress] = React.useState(0)
  const [previewUrls, setPreviewUrls] = React.useState<string[]>([])
  const inputRef = React.useRef<HTMLInputElement>(null)
  
  // Get current files from form
  const files = form.watch(name) as File | File[] | null
  
  // Create preview URLs when files change
  React.useEffect(() => {
    if (!files) {
      setPreviewUrls([])
      return
    }
    
    const fileArray = Array.isArray(files) ? files : [files]
    const urls = fileArray.map(file => URL.createObjectURL(file))
    setPreviewUrls(urls)
    
    // Clean up URLs when component unmounts
    return () => {
      urls.forEach(url => URL.revokeObjectURL(url))
    }
  }, [files])
  
  // Simulate upload progress
  React.useEffect(() => {
    if (!files) {
      setUploadProgress(0)
      return
    }
    
    let progress = 0
    const interval = setInterval(() => {
      progress += 5
      setUploadProgress(progress)
      if (onUploadProgress) {
        onUploadProgress(progress)
      }
      
      if (progress >= 100) {
        clearInterval(interval)
      }
    }, 100)
    
    return () => {
      clearInterval(interval)
    }
  }, [files, onUploadProgress])
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return
    
    const selectedFiles = Array.from(e.target.files)
    
    // Validate file size if maxSize is provided
    if (maxSize) {
      const oversizedFiles = selectedFiles.filter(file => file.size > maxSize)
      if (oversizedFiles.length > 0) {
        form.setError(name, {
          type: "manual",
          message: `File${oversizedFiles.length > 1 ? "s" : ""} exceed${oversizedFiles.length === 1 ? "s" : ""} maximum size of ${formatFileSize(maxSize)}`
        })
        return
      }
    }
    
    // Validate number of files if multiple is true
    if (multiple && maxFiles && selectedFiles.length > maxFiles) {
      form.setError(name, {
        type: "manual",
        message: `Maximum ${maxFiles} file${maxFiles > 1 ? "s" : ""} allowed`
      })
      return
    }
    
    // Update form value
    form.setValue(name, multiple ? selectedFiles : selectedFiles[0], {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    })
  }
  
  // Handle drag events
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }
  
  // Handle drop event
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files?.length) {
      const droppedFiles = Array.from(e.dataTransfer.files)
      
      // Validate file type if accept is provided
      if (accept) {
        const acceptedTypes = accept.split(",").map(type => type.trim())
        const invalidFiles = droppedFiles.filter(file => {
          return !acceptedTypes.some(type => {
            if (type.startsWith(".")) {
              // Check file extension
              return file.name.toLowerCase().endsWith(type.toLowerCase())
            } else {
              // Check MIME type
              return file.type.match(new RegExp(type.replace("*", ".*")))
            }
          })
        })
        
        if (invalidFiles.length > 0) {
          form.setError(name, {
            type: "manual",
            message: `Invalid file type${invalidFiles.length > 1 ? "s" : ""}`
          })
          return
        }
      }
      
      // Validate file size if maxSize is provided
      if (maxSize) {
        const oversizedFiles = droppedFiles.filter(file => file.size > maxSize)
        if (oversizedFiles.length > 0) {
          form.setError(name, {
            type: "manual",
            message: `File${oversizedFiles.length > 1 ? "s" : ""} exceed${oversizedFiles.length === 1 ? "s" : ""} maximum size of ${formatFileSize(maxSize)}`
          })
          return
        }
      }
      
      // Validate number of files if multiple is true
      if (multiple && maxFiles && droppedFiles.length > maxFiles) {
        form.setError(name, {
          type: "manual",
          message: `Maximum ${maxFiles} file${maxFiles > 1 ? "s" : ""} allowed`
        })
        return
      }
      
      // Update form value
      form.setValue(name, multiple ? droppedFiles : droppedFiles[0], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      })
    }
  }
  
  // Handle file removal
  const handleRemoveFile = (index: number) => {
    if (!files) return
    
    if (Array.isArray(files)) {
      const newFiles = [...files]
      newFiles.splice(index, 1)
      form.setValue(name, newFiles.length ? newFiles : null, {
        shouldValidate: true,
        shouldDirty: true,
      })
    } else {
      form.setValue(name, null, {
        shouldValidate: true,
        shouldDirty: true,
      })
    }
  }
  
  // Format file size for display
  const formatFileSize = (size: number) => {
    if (size < 1024) {
      return `${size} B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(1)} KB`
    } else {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`
    }
  }
  
  // Get file icon based on file type
  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) {
      return <ImageIcon className="h-5 w-5" />
    } else {
      return <File className="h-5 w-5" />
    }
  }
  
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field: { onChange, value, ...fieldProps } }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div
              className={cn(
                "relative flex flex-col items-center justify-center w-full min-h-[150px] rounded-md border-2 border-dashed border-input p-4 transition-colors",
                dragActive && "border-primary bg-primary/5",
                error && "border-destructive",
                isValid && showValidation && "border-green-500",
                disabled && "opacity-50 cursor-not-allowed",
                className
              )}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
            >
              <input
                ref={inputRef}
                type="file"
                accept={accept}
                multiple={multiple}
                disabled={disabled}
                className="hidden"
                onChange={handleFileChange}
                {...fieldProps}
              />
              
              {/* Show preview if files exist */}
              {files && showPreview ? (
                <div className={cn(
                  "w-full",
                  previewType === "image" ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2" : "space-y-2"
                )}>
                  {(Array.isArray(files) ? files : [files]).map((file, index) => (
                    <div
                      key={`${file.name}-${index}`}
                      className={cn(
                        "relative group",
                        previewType === "image" ? "aspect-square" : "flex items-center p-2 border rounded-md"
                      )}
                    >
                      {previewType === "image" && file.type.startsWith("image/") ? (
                        <img
                          src={previewUrls[index]}
                          alt={file.name}
                          className="w-full h-full object-cover rounded-md"
                        />
                      ) : (
                        <div className="flex items-center gap-2">
                          {getFileIcon(file)}
                          <span className="text-sm truncate">{file.name}</span>
                          <span className="text-xs text-muted-foreground ml-auto">
                            {formatFileSize(file.size)}
                          </span>
                        </div>
                      )}
                      
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className={cn(
                          "absolute",
                          previewType === "image" ? "top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity" : "right-1"
                        )}
                        onClick={() => handleRemoveFile(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  
                  {/* Show upload progress */}
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full mt-2">
                      <Progress value={uploadProgress} className="h-2" />
                      <p className="text-xs text-muted-foreground mt-1">
                        Uploading: {uploadProgress}%
                      </p>
                    </div>
                  )}
                  
                  {/* Add more files button if multiple is true */}
                  {multiple && (!maxFiles || (Array.isArray(files) && files.length < maxFiles)) && (
                    <Button
                      type="button"
                      variant="outline"
                      className={cn(
                        "h-full min-h-[100px] w-full flex flex-col items-center justify-center gap-2",
                        previewType === "image" ? "aspect-square" : ""
                      )}
                      onClick={() => inputRef.current?.click()}
                    >
                      <Upload className="h-6 w-6" />
                      <span>Add more</span>
                    </Button>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center p-4">
                  <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm font-medium mb-1">
                    Drag & drop {multiple ? "files" : "file"} here
                  </p>
                  <p className="text-xs text-muted-foreground mb-4">
                    or click to browse
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => inputRef.current?.click()}
                    disabled={disabled}
                  >
                    Select {multiple ? "Files" : "File"}
                  </Button>
                  {accept && (
                    <p className="text-xs text-muted-foreground mt-2">
                      Accepted formats: {accept}
                    </p>
                  )}
                  {maxSize && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Max size: {formatFileSize(maxSize)}
                    </p>
                  )}
                </div>
              )}
              
              {error && (
                <div className="absolute bottom-2 right-2 flex items-center gap-1 text-xs text-destructive">
                  <AlertCircle className="h-3 w-3" />
                  <span>{error.message?.toString()}</span>
                </div>
              )}
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
