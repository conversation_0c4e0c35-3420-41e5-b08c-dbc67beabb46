# Client and Pet Management Frontend Implementation

This document provides detailed guidance for implementing the client and pet management features in the Vet-Care SaaS system frontend, building on the backend client-pet-clinic relationship model.

## Table of Contents

1. [Client Management](#client-management)
   - [Client Registration](#client-registration)
   - [Client Profile](#client-profile)
   - [Client List](#client-list)
2. [Pet Management](#pet-management)
   - [Pet Registration](#pet-registration)
   - [Pet Profile](#pet-profile)
   - [Pet List](#pet-list)
3. [Cross-Clinic Functionality](#cross-clinic-functionality)
   - [Client Portal](#client-portal)
   - [Clinic Staff View](#clinic-staff-view)
4. [UI Components](#ui-components)
5. [Implementation Examples](#implementation-examples)

## Client Management

### Client Registration

#### Registration Form

Create a multi-step registration form with the following components:

1. **Personal Information Form**
   - First name, last name
   - Email address (used for login)
   - Phone number
   - Password creation (if self-registering)

2. **Contact Information Form**
   - Address
   - City, state, zip code
   - Alternative contact methods
   - Emergency contact

3. **Preferences Form**
   - Communication preferences (email, SMS, phone)
   - Data sharing preferences
   - Marketing opt-in/out

#### Implementation Considerations

- Use form validation with Zod schemas
- Implement password strength requirements
- Add email verification step
- Store form progress in local state for multi-step process
- Provide clear error messages for validation failures

### Client Profile

#### Profile Page Layout

Create a comprehensive client profile page with:

1. **Header Section**
   - Client name and basic info
   - Contact buttons (email, call)
   - Edit profile button
   - Status indicator (active, inactive)

2. **Information Tabs**
   - Personal Information
   - Pets (with add pet button)
   - Appointments (past and upcoming)
   - Billing History
   - Notes

3. **Activity Timeline**
   - Recent interactions
   - Upcoming reminders
   - Outstanding invoices

#### Edit Profile Functionality

Implement inline editing for profile fields:

- Click on field to edit
- Save changes on enter/blur
- Show validation errors inline
- Provide undo functionality

### Client List

#### List View

Create a paginated, filterable client list:

1. **Search and Filter Options**
   - Search by name, email, phone
   - Filter by status, clinic, registration date
   - Sort by various fields

2. **Client Cards/Rows**
   - Basic client information
   - Number of pets
   - Last visit date
   - Quick action buttons

3. **Bulk Actions**
   - Send email to selected clients
   - Export client data
   - Update status

#### Data Fetching Strategy

- Implement pagination with React Query
- Use debounced search
- Cache results for better performance
- Show loading states during data fetching

## Pet Management

### Pet Registration

#### Registration Form

Create a comprehensive pet registration form:

1. **Basic Information**
   - Name
   - Species (dropdown)
   - Breed (filtered by species)
   - Date of birth
   - Gender
   - Color
   - Microchip ID

2. **Medical Information**
   - Weight
   - Spayed/neutered status
   - Known allergies
   - Chronic conditions

3. **Ownership Information**
   - Link to existing client
   - Option to create new client
   - Primary/secondary owners

#### Species and Breed Selection

Implement dynamic species and breed selection:

```tsx
const PetSpeciesBreedSelector = () => {
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  
  // Fetch all species
  const { data: speciesData } = useQuery({
    queryKey: ["species"],
    queryFn: getSpecies,
  });
  
  // Only fetch breeds for selected species
  const { data: breedsData } = useQuery({
    queryKey: ["breeds", selectedSpecies],
    queryFn: () => getBreeds(selectedSpecies!),
    enabled: !!selectedSpecies,
  });
  
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="speciesId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Species</FormLabel>
            <Select
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
                setSelectedSpecies(value);
                // Reset breed when species changes
                form.setValue("breedId", "");
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select species" />
              </SelectTrigger>
              <SelectContent>
                {speciesData?.data.map((species) => (
                  <SelectItem key={species._id} value={species._id}>
                    {species.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="breedId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Breed</FormLabel>
            <Select
              value={field.value}
              onValueChange={field.onChange}
              disabled={!selectedSpecies}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select breed" />
              </SelectTrigger>
              <SelectContent>
                {breedsData?.data.map((breed) => (
                  <SelectItem key={breed._id} value={breed._id}>
                    {breed.breedName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
```

### Pet Profile

#### Profile Page Layout

Create a comprehensive pet profile page:

1. **Header Section**
   - Pet name and photo
   - Basic information (species, breed, age)
   - Owner information with link
   - Status indicator

2. **Information Tabs**
   - Basic Information
   - Medical History
   - Appointments
   - Vaccinations
   - Medications

3. **Medical Timeline**
   - Chronological view of medical events
   - Color-coded by record type
   - Expandable for details

#### Medical History Implementation

Implement a filterable, paginated medical history view:

```tsx
const PetMedicalHistory = ({ petId }) => {
  const [filters, setFilters] = useState({
    recordType: "",
    startDate: "",
    endDate: "",
    clinicId: ""
  });
  
  // Fetch clinics for filter
  const { data: clinicsData } = useQuery({
    queryKey: ["clinics"],
    queryFn: getClinics,
  });
  
  // Fetch medical history with filters and pagination
  const {
    data: historyData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useInfiniteQuery({
    queryKey: ["petMedicalHistory", petId, filters],
    queryFn: ({ pageParam = 1 }) => 
      getPetMedicalHistory(petId, {
        page: pageParam,
        limit: 10,
        ...filters
      }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });
  
  // Flatten pages of results
  const records = historyData?.pages.flatMap(page => page.data) || [];
  
  return (
    <div className="space-y-6">
      <div className="bg-card p-4 rounded-lg">
        <h3 className="text-lg font-medium mb-4">Filter Medical History</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Select
            value={filters.recordType}
            onValueChange={(value) => setFilters({...filters, recordType: value})}
          >
            <SelectTrigger>
              <SelectValue placeholder="Record Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Types</SelectItem>
              <SelectItem value="consultation">Consultation</SelectItem>
              <SelectItem value="vaccination">Vaccination</SelectItem>
              <SelectItem value="surgery">Surgery</SelectItem>
              <SelectItem value="laboratory">Laboratory</SelectItem>
              <SelectItem value="imaging">Imaging</SelectItem>
              <SelectItem value="prescription">Prescription</SelectItem>
            </SelectContent>
          </Select>
          
          <DatePicker
            placeholder="Start Date"
            value={filters.startDate}
            onChange={(date) => setFilters({...filters, startDate: date})}
          />
          
          <DatePicker
            placeholder="End Date"
            value={filters.endDate}
            onChange={(date) => setFilters({...filters, endDate: date})}
          />
          
          <Select
            value={filters.clinicId}
            onValueChange={(value) => setFilters({...filters, clinicId: value})}
          >
            <SelectTrigger>
              <SelectValue placeholder="Clinic" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Clinics</SelectItem>
              {clinicsData?.data.map((clinic) => (
                <SelectItem key={clinic._id} value={clinic._id}>
                  {clinic.clinicName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-4">
        {records.length === 0 ? (
          <EmptyState
            title="No medical records"
            description="This pet doesn't have any medical records yet."
          />
        ) : (
          records.map((record) => (
            <MedicalRecordCard key={record._id} record={record} />
          ))
        )}
        
        {hasNextPage && (
          <Button
            variant="outline"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            className="w-full"
          >
            {isFetchingNextPage ? "Loading more..." : "Load more"}
          </Button>
        )}
      </div>
    </div>
  );
};
```

### Pet List

#### List View

Create a paginated, filterable pet list:

1. **Search and Filter Options**
   - Search by name, microchip ID
   - Filter by species, breed, status
   - Filter by owner

2. **Pet Cards/Rows**
   - Pet photo and name
   - Species and breed
   - Age and gender
   - Owner name with link
   - Quick action buttons

3. **Bulk Actions**
   - Schedule appointments for selected pets
   - Print medical records
   - Update status

## Cross-Clinic Functionality

### Client Portal

Implement a client portal where clients can:

1. **Manage Their Profile**
   - Update personal information
   - Manage communication preferences
   - Control data sharing settings

2. **Manage Their Pets**
   - View pet profiles
   - Access medical history across all clinics
   - Download medical records

3. **Manage Appointments**
   - Book appointments at any clinic
   - View upcoming appointments
   - Reschedule or cancel appointments

### Clinic Staff View

Implement a clinic staff view that:

1. **Shows Client Relationships**
   - Indicates if client is registered at this clinic
   - Shows relationship status
   - Displays visit history

2. **Shows Pet Relationships**
   - Indicates if pet has been treated at this clinic
   - Shows visit history
   - Displays clinic-specific notes

3. **Manages Data Access**
   - Respects client data sharing preferences
   - Provides appropriate access to medical history
   - Allows adding clinic-specific information

## UI Components

### Client Components

1. **ClientCard**
   - Compact display of client information
   - Used in lists and search results
   - Shows basic contact information

2. **ClientSelector**
   - Searchable dropdown for selecting clients
   - Shows recently accessed clients
   - Allows creating new client from selector

3. **ClientRelationshipBadge**
   - Visual indicator of client-clinic relationship
   - Shows if client is new, returning, or preferred
   - Displays relationship status

### Pet Components

1. **PetCard**
   - Compact display of pet information
   - Shows photo, name, species, breed
   - Displays age and gender

2. **PetSelector**
   - Searchable dropdown for selecting pets
   - Filters by owner if owner is selected
   - Shows recently accessed pets

3. **PetRelationshipBadge**
   - Visual indicator of pet-clinic relationship
   - Shows if pet is new or returning
   - Displays any medical alerts

## Implementation Examples

### Client Registration Form

```tsx
const ClientRegistrationForm = () => {
  const [step, setStep] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Form schema for each step
  const personalInfoSchema = z.object({
    firstName: z.string().min(2, "First name is required"),
    lastName: z.string().min(2, "Last name is required"),
    email: z.string().email("Invalid email address"),
    phoneNumber: z.string().min(10, "Phone number is required"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string()
  }).refine(data => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"]
  });
  
  const contactInfoSchema = z.object({
    address: z.string().min(1, "Address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zipCode: z.string().min(1, "Zip code is required"),
    alternativePhone: z.string().optional(),
    emergencyContactName: z.string().optional(),
    emergencyContactPhone: z.string().optional()
  });
  
  const preferencesSchema = z.object({
    communicationPreferences: z.object({
      email: z.boolean().default(true),
      sms: z.boolean().default(true),
      phone: z.boolean().default(true)
    }),
    dataSharingPreferences: z.object({
      shareAllClinics: z.boolean().default(true)
    }),
    preferredClinicId: z.string().optional()
  });
  
  // Form setup
  const form = useForm({
    resolver: zodResolver(
      step === 1 ? personalInfoSchema :
      step === 2 ? contactInfoSchema :
      preferencesSchema
    ),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      communicationPreferences: {
        email: true,
        sms: true,
        phone: true
      },
      dataSharingPreferences: {
        shareAllClinics: true
      }
    }
  });
  
  // Handle form submission
  const onSubmit = async (values) => {
    if (step < 3) {
      setStep(step + 1);
      return;
    }
    
    try {
      const result = await createClient(values);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Client registered successfully",
        });
        navigate(`/clients/${result.data._id}`);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error.message || "Failed to register client",
        variant: "destructive",
      });
    }
  };
  
  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">Client Registration</h1>
        <div className="flex items-center mt-4">
          <Step number={1} active={step === 1} completed={step > 1} label="Personal Info" />
          <Divider />
          <Step number={2} active={step === 2} completed={step > 2} label="Contact Info" />
          <Divider />
          <Step number={3} active={step === 3} completed={step > 3} label="Preferences" />
        </div>
      </div>
      
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {step === 1 && <PersonalInfoStep form={form} />}
        {step === 2 && <ContactInfoStep form={form} />}
        {step === 3 && <PreferencesStep form={form} />}
        
        <div className="flex justify-between mt-8">
          {step > 1 && (
            <Button type="button" variant="outline" onClick={() => setStep(step - 1)}>
              Previous
            </Button>
          )}
          
          <Button type="submit">
            {step < 3 ? "Next" : "Register Client"}
          </Button>
        </div>
      </form>
    </div>
  );
};
```
