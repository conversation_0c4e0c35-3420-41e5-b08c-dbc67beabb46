# Production Environment Configuration
# Copy this file to .env.production.local and fill in the values

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration
DB_URI=mongodb://your_production_mongodb_uri

# Authentication Configuration
JWT_SECRET=your_production_jwt_secret_key
JWT_EXPIRES_IN=1h

# Security Configuration
ARCJET_KEY=your_arcjet_key
ARCJET_ENV=production

# Queue Configuration
QSTASH_URL=your_qstash_url
QSTASH_TOKEN=your_qstash_token
