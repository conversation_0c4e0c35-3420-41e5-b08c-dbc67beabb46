
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { PawPrint, <PERSON>, Sun, Menu, <PERSON> } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { motion } from "framer-motion";
import TriangleDecoration from "./TriangleDecoration";

const Header = () => {
  const { theme, setTheme } = useTheme();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navItems = [
    { name: "Services", href: "#services" },
    { name: "Impact", href: "#impact" },
    { name: "Why Choose Us", href: "#why-choose-us" },
    { name: "Success Rate", href: "#statistics" },
    { name: "Contact", href: "#contact" }
  ];

  const navItemVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1 * i,
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1]
      }
    })
  };

  return (
    <header className="fixed w-full top-0 bg-background/60 backdrop-blur-xl z-50 border-b border-primary/10">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <motion.div
          className="flex items-center gap-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative">
            <PawPrint className="text-primary h-7 w-7" />
            <TriangleDecoration
              size={15}
              color="bg-secondary"
              top="-5px"
              right="-5px"
              rotate={45}
              delay={0.5}
            />
          </div>
          <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            VetCare
          </h1>
        </motion.div>

        <div className="hidden md:flex items-center space-x-8">
          {navItems.map((item, i) => (
            <motion.div
              key={item.name}
              custom={i}
              initial="hidden"
              animate="visible"
              variants={navItemVariants}
            >
              <Link
                to={item.href}
                className="relative group"
              >
                <span className="text-foreground/80 hover:text-primary transition-colors">
                  {item.name}
                </span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
              </Link>
            </motion.div>
          ))}
        </div>

        <div className="flex items-center space-x-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="rounded-full"
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </Button>
          </motion.div>

          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="rounded-full"
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Link to="/login">
                <Button variant="outline" className="rounded-xl border-primary/20 hover:bg-primary/10">
                  Login
                </Button>
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <Link to="/register/owner">
                <Button className="rounded-xl bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all duration-300">
                  Get Started
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <motion.div
        className={`md:hidden bg-background/95 backdrop-blur-xl border-b border-primary/10 py-4 overflow-hidden`}
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: mobileMenuOpen ? 'auto' : 0,
          opacity: mobileMenuOpen ? 1 : 0
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <nav className="container mx-auto px-4 flex flex-col space-y-4">
          {navItems.map((item, i) => (
            <Link
              key={item.name}
              to={item.href}
              className="hover:text-primary transition-colors py-2 border-b border-primary/10"
              onClick={() => setMobileMenuOpen(false)}
            >
              {item.name}
            </Link>
          ))}
          <div className="pt-4 flex space-x-4">
            <Link to="/login" className="w-1/2">
              <Button variant="outline" className="w-full rounded-xl border-primary/20">
                Login
              </Button>
            </Link>
            <Link to="/register/owner" className="w-1/2">
              <Button className="w-full rounded-xl bg-gradient-to-r from-primary to-secondary">
                Get Started
              </Button>
            </Link>
          </div>
        </nav>
      </motion.div>
    </header>
  );
};

export default Header;
