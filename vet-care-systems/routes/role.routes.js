import express from 'express';
import {
    createRole,
    getAllRoles,
    getRoleById,
    updateRole,
    deleteRole,
    addPermissionToRole,
    removePermissionFromRole,
    getRolePermissions
} from '../controllers/role.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Base route: /api/roles

// Apply authentication middleware to all routes
router.use(verifyToken);

// All routes are now protected
router.get('/', getAllRoles);
router.get('/:roleId', getRoleById);
router.post('/', createRole);
router.put('/:roleId', updateRole);
router.delete('/:roleId', deleteRole);

// Permission management
router.post('/:roleId/permissions', addPermissionToRole);
router.delete('/:roleId/permissions/:permissionId', removePermissionFromRole);
router.get('/:roleId/permissions', getRolePermissions);

export default router;