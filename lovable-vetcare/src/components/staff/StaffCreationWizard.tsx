import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Shield,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Building2,
  Calendar,
  DollarSign,
  Clock,
  UserCheck
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { api } from "@/services/api";

interface StaffData {
  // Personal Info
  firstName: string;
  middleName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: string;
  dob: string;

  // Employment Info
  clinicId: number;
  roleId: number;
  jobTitle: string;
  employmentDate: string;
  salary: number;

  // Emergency Contact
  emergencyContact: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };

  // Schedule
  schedule: {
    workDays: string[];
    workHours: { start: string; end: string };
  };

  // Permissions
  specialPermissions: number[];
  revokedPermissions: number[];
}

interface StaffCreationWizardProps {
  clinicId?: number;
  onComplete: (staff: any) => void;
  onCancel: () => void;
}

const STEPS = [
  { id: 1, title: "Personal Information", icon: User },
  { id: 2, title: "Employment Details", icon: Briefcase },
  { id: 3, title: "Role & Permissions", icon: Shield },
  { id: 4, title: "Schedule & Contact", icon: Clock },
  { id: 5, title: "Review & Create", icon: CheckCircle }
];

const WORK_DAYS = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' }
];

export const StaffCreationWizard = ({ clinicId, onComplete, onCancel }: StaffCreationWizardProps) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [clinics, setClinics] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<any[]>([]);
  const [formData, setFormData] = useState<StaffData>({
    firstName: "",
    middleName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    address: "",
    dob: "",
    clinicId: clinicId || 0,
    roleId: 0,
    jobTitle: "",
    employmentDate: new Date().toISOString().split('T')[0],
    salary: 0,
    emergencyContact: {
      name: "",
      relationship: "",
      phoneNumber: ""
    },
    schedule: {
      workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      workHours: { start: '08:00', end: '17:00' }
    },
    specialPermissions: [],
    revokedPermissions: []
  });

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [clinicsRes, rolesRes, permissionsRes] = await Promise.all([
          api.get('/clinics'),
          api.get('/roles'),
          api.get('/permissions')
        ]);

        if (clinicsRes.data?.success) {
          setClinics(clinicsRes.data.data.data || []);
        }
        if (rolesRes.data?.success) {
          setRoles(rolesRes.data.data.data || []);
        }
        if (permissionsRes.data?.success) {
          setPermissions(permissionsRes.data.data.data || []);
        }
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch required data",
        });
      }
    };

    fetchData();
  }, [toast]);

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof StaffData],
        [field]: value
      }
    }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await api.post('/staff', {
        ...formData,
        password: 'pass123' // Default password
      });

      if (response.data?.success) {
        toast({
          title: "Success!",
          description: "Staff member created successfully",
        });
        onComplete(response.data.data);
      } else {
        throw new Error(response.data?.message || "Failed to create staff member");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create staff member",
      });
    } finally {
      setLoading(false);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;
  const selectedRole = roles.find(role => role.roleId === formData.roleId);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Add New Staff Member</h1>
        <p className="text-gray-600">Create a new staff account with role and permissions</p>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progress} className="h-2" />
        <div className="flex justify-between text-sm text-gray-500">
          <span>Step {currentStep} of {STEPS.length}</span>
          <span>{Math.round(progress)}% Complete</span>
        </div>
      </div>

      {/* Steps Navigation */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4">
          {STEPS.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;

            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all
                  ${isActive ? 'border-primary bg-primary text-white' :
                    isCompleted ? 'border-green-500 bg-green-500 text-white' :
                    'border-gray-300 bg-white text-gray-400'}
                `}>
                  <Icon className="h-5 w-5" />
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="min-h-[500px]">
        <CardHeader>
          <CardTitle className="flex items-center">
            {React.createElement(STEPS[currentStep - 1].icon, { className: "mr-2 h-5 w-5" })}
            {STEPS[currentStep - 1].title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && (
                <PersonalInfoStep formData={formData} updateFormData={updateFormData} />
              )}
              {currentStep === 2 && (
                <EmploymentDetailsStep
                  formData={formData}
                  updateFormData={updateFormData}
                  clinics={clinics}
                  roles={roles}
                />
              )}
              {currentStep === 3 && (
                <RolePermissionsStep
                  formData={formData}
                  updateFormData={updateFormData}
                  roles={roles}
                  permissions={permissions}
                  selectedRole={selectedRole}
                />
              )}
              {currentStep === 4 && (
                <ScheduleContactStep
                  formData={formData}
                  updateFormData={updateFormData}
                  updateNestedFormData={updateNestedFormData}
                />
              )}
              {currentStep === 5 && (
                <ReviewStep formData={formData} selectedRole={selectedRole} />
              )}
            </motion.div>
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={currentStep === 1 ? onCancel : prevStep}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {currentStep === 1 ? 'Cancel' : 'Previous'}
        </Button>

        <Button
          onClick={currentStep === STEPS.length ? handleSubmit : nextStep}
          disabled={loading}
          className="flex items-center"
        >
          {currentStep === STEPS.length ? (
            loading ? 'Creating...' : 'Create Staff Member'
          ) : (
            <>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

// Step Components
const PersonalInfoStep = ({ formData, updateFormData }: any) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="space-y-2">
        <Label htmlFor="firstName">First Name *</Label>
        <Input
          id="firstName"
          placeholder="John"
          value={formData.firstName}
          onChange={(e) => updateFormData('firstName', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="middleName">Middle Name</Label>
        <Input
          id="middleName"
          placeholder="Michael"
          value={formData.middleName}
          onChange={(e) => updateFormData('middleName', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="lastName">Last Name *</Label>
        <Input
          id="lastName"
          placeholder="Smith"
          value={formData.lastName}
          onChange={(e) => updateFormData('lastName', e.target.value)}
        />
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="email">Email *</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="phoneNumber">Phone Number *</Label>
        <Input
          id="phoneNumber"
          placeholder="+****************"
          value={formData.phoneNumber}
          onChange={(e) => updateFormData('phoneNumber', e.target.value)}
        />
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          placeholder="123 Main Street, City, State 12345"
          value={formData.address}
          onChange={(e) => updateFormData('address', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="dob">Date of Birth</Label>
        <Input
          id="dob"
          type="date"
          value={formData.dob}
          onChange={(e) => updateFormData('dob', e.target.value)}
        />
      </div>
    </div>
  </div>
);

const EmploymentDetailsStep = ({ formData, updateFormData, clinics, roles }: any) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="clinicId">Clinic *</Label>
        <Select
          value={formData.clinicId.toString()}
          onValueChange={(value) => updateFormData('clinicId', parseInt(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a clinic" />
          </SelectTrigger>
          <SelectContent>
            {clinics.map((clinic: any) => (
              <SelectItem key={clinic.clinicId} value={clinic.clinicId.toString()}>
                <div className="flex items-center">
                  <Building2 className="mr-2 h-4 w-4" />
                  {clinic.clinicName}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="roleId">Role *</Label>
        <Select
          value={formData.roleId.toString()}
          onValueChange={(value) => updateFormData('roleId', parseInt(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a role" />
          </SelectTrigger>
          <SelectContent>
            {roles.map((role: any) => (
              <SelectItem key={role.roleId} value={role.roleId.toString()}>
                <div className="flex items-center justify-between w-full">
                  <span>{role.name}</span>
                  <Badge variant="outline" className="ml-2">
                    {role.category}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="jobTitle">Job Title *</Label>
        <Input
          id="jobTitle"
          placeholder="Senior Veterinarian"
          value={formData.jobTitle}
          onChange={(e) => updateFormData('jobTitle', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="employmentDate">Employment Date *</Label>
        <Input
          id="employmentDate"
          type="date"
          value={formData.employmentDate}
          onChange={(e) => updateFormData('employmentDate', e.target.value)}
        />
      </div>
    </div>

    <div className="space-y-2">
      <Label htmlFor="salary">Annual Salary (USD)</Label>
      <Input
        id="salary"
        type="number"
        placeholder="75000"
        value={formData.salary}
        onChange={(e) => updateFormData('salary', parseInt(e.target.value) || 0)}
      />
    </div>
  </div>
);

const RolePermissionsStep = ({ formData, updateFormData, roles, permissions, selectedRole }: any) => (
  <div className="space-y-6">
    <div className="text-center space-y-2">
      <h3 className="text-lg font-medium">Role & Permissions</h3>
      <p className="text-gray-600">Configure role-based access and special permissions</p>
    </div>

    {selectedRole && (
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Selected Role: {selectedRole.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">{selectedRole.description}</p>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Default Permissions:</Label>
            <div className="flex flex-wrap gap-2">
              {selectedRole.permissions?.map((permId: number) => {
                const permission = permissions.find((p: any) => p.permissionId === permId);
                return permission ? (
                  <Badge key={permId} variant="default">
                    {permission.name}
                  </Badge>
                ) : null;
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    )}

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Special Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Grant Special Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {permissions.map((permission: any) => {
              const isRolePermission = selectedRole?.permissions?.includes(permission.permissionId);
              const isSpecial = formData.specialPermissions.includes(permission.permissionId);

              if (isRolePermission) return null;

              return (
                <div key={permission.permissionId} className="flex items-center space-x-2">
                  <Checkbox
                    id={`special-${permission.permissionId}`}
                    checked={isSpecial}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateFormData('specialPermissions', [...formData.specialPermissions, permission.permissionId]);
                      } else {
                        updateFormData('specialPermissions', formData.specialPermissions.filter((id: number) => id !== permission.permissionId));
                      }
                    }}
                  />
                  <label
                    htmlFor={`special-${permission.permissionId}`}
                    className="text-sm cursor-pointer flex-1"
                  >
                    {permission.name}
                    <span className="text-muted-foreground ml-1">({permission.module})</span>
                  </label>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Revoked Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Revoke Role Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {selectedRole?.permissions?.map((permId: number) => {
              const permission = permissions.find((p: any) => p.permissionId === permId);
              const isRevoked = formData.revokedPermissions.includes(permId);

              if (!permission) return null;

              return (
                <div key={permId} className="flex items-center space-x-2">
                  <Checkbox
                    id={`revoke-${permId}`}
                    checked={isRevoked}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateFormData('revokedPermissions', [...formData.revokedPermissions, permId]);
                      } else {
                        updateFormData('revokedPermissions', formData.revokedPermissions.filter((id: number) => id !== permId));
                      }
                    }}
                  />
                  <label
                    htmlFor={`revoke-${permId}`}
                    className="text-sm cursor-pointer flex-1"
                  >
                    {permission.name}
                    <span className="text-muted-foreground ml-1">({permission.module})</span>
                  </label>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

const ScheduleContactStep = ({ formData, updateFormData, updateNestedFormData }: any) => (
  <div className="space-y-6">
    <div className="text-center space-y-2">
      <h3 className="text-lg font-medium">Schedule & Emergency Contact</h3>
      <p className="text-gray-600">Set work schedule and emergency contact information</p>
    </div>

    {/* Work Schedule */}
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Work Schedule</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="workStart">Work Hours Start</Label>
            <Input
              id="workStart"
              type="time"
              value={formData.schedule.workHours.start}
              onChange={(e) => updateNestedFormData('schedule', 'workHours', {
                ...formData.schedule.workHours,
                start: e.target.value
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="workEnd">Work Hours End</Label>
            <Input
              id="workEnd"
              type="time"
              value={formData.schedule.workHours.end}
              onChange={(e) => updateNestedFormData('schedule', 'workHours', {
                ...formData.schedule.workHours,
                end: e.target.value
              })}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label>Work Days</Label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {WORK_DAYS.map((day) => (
              <div key={day.key} className="flex items-center space-x-2">
                <Checkbox
                  id={`workday-${day.key}`}
                  checked={formData.schedule.workDays.includes(day.key)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateNestedFormData('schedule', 'workDays', [...formData.schedule.workDays, day.key]);
                    } else {
                      updateNestedFormData('schedule', 'workDays', formData.schedule.workDays.filter((d: string) => d !== day.key));
                    }
                  }}
                />
                <label htmlFor={`workday-${day.key}`} className="text-sm cursor-pointer">
                  {day.label}
                </label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>

    {/* Emergency Contact */}
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Emergency Contact</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <Label htmlFor="emergencyName">Contact Name</Label>
            <Input
              id="emergencyName"
              placeholder="Jane Smith"
              value={formData.emergencyContact.name}
              onChange={(e) => updateNestedFormData('emergencyContact', 'name', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="emergencyRelationship">Relationship</Label>
            <Input
              id="emergencyRelationship"
              placeholder="Spouse"
              value={formData.emergencyContact.relationship}
              onChange={(e) => updateNestedFormData('emergencyContact', 'relationship', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="emergencyPhone">Phone Number</Label>
            <Input
              id="emergencyPhone"
              placeholder="+****************"
              value={formData.emergencyContact.phoneNumber}
              onChange={(e) => updateNestedFormData('emergencyContact', 'phoneNumber', e.target.value)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

const ReviewStep = ({ formData, selectedRole }: any) => {
  const getEffectivePermissions = () => {
    const rolePermissions = selectedRole?.permissions || [];
    const effective = new Set([
      ...rolePermissions,
      ...formData.specialPermissions
    ]);

    // Remove revoked permissions
    formData.revokedPermissions.forEach((id: number) => effective.delete(id));

    return Array.from(effective);
  };

  const effectivePermissions = getEffectivePermissions();

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-lg font-medium">Review & Confirm</h3>
        <p className="text-gray-600">Please review all information before creating the staff member</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Personal Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <User className="mr-2 h-4 w-4" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm text-gray-600">Name</Label>
              <p className="font-medium">{formData.firstName} {formData.middleName} {formData.lastName}</p>
            </div>
            <div>
              <Label className="text-sm text-gray-600">Email</Label>
              <p className="text-sm">{formData.email}</p>
            </div>
            <div>
              <Label className="text-sm text-gray-600">Phone</Label>
              <p className="text-sm">{formData.phoneNumber}</p>
            </div>
            {formData.address && (
              <div>
                <Label className="text-sm text-gray-600">Address</Label>
                <p className="text-sm">{formData.address}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Employment Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <Briefcase className="mr-2 h-4 w-4" />
              Employment Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm text-gray-600">Job Title</Label>
              <p className="font-medium">{formData.jobTitle}</p>
            </div>
            <div>
              <Label className="text-sm text-gray-600">Role</Label>
              <p className="text-sm">{selectedRole?.name}</p>
            </div>
            <div>
              <Label className="text-sm text-gray-600">Employment Date</Label>
              <p className="text-sm">{formData.employmentDate}</p>
            </div>
            {formData.salary > 0 && (
              <div>
                <Label className="text-sm text-gray-600">Annual Salary</Label>
                <p className="text-sm">${formData.salary.toLocaleString()}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Schedule & Contact */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Work Schedule
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm text-gray-600">Work Hours</Label>
              <p className="text-sm">{formData.schedule.workHours.start} - {formData.schedule.workHours.end}</p>
            </div>
            <div>
              <Label className="text-sm text-gray-600">Work Days</Label>
              <div className="flex flex-wrap gap-1">
                {formData.schedule.workDays.map((day: string) => (
                  <Badge key={day} variant="outline" className="text-xs">
                    {day.charAt(0).toUpperCase() + day.slice(1, 3)}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <Phone className="mr-2 h-4 w-4" />
              Emergency Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {formData.emergencyContact.name ? (
              <>
                <div>
                  <Label className="text-sm text-gray-600">Name</Label>
                  <p className="text-sm">{formData.emergencyContact.name}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Relationship</Label>
                  <p className="text-sm">{formData.emergencyContact.relationship}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Phone</Label>
                  <p className="text-sm">{formData.emergencyContact.phoneNumber}</p>
                </div>
              </>
            ) : (
              <p className="text-sm text-gray-500">No emergency contact provided</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <Shield className="mr-2 h-4 w-4" />
            Effective Permissions ({effectivePermissions.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {effectivePermissions.map((permId) => (
              <Badge key={permId} variant="default" className="text-xs">
                Permission {permId}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Login Credentials */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <UserCheck className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900">Default Login Credentials</h4>
            <p className="text-sm text-blue-700 mt-1">
              Email: <code className="bg-blue-100 px-1 rounded">{formData.email}</code>
            </p>
            <p className="text-sm text-blue-700">
              Password: <code className="bg-blue-100 px-1 rounded">pass123</code>
            </p>
            <p className="text-sm text-blue-700 mt-1">
              The staff member should change this password on first login for security.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};