/**
 * Auth Controller Tests
 */

import { request, setupTestDB, clearTestData, teardownTestDB } from './setup.js';

// Setup and teardown
beforeAll(async () => await setupTestDB());
afterEach(async () => await clearTestData());
afterAll(async () => await teardownTestDB());

describe('Auth Controller', () => {
  describe('POST /api/v1/auth/sign-up', () => {
    it('should create a new user', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        roleId: 1001
      };
      
      const response = await request
        .post('/api/v1/auth/sign-up')
        .send(userData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.email).toBe(userData.email);
    });
    
    it('should return error for duplicate email', async () => {
      const userData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        roleId: 1001
      };
      
      // Create first user
      await request.post('/api/v1/auth/sign-up').send(userData);
      
      // Try to create duplicate
      const response = await request
        .post('/api/v1/auth/sign-up')
        .send(userData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(false);
      expect(response.body.status).toBe(409); // Conflict
    });
  });
  
  describe('POST /api/v1/auth/sign-in', () => {
    it('should sign in a user with valid credentials', async () => {
      const userData = {
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        roleId: 1001
      };
      
      // Create user
      await request.post('/api/v1/auth/sign-up').send(userData);
      
      // Sign in
      const response = await request
        .post('/api/v1/auth/sign-in')
        .send({
          email: userData.email,
          password: userData.password
        });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
    });
    
    it('should return error for invalid credentials', async () => {
      const response = await request
        .post('/api/v1/auth/sign-in')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword123!'
        });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(false);
      expect(response.body.status).toBe(404); // Not Found
    });
  });
});
