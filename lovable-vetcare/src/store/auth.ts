import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Clinic, Staff, User, StandardResponse } from './types';
import { api } from '@/services/api';
import { toast } from '@/components/ui/use-toast';

interface AuthState {
    isAuthenticated: boolean;
    user: User | null;
    token: string | null;
    staff: Staff | null;
    availableClinics: Clinic[];
    currentClinic: Clinic | null;
    login: (email: string, password: string) => Promise<StandardResponse<{
        token: string;
        user: User;
        staff?: Staff;
        clinics?: Clinic[];
    }>>;
    switchClinic: (clinicId: string) => Promise<StandardResponse<{
        token: string;
        currentClinic: Clinic;
    }>>;
    updateUser: (updatedUser: User) => void;
    updateStaff: (updatedStaff: Staff) => void;
    logout: () => void;
    getToken: () => string | null;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            isAuthenticated: false,
            user: null,
            token: null,
            staff: null,
            availableClinics: [],
            currentClinic: null,
            login: async (email, password) => {
                try {
                    // Our new login flow checks for staff first, then users
                    const response = await loginUser({email, password});
                    const { status, message, data } = response;

                    if (status === 200) {
                        const clinics = data.clinics || [];
                        const userType = data.staff ? 'staff' : (email === '<EMAIL>' ? 'admin' : 'user');

                        set({
                            isAuthenticated: true,
                            user: data.user,
                            token: data.token,
                            staff: data.staff || null,
                            availableClinics: clinics,
                            currentClinic: clinics.length > 0 ? clinics[0] : null
                        });

                        // Log the user type for debugging
                        console.log(`Logged in as: ${userType}`);

                        return {
                            success: true,
                            status,
                            message: message || 'User signed in successfully',
                            data
                        };
                    }
                    return {
                        success: false,
                        status,
                        message: message || 'Login failed',
                        data
                    };
                } catch (error: any) {
                    return {
                        success: false,
                        status: error?.status || 500,
                        message: error?.message || 'Login failed',
                        data: error?.data || {}
                    };
                }
            },
            switchClinic: async (clinicId) => {
                try {
                    const response = await api.post<StandardResponse<{
                        token: string;
                        currentClinic: Clinic;
                    }>>('/clinics/switch', { clinicId });

                    const { status, message, data } = response;

                    if (status === 200) {
                        set({
                            token: data.token,
                            currentClinic: data.currentClinic
                        });

                        toast({
                            title: "Success",
                            description: `Switched to ${data.currentClinic.clinicName}`,
                        });

                        return {
                            success: true,
                            status,
                            message: message || 'Clinic switched successfully',
                            data
                        };
                    }

                    toast({
                        variant: "destructive",
                        title: "Error",
                        description: message || 'Failed to switch clinic',
                    });

                    return {
                        success: false,
                        status,
                        message: message || 'Failed to switch clinic',
                        data
                    };
                } catch (error: any) {
                    toast({
                        variant: "destructive",
                        title: "Error",
                        description: error?.message || 'Failed to switch clinic',
                    });

                    return {
                        success: false,
                        status: error?.status || 500,
                        message: error?.message || 'Failed to switch clinic',
                        data: error?.data || {}
                    };
                }
            },
            logout: () => {
                set({
                    isAuthenticated: false,
                    user: null,
                    token: null,
                    staff: null,
                    availableClinics: [],
                    currentClinic: null
                });
                window.location.href = '/';
            },
            updateUser: (updatedUser: User) => {
                set({ user: updatedUser });

                // Register the update function on the window object for global access
                window.updateUser = (user: User) => {
                    set({ user });
                };
            },
            updateStaff: (updatedStaff: Staff) => {
                set({ staff: updatedStaff });

                // Register the update function on the window object for global access
                window.updateStaff = (staff: Staff) => {
                    set({ staff });
                };
            },
            getToken: () => get().token,
        }),
        { name: 'auth-storage' }
    )
);

// Remove the duplicate axios interceptors since they're now in api.ts

// Initialize the window update functions
if (typeof window !== 'undefined') {
    window.updateUser = (user: User) => {
        useAuthStore.getState().updateUser(user);
    };
    window.updateStaff = (staff: Staff) => {
        useAuthStore.getState().updateStaff(staff);
    };
}

// Use the new api client for login
const loginUser = async (credentials: {email: string, password: string}) => {
    // Our new backend login flow checks for staff first, then users
    return api.post<StandardResponse<{
        token: string;
        user: User;
        staff?: Staff;
        clinics?: Clinic[];
    }>>('/auth/sign-in', credentials);
};
