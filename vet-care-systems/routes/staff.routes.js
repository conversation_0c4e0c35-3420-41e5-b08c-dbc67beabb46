import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    registerStaff,
    getAllStaff,
    getStaffById,
    updateStaff,
    deleteStaff,
    updateStaffProfile,
    grantSpecialPermission,
    revokePermission,
    resetStaffPermissions,
    getStaffPermissions,
    getClinicOwners
} from '../controllers/staff.controller.js';

const router = Router();

// Base route: /api/staff

// Protected routes
router.use(verifyToken);

// Staff permission management routes
router.post('/:staffId/permissions', grantSpecialPermission);
router.delete('/:staffId/permissions', revokePermission);
router.put('/:staffId/permissions/reset', resetStaffPermissions);
router.get('/:staffId/permissions', getStaffPermissions);

// Existing routes
router.post('/', registerStaff);
router.get('/', getAllStaff);
router.get('/owners', getClinicOwners); // Get clinic owners for assignment
router.get('/:staffId', getStaffById);
router.put('/:staffId', updateStaff);
router.put('/:staffId/profile', updateStaffProfile);
router.delete('/:staffId', deleteStaff);

export default router;
