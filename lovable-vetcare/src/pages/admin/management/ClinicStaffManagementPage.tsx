import { ClinicStaffManager } from "@/components/admin/ClinicStaffManager";
import { useAuth } from "@/store";
import { Navigate } from "react-router-dom";

const ClinicStaffManagementPage = () => {
  const { user } = useAuth();

  // Check if user has admin access
  const isAdmin = user?.role === 'super_admin';
  
  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ClinicStaffManager />
    </div>
  );
};

export default ClinicStaffManagementPage;
