import ServiceType from '../models/serviceType.model.js';
import { sendResponse } from '../utils/responseHandler.js';

export const createServiceType = async (req, res) => {
    try {
        const { name, description, category, minCharge, maxCharge, defaultDuration, currency } = req.body;
        
        // Check if service type already exists
        const existingServiceType = await ServiceType.findOne({ name });
        if (existingServiceType) {
            return sendResponse(res, 400, false, "Service type with this name already exists");
        }
        
        // Create new service type
        const serviceType = new ServiceType({
            name,
            description,
            category,
            minCharge,
            maxCharge,
            defaultDuration,
            currency: currency || 'KES', 
            isActive: true
        });
        
        await serviceType.save();
        
        return sendResponse(res, 201, true, "Service type created successfully", serviceType);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllServiceTypes = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 50, 
            sortBy = "name", 
            sortOrder = "asc",
            category,
            isActive
        } = req.query;
        
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };
        
        let query = {};
        if (category) query.category = category;
        if (isActive !== undefined) query.isActive = isActive === 'true';
        
        const serviceTypes = await ServiceType.find(query)
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));
            
        const total = await ServiceType.countDocuments(query);
        
        return sendResponse(res, 200, true, "Service types retrieved successfully", {
            serviceTypes,
            pagination: {
                total,
                page: parseInt(page),
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getServiceTypeById = async (req, res) => {
    try {
        const { serviceTypeId } = req.params;
        
        const serviceType = await ServiceType.findOne({ serviceTypeId: parseInt(serviceTypeId) });
        if (!serviceType) {
            return sendResponse(res, 404, false, "Service type not found");
        }
        
        return sendResponse(res, 200, true, "Service type retrieved successfully", serviceType);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateServiceType = async (req, res) => {
    try {
        const { serviceTypeId } = req.params;
        const updates = req.body;
        
        // Validate min/max charge relationship if both are provided
        if (updates.minCharge !== undefined && updates.maxCharge !== undefined) {
            if (updates.minCharge > updates.maxCharge) {
                return sendResponse(res, 400, false, "Minimum charge cannot be greater than maximum charge");
            }
        } else if (updates.minCharge !== undefined) {
            // If only minCharge is provided, check against existing maxCharge
            const serviceType = await ServiceType.findOne({ serviceTypeId: parseInt(serviceTypeId) });
            if (serviceType && updates.minCharge > serviceType.maxCharge) {
                return sendResponse(res, 400, false, "Minimum charge cannot be greater than existing maximum charge");
            }
        } else if (updates.maxCharge !== undefined) {
            // If only maxCharge is provided, check against existing minCharge
            const serviceType = await ServiceType.findOne({ serviceTypeId: parseInt(serviceTypeId) });
            if (serviceType && updates.maxCharge < serviceType.minCharge) {
                return sendResponse(res, 400, false, "Maximum charge cannot be less than existing minimum charge");
            }
        }
        
        const updatedServiceType = await ServiceType.findOneAndUpdate(
            { serviceTypeId: parseInt(serviceTypeId) },
            updates,
            { new: true, runValidators: true }
        );
        
        if (!updatedServiceType) {
            return sendResponse(res, 404, false, "Service type not found");
        }
        
        return sendResponse(res, 200, true, "Service type updated successfully", updatedServiceType);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const deleteServiceType = async (req, res) => {
    try {
        const { serviceTypeId } = req.params;
        
        const deletedServiceType = await ServiceType.findOneAndDelete({ serviceTypeId: parseInt(serviceTypeId) });
        if (!deletedServiceType) {
            return sendResponse(res, 404, false, "Service type not found");
        }
        
        return sendResponse(res, 200, true, "Service type deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getServiceTypesByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        
        const serviceTypes = await ServiceType.find({ 
            category, 
            isActive: true 
        }).sort({ name: 1 });
        
        return sendResponse(res, 200, true, "Service types retrieved successfully", serviceTypes);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};