import express from 'express';
import {
    createAppointmentNote,
    getAppointmentNotes,
    updateAppointmentNote,
    deleteAppointmentNote,
    getNotesByCategory,
    archiveAppointmentNote
} from '../controllers/appointmentNote.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Create a new note for an appointment
router.post('/appointment/:appointmentId/notes', createAppointmentNote);

// Get all notes for an appointment
router.get('/appointment/:appointmentId/notes', getAppointmentNotes);

// Get notes by category for an appointment
router.get('/appointment/:appointmentId/notes/category/:category', getNotesByCategory);

// Update a specific note
router.put('/notes/:noteId', updateAppointmentNote);

// Archive a specific note
router.patch('/notes/:noteId/archive', archiveAppointmentNote);

// Delete a specific note
router.delete('/notes/:noteId', deleteAppointmentNote);

export default router;
