import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { getHealthRecordsByPet, HealthRecord } from "@/services/healthRecord";
import { Eye, FileText, Calendar, Activity, Pill } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface PetMedicalHistoryProps {
  petId: string;
}

const PetMedicalHistory = ({ petId }: PetMedicalHistoryProps) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("all");

  // Fetch health records for this pet
  const { data: recordsData, isLoading } = useQuery({
    queryKey: ['healthRecords', petId, activeTab],
    queryFn: () => getHealthRecordsByPet(
      petId,
      {
        recordType: activeTab !== 'all' ? activeTab : undefined,
        includeDetails: true,
        limit: 50
      }
    ),
    enabled: !!petId
  });

  const healthRecords = recordsData?.data?.data || [];

  // Filter records by type based on active tab
  const getFilteredRecords = (type?: string) => {
    if (!type || type === 'all') return healthRecords;
    return healthRecords.filter(record => record.recordType.toLowerCase() === type.toLowerCase());
  };

  const consultations = getFilteredRecords('consultation');
  const vaccinations = getFilteredRecords('vaccination');
  const surgeries = getFilteredRecords('surgery');
  const labTests = getFilteredRecords('laboratory');

  const handleViewRecord = (recordId: string) => {
    navigate(`/records/${recordId}`);
  };

  const getRecordTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      consultation: "bg-blue-100 text-blue-800",
      vaccination: "bg-green-100 text-green-800",
      surgery: "bg-red-100 text-red-800",
      laboratory: "bg-purple-100 text-purple-800",
      imaging: "bg-yellow-100 text-yellow-800",
      other: "bg-gray-100 text-gray-800"
    };

    return (
      <Badge className={colors[type.toLowerCase()] || colors.other}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const renderRecordCard = (record: HealthRecord) => (
    <div key={record._id} className="border rounded-md p-4 hover:bg-gray-50">
      <div className="flex justify-between items-start mb-2">
        <div className="flex items-center gap-2">
          {getRecordTypeBadge(record.recordType)}
          <span className="text-sm text-muted-foreground">
            {record.date ? format(new Date(record.date), 'PPP') : 'Unknown date'}
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewRecord(record._id)}
        >
          <Eye className="h-4 w-4 mr-1" /> View
        </Button>
      </div>

      {record.diagnosis && (
        <div className="mb-2">
          <span className="font-semibold">Diagnosis:</span> {record.diagnosis}
        </div>
      )}

      {record.description && (
        <div className="mb-2 text-sm text-muted-foreground line-clamp-2">
          {record.description}
        </div>
      )}

      <div className="flex items-center justify-between text-sm mt-2">
        <div>
          <span className="text-muted-foreground">By:</span> {record.performedBy?.firstName} {record.performedBy?.lastName}
        </div>
        <div>
          <span className="text-muted-foreground">Clinic:</span> {record.clinicId?.clinicName}
        </div>
      </div>
    </div>
  );

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Medical History</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">
              <FileText className="h-4 w-4 mr-2" /> All Records
            </TabsTrigger>
            <TabsTrigger value="consultation">
              <Activity className="h-4 w-4 mr-2" /> Consultations
            </TabsTrigger>
            <TabsTrigger value="vaccination">
              <Pill className="h-4 w-4 mr-2" /> Vaccinations
            </TabsTrigger>
            <TabsTrigger value="surgery">
              <Calendar className="h-4 w-4 mr-2" /> Surgeries
            </TabsTrigger>
            <TabsTrigger value="laboratory">
              <Calendar className="h-4 w-4 mr-2" /> Lab Tests
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {isLoading ? (
              <p className="text-center py-4">Loading medical records...</p>
            ) : healthRecords.length === 0 ? (
              <p className="text-center py-4 text-gray-500">No medical records found</p>
            ) : (
              <div className="space-y-4">
                {healthRecords.map(renderRecordCard)}
              </div>
            )}
          </TabsContent>

          <TabsContent value="consultation">
            {isLoading ? (
              <p className="text-center py-4">Loading consultation records...</p>
            ) : consultations.length === 0 ? (
              <p className="text-center py-4 text-gray-500">No consultation records found</p>
            ) : (
              <div className="space-y-4">
                {consultations.map(renderRecordCard)}
              </div>
            )}
          </TabsContent>

          <TabsContent value="vaccination">
            {isLoading ? (
              <p className="text-center py-4">Loading vaccination records...</p>
            ) : vaccinations.length === 0 ? (
              <p className="text-center py-4 text-gray-500">No vaccination records found</p>
            ) : (
              <div className="space-y-4">
                {vaccinations.map(renderRecordCard)}
              </div>
            )}
          </TabsContent>

          <TabsContent value="surgery">
            {isLoading ? (
              <p className="text-center py-4">Loading surgery records...</p>
            ) : surgeries.length === 0 ? (
              <p className="text-center py-4 text-gray-500">No surgery records found</p>
            ) : (
              <div className="space-y-4">
                {surgeries.map(renderRecordCard)}
              </div>
            )}
          </TabsContent>

          <TabsContent value="laboratory">
            {isLoading ? (
              <p className="text-center py-4">Loading laboratory records...</p>
            ) : labTests.length === 0 ? (
              <p className="text-center py-4 text-gray-500">No laboratory test records found</p>
            ) : (
              <div className="space-y-4">
                {labTests.map(renderRecordCard)}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default PetMedicalHistory;