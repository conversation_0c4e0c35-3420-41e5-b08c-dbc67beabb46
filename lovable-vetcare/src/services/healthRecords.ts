import { api } from './api';

export interface HealthRecord {
  healthRecordId: number;
  petId: number;
  serviceId: number;
  performedBy: number;
  clinicId: number;
  appointmentId?: number;
  petClinicRelationshipId: number;
  recordType: 'consultation' | 'vaccination' | 'surgery' | 'laboratory' | 'imaging' | 'prescription' | 'other';
  date: string;
  description: string;
  diagnosis?: string;
  treatment?: string;
  medications?: Array<{
    name: string;
    dosage: string;
    frequency: string;
    duration?: string;
    notes?: string;
    prescribedBy?: number;
  }>;
  labResults?: Array<{
    testName: string;
    result: string;
    normalRange?: string;
    interpretation?: string;
    performedBy?: number;
    attachmentUrl?: string;
  }>;
  vitalSigns?: {
    temperature?: number;
    heartRate?: number;
    respiratoryRate?: number;
    weight?: number;
    bloodPressure?: string;
  };
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileType: string;
    description?: string;
    uploadDate: string;
    uploadedBy?: number;
  }>;
  followUpDate?: string;
  followUpInstructions?: string;
  followUpStatus?: 'scheduled' | 'completed' | 'missed' | 'cancelled' | 'none';
  notes?: string;
  billingDetails: {
    amount: number;
    currency: string;
    paymentStatus: 'pending' | 'paid' | 'partially_paid' | 'waived';
    invoiceId?: number;
  };
  accessControl?: {
    isPublic: boolean;
    restrictedTo?: number[];
    accessReason?: string;
  };
  createdAt: string;
  updatedAt: string;
  // Populated fields
  petData?: any;
  serviceData?: any;
  staffData?: any;
  discounts?: Discount[];
  receipt?: Receipt;
}

export interface Discount {
  discountId: number;
  healthRecordId: number;
  appointmentId?: number;
  discountType: 'percentage' | 'fixed_amount' | 'waiver';
  discountValue: number;
  discountAmount: number;
  originalAmount: number;
  finalAmount: number;
  currency: string;
  reason: string;
  authorizedBy: number;
  clientId: number;
  clinicId: number;
  category: 'senior_discount' | 'loyalty_discount' | 'emergency_waiver' | 'staff_discount' | 'promotional' | 'hardship' | 'other';
  status: 'active' | 'revoked' | 'expired';
  approvalRequired: boolean;
  approvedBy?: number;
  approvalDate?: string;
  validFrom: string;
  validUntil?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Receipt {
  receiptId: number;
  receiptNumber: string;
  healthRecordId: number;
  appointmentId?: number;
  clientId: number;
  petId: number;
  clinicId: number;
  services: Array<{
    serviceId: number;
    serviceName: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  subtotal: number;
  totalDiscount: number;
  tax: number;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  currency: string;
  discounts: Array<{
    discountId: number;
    discountType: string;
    discountValue: number;
    discountAmount: number;
    reason: string;
  }>;
  paymentMethod: 'cash' | 'card' | 'mobile_money' | 'bank_transfer' | 'insurance' | 'credit';
  paymentStatus: 'paid' | 'partially_paid' | 'pending' | 'overdue' | 'waived';
  paymentDate?: string;
  issuedBy: number;
  status: 'draft' | 'issued' | 'cancelled' | 'refunded';
  issueDate: string;
  dueDate?: string;
  notes?: string;
  template: string;
  emailSent: boolean;
  emailSentDate?: string;
  smsSent: boolean;
  smsSentDate?: string;
  createdAt: string;
  updatedAt: string;
  // Populated fields
  healthRecord?: HealthRecord;
  clientData?: any;
  petData?: any;
  clinicData?: any;
  staffData?: any;
}

export interface HealthRecordResponse {
  success: boolean;
  status: number;
  message: string;
  data: HealthRecord | null;
}

export interface HealthRecordsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: HealthRecord[];
    pagination: {
      totalRecords: number;
      totalPages: number;
      currentPage: number;
      recordsPerPage: number;
    };
  } | null;
}

export interface DiscountResponse {
  success: boolean;
  status: number;
  message: string;
  data: Discount | null;
}

export interface ReceiptResponse {
  success: boolean;
  status: number;
  message: string;
  data: Receipt | null;
}

// Health Records API
export const createHealthRecord = async (healthRecordData: Partial<HealthRecord>): Promise<HealthRecordResponse> => {
  try {
    return await api.post<HealthRecordResponse>('/health-records', healthRecordData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create health record',
      data: null
    };
  }
};

export const getHealthRecordsByPet = async (petId: string | number, params: any = {}): Promise<HealthRecordsResponse> => {
  try {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) queryParams.append(key, String(value));
    });
    
    const url = `/health-records/pet/${petId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<HealthRecordsResponse>(url);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch health records',
      data: null
    };
  }
};

export const getHealthRecordById = async (healthRecordId: string | number): Promise<HealthRecordResponse> => {
  try {
    return await api.get<HealthRecordResponse>(`/health-records/${healthRecordId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch health record',
      data: null
    };
  }
};

export const updateHealthRecord = async (healthRecordId: string | number, data: Partial<HealthRecord>): Promise<HealthRecordResponse> => {
  try {
    return await api.put<HealthRecordResponse>(`/health-records/${healthRecordId}`, data);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update health record',
      data: null
    };
  }
};

// Discount API
export const applyDiscount = async (healthRecordId: string | number, discountData: Partial<Discount>): Promise<DiscountResponse> => {
  try {
    return await api.post<DiscountResponse>(`/health-records/${healthRecordId}/discount`, discountData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to apply discount',
      data: null
    };
  }
};

// Receipt API
export const generateReceipt = async (healthRecordId: string | number, clientId: string | number): Promise<ReceiptResponse> => {
  try {
    return await api.post<ReceiptResponse>(`/health-records/${healthRecordId}/receipt`, { clientId });
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to generate receipt',
      data: null
    };
  }
};

export const getReceiptByHealthRecord = async (healthRecordId: string | number): Promise<ReceiptResponse> => {
  try {
    return await api.get<ReceiptResponse>(`/health-records/${healthRecordId}/receipt`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch receipt',
      data: null
    };
  }
};

// Complete appointment and create health record
export const completeAppointment = async (appointmentId: string | number, completionData: any): Promise<any> => {
  try {
    return await api.post(`/appointments/${appointmentId}/complete`, completionData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to complete appointment',
      data: null
    };
  }
};
