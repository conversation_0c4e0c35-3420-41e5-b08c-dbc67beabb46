
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { getStaff, updateStaff } from "@/services/staff";
import { Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, RefreshCw, Filter, CheckCircle, XCircle, Shield, Plus, Search } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Staff as StaffType } from "@/store/types";
import { useToast } from "@/components/ui/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import AddStaffToClinicModal from "../clinics/components/AddStaffToClinicModal";

const Staff = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchName, setSearchName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
  const [limit, setLimit] = useState(10);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["staff", currentPage, limit, statusFilter, searchName, email, phone],
    queryFn: async () => {
      const statusValue = statusFilter !== "all" ? parseInt(statusFilter) : undefined;
      const response = await getStaff({
        page: currentPage,
        limit: limit,
        status: statusValue,
        search: searchName || undefined,
        email: email || undefined,
        phone: phone || undefined,
        sortBy: "createdAt",
        sortOrder: "desc"
      });
      return response.data;
    },
  });

  const staff = data?.data || [];
  const totalPages = data?.pagination?.totalPages || 1;

  const handleStatusToggle = async (staffId: string | number, currentStatus: number) => {
    try {
      const newStatus = currentStatus === 1 ? 0 : 1;
      await updateStaff(staffId, { status: newStatus });
      await queryClient.invalidateQueries({ queryKey: ["staff"] });
      toast({
        title: "Success",
        description: `Staff status updated to ${newStatus === 1 ? "Active" : "Inactive"}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update staff status",
        variant: "destructive"
      });
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    refetch();
  };

  const resetFilters = () => {
    setSearchName("");
    setEmail("");
    setPhone("");
    setCurrentPage(1);
    refetch();
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setCurrentPage(1);
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Staff Management</h1>
        <div className="flex gap-2">
          <AddStaffToClinicModal
            clinicId=""
            clinicName="New Staff"
            trigger={
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Add Staff Member
              </Button>
            }
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: ["staff"] });
              toast({
                title: "Success",
                description: "Staff member added successfully",
              });
            }}
          />
        </div>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-grow">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by staff name..."
                  value={searchName}
                  onChange={(e) => setSearchName(e.target.value)}
                  className="pl-8"
                />
              </div>

              <Button
                variant="outline"
                onClick={() => setIsAdvancedSearchOpen(!isAdvancedSearchOpen)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {isAdvancedSearchOpen ? "Hide Filters" : "Show Filters"}
              </Button>

              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value);
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="1">Active</SelectItem>
                  <SelectItem value="0">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={String(limit)}
                onValueChange={handleLimitChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select page size"/>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="15">15 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {isAdvancedSearchOpen && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    placeholder="Search by email..."
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Phone Number</label>
                  <Input
                    placeholder="Search by phone number..."
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>

          {isAdvancedSearchOpen && (
            <div className="flex justify-end gap-4 items-center mt-4">
              <Button variant="outline" onClick={resetFilters}>
                Reset
              </Button>
              <Button onClick={handleSearch}>
                Apply Filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {Array.isArray(staff) && staff.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">No staff members found</div>
      ) : (
        <>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Job Title</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right pr-6">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => refetch()}
                      className="ml-auto"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(staff) && staff.map((member) => (
                  <TableRow key={member.staffId || member._id}>
                    <TableCell>{member.staffId}</TableCell>
                    <TableCell>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-left"
                        onClick={() => window.location.href = `/admin/staff/${member.staffId || member._id}`}
                      >
                        {member.name ||
                         member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` :
                         (typeof member.userId === 'object' ?
                          `${member.userId.firstName || ''} ${member.userId.lastName || ''}`.trim() :
                          'Unknown')}
                      </Button>
                    </TableCell>
                    <TableCell>
                      {member.email || (typeof member.userId === 'object' ? member.userId.email : 'N/A')}
                    </TableCell>
                    <TableCell>
                      {member.phoneNumber || (typeof member.userId === 'object' ? member.userId.phoneNumber : 'N/A')}
                    </TableCell>
                    <TableCell>{member.jobTitle || 'N/A'}</TableCell>
                    <TableCell>
                      {typeof member.role === 'object'
                        ? (member.role?.roleName || member.role?.name || 'Unknown')
                        : (member.role || 'Unknown')}
                    </TableCell>
                    <TableCell>
                      {member.status === 1 ? (
                        <Badge variant="default" className="bg-green-500">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-gray-400">
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[160px]">
                          <DropdownMenuItem asChild>
                            <Link to={`/admin/staff/${member.staffId || member._id}`} className="w-full">View Profile</Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link to={`/admin/staff/${member.staffId || member._id}/edit`} className="w-full">Edit Profile</Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link to={`/admin/staff/${member.staffId || member._id}/permissions`} className="w-full flex items-center gap-2">
                              <Shield className="h-4 w-4" />
                              Edit Permissions
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusToggle(member.staffId || member._id || '', member.status || 0)}>
                            {member.status === 1 ? (
                              <span className="flex items-center gap-2">
                                <XCircle className="h-4 w-4 text-red-500" />
                                Deactivate
                              </span>
                            ) : (
                              <span className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                Activate
                              </span>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </>
      )}

          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, data?.pagination?.totalCount || 0)} of {data?.pagination?.totalCount || 0} entries
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
    </div>
  );
};

export default Staff;
