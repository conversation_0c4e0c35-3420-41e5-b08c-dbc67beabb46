import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    dispenseMedication,
    getAllMedicationDispensing,
    getMedicationDispensingById,
    updateMedicationDispensing
} from "../controllers/medicationDispensing.controller.js";

const medicationDispensingRouter = Router();

// Apply authorization middleware to all routes
medicationDispensingRouter.use(verifyToken);

// Medication dispensing routes
medicationDispensingRouter.post('/', dispenseMedication);
medicationDispensingRouter.get('/', getAllMedicationDispensing);
medicationDispensingRouter.get('/:dispensingId', getMedicationDispensingById);
medicationDispensingRouter.put('/:dispensingId', updateMedicationDispensing);

export default medicationDispensingRouter;
