import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createAppointment,
    getAllAppointments,
    getAppointmentById,
    updateAppointment,
    deleteAppointment,
    getAppointmentTypes,
    completeAppointment,
    reassignAppointment,
    updateAppointmentProgress,
} from "../controllers/appointment.controller.js";

const appointmentRouter = Router();

// Add progress route without authentication BEFORE applying verifyToken middleware
appointmentRouter.put('/:appointmentId/progress', updateAppointmentProgress);

// Apply authentication to all other routes
appointmentRouter.use(verifyToken);

appointmentRouter.get('/types', getAppointmentTypes);
appointmentRouter.post('/', createAppointment);
appointmentRouter.get('/', getAllAppointments);

// Test route without additional auth
appointmentRouter.get('/:appointmentId/progress', (req, res) => {
  console.log('GET progress route hit with params:', req.params);
  res.json({ message: 'Progress route is working', appointmentId: req.params.appointmentId });
});

// Temporarily disable auth for PUT progress route
appointmentRouter.put('/:appointmentId/progress', (req, res, next) => {
  console.log('PUT progress route hit with params:', req.params);
  console.log('PUT progress route hit with body:', req.body);
  updateAppointmentProgress(req, res, next);
});

appointmentRouter.post('/:appointmentId/complete', completeAppointment);
appointmentRouter.put('/:appointmentId/reassign', reassignAppointment);
appointmentRouter.get('/:appointmentId', getAppointmentById);
appointmentRouter.put('/:appointmentId', updateAppointment);
appointmentRouter.delete('/:appointmentId', deleteAppointment);

export default appointmentRouter;