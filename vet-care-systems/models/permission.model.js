import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const permissionSchema = new mongoose.Schema({
    // Auto-increment ID
    permissionId: {
        type: Number,
        index: true,
        unique: true,
    },
    name: {
        type: String,
        required: [true, 'Permission name is required'],
        trim: true,
        index: true,
    },
    description: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        enum: ['system', 'clinic', 'client', ],
        required: [true, 'Permission category is required'],
        trim: true,
        index: true
    },
    module: {
        type: String,
        required: [true, 'Module is required'],
        trim: true,
        index: true,
    },
    status: {
        type: Number,
        enum: [0, 1],
        default: 1,
        index: true,
    }
}, {
    timestamps: true,
    collection: 'permissions'
});

// Add auto-increment plugin
permissionSchema.plugin(AutoIncrement, { inc_field: 'permissionId', start_seq: 1001 });
permissionSchema.index({ permissionId: 1 }, { unique: true });
permissionSchema.index({ name: 1 }, { unique: true });
permissionSchema.index({ module: 1 });
permissionSchema.index({ category: 1 });

const defaultPermissions = [
    { name: 'all_access', description: 'Full system access', category: 'system', module: 'system' },
    { name: 'clinic_management', description: 'Manage clinic settings', category: 'clinic', module: 'clinic' },
    { name: 'staff_management', description: 'Manage staff members', category: 'clinic', module: 'staff' },
    { name: 'patient_management', description: 'Manage patients', category: 'clinic', module: 'patient' },
    { name: 'medical_records', description: 'Access medical records', category: 'clinic', module: 'medical' },
    { name: 'appointment_management', description: 'Manage appointments', category: 'clinic', module: 'appointment' },
    { name: 'inventory_management', description: 'Manage inventory', category: 'clinic', module: 'inventory' },
    { name: 'billing_management', description: 'Manage billing', category: 'clinic', module: 'billing' },
    { name: 'basic_access', description: 'Basic system access', category: 'system', module: 'system' }
];

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = 'permissions';
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        const Permission = mongoose.model('Permission', permissionSchema);
        await Permission.init();
        console.log(`Recreated collection: ${collectionName}`);

        // Initialize default permissions after recreation
        await Permission.initializePermissions();
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

permissionSchema.statics.initializePermissions = async function() {
    try {
        const existingPermissions = await this.find().lean();

        if (existingPermissions.length > 0) {
            console.log('Existing permissions found:');
            console.table(existingPermissions.map(p => ({
                ID: p.permissionId,
                Name: p.name,
                Module: p.module,
                Status: p.status
            })));
            return;
        }

        const inserted = await this.insertMany(defaultPermissions);
        console.log('Default permissions created successfully:');
        console.table(inserted.map(p => ({
            ID: p.permissionId,
            Name: p.name,
            Module: p.module,
            Status: p.status
        })));
    } catch (error) {
        console.error('Error initializing permissions:', error);
    }
};

const Permission = mongoose.model('Permission', permissionSchema);

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Permission;