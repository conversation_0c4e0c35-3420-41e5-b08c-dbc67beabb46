
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Search, Filter, MoreHorizontal } from "lucide-react";
import { Input } from "@/components/ui/input";
import MedicalRecordsNav from "./components/MedicalRecordsNav";
import AddConsultationModal from "./components/AddConsultationModal";

const Consultations = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Medical Consultations</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Consultation
        </Button>
      </div>
      
      <MedicalRecordsNav />
      
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Consultation Records</CardTitle>
            <div className="flex gap-2">
              <Input placeholder="Search records..." className="w-64" />
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Table implementation will go here */}
          <p className="text-center py-8 text-gray-500">No consultation records found</p>
        </CardContent>
      </Card>

      <AddConsultationModal 
        open={isAddModalOpen} 
        onOpenChange={setIsAddModalOpen} 
      />
    </div>
  );
};

export default Consultations;
