import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Payment Model
 * 
 * This model represents payments made for invoices.
 * It supports multiple payment methods and tracks payment details.
 */
const paymentSchema = new mongoose.Schema({
    // Auto-increment ID
    paymentId: {
        type: Number,
        index: true,
        unique: true,
    },
    
    // Payment reference number
    paymentReference: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Related records
    invoiceId: {
        type: Number,
        required: true,
        index: true
    },
    appointmentId: {
        type: Number,
        required: true,
        index: true
    },
    
    // Client information
    clientId: {
        type: Number,
        required: true,
        index: true
    },
    
    // Clinic information
    clinicId: {
        type: Number,
        required: true,
        index: true
    },
    
    // Payment details
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    
    currency: {
        type: String,
        required: true,
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },
    
    // Payment method
    paymentMethod: {
        type: String,
        enum: ['cash', 'mpesa', 'credit_card', 'debit_card', 'bank_transfer', 'cheque', 'insurance'],
        required: true,
        index: true
    },
    
    // Payment method specific details
    paymentDetails: {
        // For M-Pesa
        mpesaTransactionId: String,
        mpesaReceiptNumber: String,
        mpesaPhoneNumber: String,
        
        // For card payments
        cardLastFourDigits: String,
        cardType: String, // visa, mastercard, etc.
        authorizationCode: String,
        
        // For bank transfer
        bankReference: String,
        bankName: String,
        accountNumber: String,
        
        // For cheque
        chequeNumber: String,
        chequeBank: String,
        chequeDate: Date,
        
        // For insurance
        insuranceProvider: String,
        insuranceClaimNumber: String,
        insurancePolicyNumber: String
    },
    
    // Payment status
    status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
        default: 'pending',
        index: true
    },
    
    // Payment date and time
    paymentDate: {
        type: Date,
        required: true,
        default: Date.now
    },
    
    processedDate: Date,
    
    // Staff who processed the payment
    processedBy: {
        type: Number,
        required: true,
        index: true
    },
    
    // Transaction fees
    transactionFee: {
        type: Number,
        default: 0,
        min: 0
    },
    
    netAmount: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Notes and description
    description: {
        type: String,
        trim: true
    },
    
    notes: {
        type: String,
        trim: true
    },
    
    // Refund information
    refundAmount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    refundDate: Date,
    
    refundReason: String,
    
    refundedBy: {
        type: Number
    },
    
    // External system references
    externalTransactionId: String,
    externalSystemResponse: mongoose.Schema.Types.Mixed
}, { timestamps: true });

// Add indexes for better query performance
paymentSchema.index({ invoiceId: 1 });
paymentSchema.index({ clientId: 1, paymentDate: -1 });
paymentSchema.index({ clinicId: 1, paymentDate: -1 });
paymentSchema.index({ paymentMethod: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ paymentDate: -1 });

// Pre-save middleware to generate payment reference
paymentSchema.pre('save', async function(next) {
    if (this.isNew && !this.paymentReference) {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const day = String(new Date().getDate()).padStart(2, '0');
        const count = await this.constructor.countDocuments({
            paymentDate: {
                $gte: new Date(year, new Date().getMonth(), new Date().getDate()),
                $lt: new Date(year, new Date().getMonth(), new Date().getDate() + 1)
            }
        });
        this.paymentReference = `PAY-${year}${month}${day}-${String(count + 1).padStart(4, '0')}`;
    }
    
    // Calculate net amount (amount minus transaction fee)
    this.netAmount = this.amount - this.transactionFee;
    
    // Set processed date when status changes to completed
    if (this.status === 'completed' && !this.processedDate) {
        this.processedDate = new Date();
    }
    
    next();
});

// Add auto-increment plugin
paymentSchema.plugin(AutoIncrement, { inc_field: 'paymentId', start_seq: 1001 });
paymentSchema.index({ paymentId: 1 }, { unique: true });

const Payment = mongoose.model('Payment', paymentSchema);

export default Payment;
