import Receipt from "../models/receipt.model.js";
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Get all receipts with pagination
 */
export const getAllReceipts = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        
        const clinicId = req.user?.clinicId || req.staff?.clinicId;
        
        const query = clinicId ? { clinicId } : {};
        
        const [receipts, totalCount] = await Promise.all([
            Receipt.find(query)
                .sort({ createdAt: -1 })
                .skip(offset)
                .limit(limit)
                .lean(),
            Receipt.countDocuments(query)
        ]);
        
        const totalPages = Math.ceil(totalCount / limit);
        
        return sendResponse(res, 200, true, "Receipts retrieved successfully", {
            data: receipts,
            pagination: {
                totalCount,
                page,
                limit,
                offset,
                totalPages
            }
        });
    } catch (error) {
        console.error("Get receipts error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipts: ${error.message}`);
    }
};

/**
 * Get receipt by ID
 */
export const getReceiptById = async (req, res) => {
    try {
        const { receiptId } = req.params;
        
        const receipt = await Receipt.findOne({ 
            receiptId: parseInt(receiptId) 
        }).lean();
        
        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found");
        }
        
        // Get related data
        const [clientData, petData, staffData] = await Promise.all([
            Receipt.model('Client').findOne({ clientId: receipt.clientId }).lean(),
            Receipt.model('Pet').findOne({ petId: receipt.petId }).lean(),
            Receipt.model('Staff').findOne({ staffId: receipt.issuedBy }).lean()
        ]);
        
        // Add related data to receipt
        receipt.clientData = clientData;
        receipt.petData = petData;
        receipt.staffData = staffData;
        
        return sendResponse(res, 200, true, "Receipt retrieved successfully", receipt);
    } catch (error) {
        console.error("Get receipt error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipt: ${error.message}`);
    }
};

/**
 * Get receipt by appointment ID
 */
export const getReceiptByAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        
        const receipt = await Receipt.findOne({ 
            appointmentId: parseInt(appointmentId) 
        }).lean();
        
        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found for this appointment");
        }
        
        // Get related data
        const [clientData, petData, staffData] = await Promise.all([
            Receipt.model('Client').findOne({ clientId: receipt.clientId }).lean(),
            Receipt.model('Pet').findOne({ petId: receipt.petId }).lean(),
            Receipt.model('Staff').findOne({ staffId: receipt.issuedBy }).lean()
        ]);
        
        // Add related data to receipt
        receipt.clientData = clientData;
        receipt.petData = petData;
        receipt.staffData = staffData;
        
        return sendResponse(res, 200, true, "Receipt retrieved successfully", receipt);
    } catch (error) {
        console.error("Get receipt by appointment error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipt: ${error.message}`);
    }
};

/**
 * Update receipt status
 */
export const updateReceiptStatus = async (req, res) => {
    try {
        const { receiptId } = req.params;
        const { status } = req.body;
        
        const receipt = await Receipt.findOneAndUpdate(
            { receiptId: parseInt(receiptId) },
            { status },
            { new: true }
        );
        
        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found");
        }
        
        return sendResponse(res, 200, true, "Receipt status updated successfully", receipt);
    } catch (error) {
        console.error("Update receipt status error:", error);
        return sendResponse(res, 500, false, `Failed to update receipt status: ${error.message}`);
    }
};
