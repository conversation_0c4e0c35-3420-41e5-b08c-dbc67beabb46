import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"

interface DatePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName
  label?: string
  description?: string
  placeholder?: string
  form: UseFormReturn<TFieldValues>
  required?: boolean
  disabled?: boolean
  className?: string
  fromDate?: Date
  toDate?: Date
  disabledDates?: Date[] | ((date: Date) => boolean)
  showValidation?: boolean
}

export function DatePicker<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  placeholder = "Select date",
  form,
  required,
  disabled,
  className,
  fromDate,
  toDate,
  disabledDates,
  showValidation = true,
}: DatePickerProps<TFieldValues, TName>) {
  const { formState } = form
  const error = formState.errors[name]
  const isValid = formState.touchedFields[name] && !error

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground",
                    error && "border-destructive ring-destructive",
                    isValid && showValidation && "border-green-500 ring-green-500",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={disabled}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value ? (
                    format(new Date(field.value), "PPP")
                  ) : (
                    <span>{placeholder}</span>
                  )}
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value ? new Date(field.value) : undefined}
                onSelect={field.onChange}
                disabled={
                  disabled ||
                  ((date) => {
                    // Check fromDate constraint
                    if (fromDate && date < fromDate) {
                      return true
                    }
                    
                    // Check toDate constraint
                    if (toDate && date > toDate) {
                      return true
                    }
                    
                    // Check custom disabled dates
                    if (disabledDates) {
                      if (typeof disabledDates === 'function') {
                        return disabledDates(date)
                      } else if (Array.isArray(disabledDates)) {
                        return disabledDates.some(
                          (disabledDate) =>
                            date.getDate() === disabledDate.getDate() &&
                            date.getMonth() === disabledDate.getMonth() &&
                            date.getFullYear() === disabledDate.getFullYear()
                        )
                      }
                    }
                    
                    return false
                  })
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

interface DateRangePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TFromName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TToName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  fromName: TFromName
  toName: TToName
  label?: string
  description?: string
  fromPlaceholder?: string
  toPlaceholder?: string
  form: UseFormReturn<TFieldValues>
  required?: boolean
  disabled?: boolean
  className?: string
  disabledDates?: Date[] | ((date: Date) => boolean)
  showValidation?: boolean
}

export function DateRangePicker<
  TFieldValues extends FieldValues = FieldValues,
  TFromName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TToName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  fromName,
  toName,
  label,
  description,
  fromPlaceholder = "Start date",
  toPlaceholder = "End date",
  form,
  required,
  disabled,
  className,
  disabledDates,
  showValidation = true,
}: DateRangePickerProps<TFieldValues, TFromName, TToName>) {
  const fromValue = form.watch(fromName)
  const toValue = form.watch(toName)
  
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <FormLabel className="flex items-center gap-1">
          {label}
          {required && <span className="text-destructive">*</span>}
        </FormLabel>
      )}
      <div className="flex flex-col sm:flex-row gap-2">
        <DatePicker
          name={fromName}
          placeholder={fromPlaceholder}
          form={form}
          disabled={disabled}
          toDate={toValue ? new Date(toValue) : undefined}
          disabledDates={disabledDates}
          showValidation={showValidation}
          className="flex-1"
        />
        <DatePicker
          name={toName}
          placeholder={toPlaceholder}
          form={form}
          disabled={disabled || !fromValue}
          fromDate={fromValue ? new Date(fromValue) : undefined}
          disabledDates={disabledDates}
          showValidation={showValidation}
          className="flex-1"
        />
      </div>
      {description && <FormDescription>{description}</FormDescription>}
    </div>
  )
}
