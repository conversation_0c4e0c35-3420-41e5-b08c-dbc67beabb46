import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Plus,
  Edit,
  Trash2,
  Archive,
  MessageSquare,
  Clock,
  User,
  Tag,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface AppointmentNote {
  noteId: number;
  appointmentId: number;
  staffId: number;
  noteType: string;
  category: string;
  title: string;
  content: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  isPrivate: boolean;
  tags: string[];
  staffName?: string;
  createdAt: string;
  updatedAt: string;
}

interface AppointmentNotesProps {
  appointmentId: number;
}

const noteCategories = [
  { value: 'consultation', label: 'Consultation' },
  { value: 'laboratory', label: 'Laboratory' },
  { value: 'surgery', label: 'Surgery' },
  { value: 'grooming', label: 'Grooming' },
  { value: 'boarding', label: 'Boarding' },
  { value: 'vaccination', label: 'Vaccination' },
  { value: 'dental', label: 'Dental' },
  { value: 'emergency', label: 'Emergency' },
  { value: 'wellness', label: 'Wellness' },
  { value: 'follow_up', label: 'Follow-up' },
  { value: 'imaging', label: 'Imaging' },
  { value: 'therapy', label: 'Therapy' },
  { value: 'nutrition', label: 'Nutrition' },
  { value: 'behavioral', label: 'Behavioral' },
  { value: 'other', label: 'Other' }
];

const noteTypes = [
  { value: 'general', label: 'General' },
  { value: 'pre_visit', label: 'Pre-visit' },
  { value: 'during_visit', label: 'During Visit' },
  { value: 'post_visit', label: 'Post-visit' },
  { value: 'lab_results', label: 'Lab Results' },
  { value: 'grooming_notes', label: 'Grooming Notes' },
  { value: 'boarding_notes', label: 'Boarding Notes' },
  { value: 'surgery_notes', label: 'Surgery Notes' },
  { value: 'vaccination_notes', label: 'Vaccination Notes' },
  { value: 'dental_notes', label: 'Dental Notes' },
  { value: 'emergency_notes', label: 'Emergency Notes' },
  { value: 'follow_up', label: 'Follow-up' },
  { value: 'medication_notes', label: 'Medication Notes' },
  { value: 'behavioral_notes', label: 'Behavioral Notes' },
  { value: 'nutrition_notes', label: 'Nutrition Notes' },
  { value: 'imaging_notes', label: 'Imaging Notes' },
  { value: 'therapy_notes', label: 'Therapy Notes' }
];

const priorities = [
  { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: 'normal', label: 'Normal', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
];

const AppointmentNotes: React.FC<AppointmentNotesProps> = ({ appointmentId }) => {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    noteType: 'general',
    category: 'consultation',
    priority: 'normal' as const,
    tags: [] as string[],
    isPrivate: false
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch appointment notes
  const { data: notesResponse, isLoading } = useQuery({
    queryKey: ['appointmentNotes', appointmentId, selectedCategory],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedCategory && selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }

      const response = await api.get(`/appointment/${appointmentId}/notes?${params}`);
      return response.data;
    }
  });

  // Create note mutation
  const createNoteMutation = useMutation({
    mutationFn: async (noteData: any) => {
      const response = await api.post(`/appointment/${appointmentId}/notes`, noteData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentNotes', appointmentId] });
      setIsAddingNote(false);
      setNewNote({
        title: '',
        content: '',
        noteType: 'general',
        category: 'consultation',
        priority: 'normal',
        tags: [],
        isPrivate: false
      });
      toast({
        title: "Success",
        description: "Note added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add note",
        variant: "destructive",
      });
    }
  });

  const handleSubmitNote = () => {
    if (!newNote.title.trim() || !newNote.content.trim()) {
      toast({
        title: "Error",
        description: "Title and content are required",
        variant: "destructive",
      });
      return;
    }

    createNoteMutation.mutate(newNote);
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'normal':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    const priorityObj = priorities.find(p => p.value === priority);
    return priorityObj?.color || 'bg-gray-100 text-gray-800';
  };

  const notes = notesResponse?.data?.data || [];

  return (
    <div className="space-y-4">
      {/* Header with filters and add button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Appointment Notes
          </h3>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {noteCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Dialog open={isAddingNote} onOpenChange={setIsAddingNote}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Note
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Appointment Note</DialogTitle>
              <DialogDescription>
                Add a structured note for this appointment. Choose the appropriate category and note type for better organization.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="noteType">Note Type</Label>
                  <Select value={newNote.noteType} onValueChange={(value) => setNewNote({...newNote, noteType: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {noteTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={newNote.category} onValueChange={(value) => setNewNote({...newNote, category: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {noteCategories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newNote.title}
                  onChange={(e) => setNewNote({...newNote, title: e.target.value})}
                  placeholder="Enter note title"
                />
              </div>

              <div>
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  value={newNote.content}
                  onChange={(e) => setNewNote({...newNote, content: e.target.value})}
                  placeholder="Enter note content"
                  rows={6}
                />
              </div>

              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={newNote.priority} onValueChange={(value: any) => setNewNote({...newNote, priority: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAddingNote(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSubmitNote} disabled={createNoteMutation.isPending}>
                  {createNoteMutation.isPending ? 'Adding...' : 'Add Note'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Notes list */}
      {isLoading ? (
        <div className="text-center py-8">Loading notes...</div>
      ) : notes.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No notes found for this appointment</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {notes.map((note: AppointmentNote) => (
            <Card key={note.noteId} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(note.priority)}
                    <h4 className="font-semibold">{note.title}</h4>
                    <Badge className={getPriorityColor(note.priority)}>
                      {note.priority}
                    </Badge>
                    <Badge variant="outline">{note.category}</Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <User className="h-4 w-4" />
                    {note.staffName || 'Unknown Staff'}
                    <Clock className="h-4 w-4 ml-2" />
                    {format(new Date(note.createdAt), 'MMM d, yyyy h:mm a')}
                  </div>
                </div>

                <p className="text-gray-700 mb-3">{note.content}</p>

                {note.tags && note.tags.length > 0 && (
                  <div className="flex items-center gap-2 mb-2">
                    <Tag className="h-4 w-4 text-gray-400" />
                    {note.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AppointmentNotes;
