import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import SearchOwner from "@/components/common/SearchOwner";
import { Client, Pet } from "@/store/types";
import { useQuery } from "@tanstack/react-query";
import { getPets } from "@/services/pets";

interface AddConsultationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const formSchema = z.object({
  petId: z.string().min(1, "Pet is required"),
  ownerId: z.string().min(1, "Owner is required"),
  consultationDate: z.string().min(1, "Date is required"),
  symptoms: z.string().min(1, "Symptoms are required"),
  diagnosis: z.string().min(1, "Diagnosis is required"),
  treatment: z.string().min(1, "Treatment is required"),
  notes: z.string().optional(),
});

const AddConsultationModal = ({ open, onOpenChange }: AddConsultationModalProps) => {
  const [selectedOwner, setSelectedOwner] = useState<Client | null>(null);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      petId: "",
      ownerId: "",
      consultationDate: "",
      symptoms: "",
      diagnosis: "",
      treatment: "",
      notes: "",
    },
  });

  const { data: petsData } = useQuery({
    queryKey: ['pets', selectedOwner?._id],
    queryFn: () => getPets({ owner: selectedOwner?._id }),
    enabled: !!selectedOwner?._id,
  });

  const pets = petsData?.data || [];

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log(data);
    // TODO: Implement API call
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add New Consultation</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="ownerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Owner</FormLabel>
                  <FormControl>
                    <SearchOwner 
                      onSelectOwner={(owner) => {
                        setSelectedOwner(owner);
                        field.onChange(owner._id);
                      }}
                      selectedOwnerId={field.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* More form fields */}
            
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Consultation</Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddConsultationModal;