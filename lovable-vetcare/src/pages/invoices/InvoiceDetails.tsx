import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  ArrowLeft,
  Download,
  CreditCard,
  Receipt,
  Percent,
  DollarSign,
  Calendar,
  User,
  PawPrint,
  Building
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { getInvoiceByAppointment, generateInvoiceFromAppointment } from '@/services/invoices';
import { getAppointmentById } from '@/services/appointments';
import PaymentModal from './components/PaymentModal';
import DiscountModal from './components/DiscountModal';

const InvoiceDetails = () => {
  const { appointmentId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showDiscountModal, setShowDiscountModal] = useState(false);

  // Fetch appointment details
  const { data: appointment } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(appointmentId!),
    enabled: !!appointmentId
  });

  // Fetch or generate invoice
  const { data: invoice, isLoading, error } = useQuery({
    queryKey: ['invoice', 'appointment', appointmentId],
    queryFn: async () => {
      try {
        const response = await getInvoiceByAppointment(parseInt(appointmentId!));
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invoice not found');
      } catch (error: any) {
        if (error.status === 404 || error.message === 'Invoice not found') {
          // Invoice doesn't exist, generate it
          const generateResponse = await generateInvoiceFromAppointment(parseInt(appointmentId!));
          if (generateResponse.success && generateResponse.data) {
            return generateResponse.data;
          }
          throw new Error('Failed to generate invoice');
        }
        throw error;
      }
    },
    enabled: !!appointmentId,
    retry: 1
  });

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'partial':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handlePayment = () => {
    setShowPaymentModal(true);
  };

  const handleDiscount = () => {
    setShowDiscountModal(true);
  };

  const handlePaymentSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['invoice', 'appointment', appointmentId] });
    toast({
      title: "Success",
      description: "Payment processed successfully",
    });
  };

  const handleDiscountSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['invoice', 'appointment', appointmentId] });
    toast({
      title: "Success",
      description: "Discount applied successfully",
    });
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <div className="p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Invoice Not Found</h2>
          <p className="text-gray-600 mb-4">Unable to load invoice details.</p>
          <Button onClick={() => navigate('/appointments')}>
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/appointments/${appointmentId}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Appointment
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Invoice {invoice.invoiceNumber}</h1>
            <p className="text-gray-600">
              Generated on {format(new Date(invoice.invoiceDate), 'PPP')}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge className={getStatusBadgeClass(invoice.paymentStatus)}>
            {invoice.paymentStatus.charAt(0).toUpperCase() + invoice.paymentStatus.slice(1)}
          </Badge>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Invoice Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Info */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Invoice Number:</span>
                  <p className="font-semibold">{invoice.invoiceNumber}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Due Date:</span>
                  <p>{format(new Date(invoice.dueDate), 'PPP')}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Patient:</span>
                  <p className="flex items-center gap-1">
                    <PawPrint className="h-4 w-4" />
                    {appointment?.petName || 'Unknown Pet'}
                  </p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Client:</span>
                  <p className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {appointment?.clientName || 'Unknown Client'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Services */}
          <Card>
            <CardHeader>
              <CardTitle>Services</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {invoice.services?.map((service: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      {service.description && (
                        <p className="text-sm text-gray-600">{service.description}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {invoice.currency} {service.totalPrice.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600">
                        {service.quantity} × {invoice.currency} {service.unitPrice.toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Medications */}
          {invoice.medications?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Medications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {invoice.medications.map((medication: any, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                      <div>
                        <p className="font-medium">{medication.medicationName}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {invoice.currency} {medication.totalPrice.toLocaleString()}
                        </p>
                        <p className="text-sm text-gray-600">
                          {medication.quantity} × {invoice.currency} {medication.unitPrice.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{invoice.currency} {invoice.subtotal.toLocaleString()}</span>
              </div>

              {invoice.afterHoursCharge > 0 && (
                <div className="flex justify-between text-sm">
                  <span>After Hours Charge:</span>
                  <span>{invoice.currency} {invoice.afterHoursCharge.toLocaleString()}</span>
                </div>
              )}

              {invoice.totalDiscounts > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Total Discounts:</span>
                  <span>-{invoice.currency} {invoice.totalDiscounts.toLocaleString()}</span>
                </div>
              )}

              <div className="flex justify-between text-sm">
                <span>Tax ({invoice.taxRate}%):</span>
                <span>{invoice.currency} {invoice.taxAmount.toLocaleString()}</span>
              </div>

              <Separator />

              <div className="flex justify-between font-semibold">
                <span>Total Amount:</span>
                <span>{invoice.currency} {invoice.totalAmount.toLocaleString()}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span>Amount Paid:</span>
                <span className="text-green-600">
                  {invoice.currency} {invoice.amountPaid.toLocaleString()}
                </span>
              </div>

              <div className="flex justify-between font-semibold text-lg">
                <span>Amount Due:</span>
                <span className="text-red-600">
                  {invoice.currency} {invoice.amountDue.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {invoice.paymentStatus !== 'paid' && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={handlePayment}
                  className="w-full"
                  disabled={invoice.amountDue <= 0}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Make Payment
                </Button>

                <Button
                  onClick={handleDiscount}
                  variant="outline"
                  className="w-full"
                >
                  <Percent className="h-4 w-4 mr-2" />
                  Apply Discount
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Discounts Applied */}
          {invoice.discounts?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Discounts Applied</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {invoice.discounts.map((discount: any, index: number) => (
                    <div key={index} className="text-sm">
                      <div className="flex justify-between">
                        <span>{discount.reason || 'Discount'}</span>
                        <span className="text-green-600">
                          -{invoice.currency} {discount.discountAmount.toLocaleString()}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        {discount.discountType === 'percentage'
                          ? `${discount.discountValue}% discount`
                          : `Fixed discount`
                        }
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentModal
          invoice={invoice}
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}

      {/* Discount Modal */}
      {showDiscountModal && (
        <DiscountModal
          invoice={invoice}
          isOpen={showDiscountModal}
          onClose={() => setShowDiscountModal(false)}
          onSuccess={handleDiscountSuccess}
        />
      )}
    </div>
  );
};

export default InvoiceDetails;
