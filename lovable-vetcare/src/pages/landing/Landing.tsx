
import { useEffect, useState } from "react";
import Header from "./components/Header";
import HeroSection from "./components/HeroSection";
import ServicesSection from "./components/ServicesSection";
import WhyChooseUsSection from "./components/WhyChooseUsSection";
import StatsSection from "./components/StatsSection";
import ContactSection from "./components/ContactSection";
import Footer from "./components/Footer";
import TestimonialsSection from "./components/TestimonialsSection";

const Landing = () => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const [staticTitle, setStaticTitle] = useState("");
  const [showTitle, setShowTitle] = useState(false);
  const [currentBgIndex, setCurrentBgIndex] = useState(0);

  const messages = [
    "Veterinary Excellence",
    "Complete Pet Care",
    "Practice Management"
  ];

  const backgroundImages = [
    "/pets/cat.jpg",
    "/pets/dog.jpg",
    "/pets/parrot.jpg",
    "/pets/horse.jpg",
    "/pets/pig.jpg",
    "/pets/goat.jpg"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBgIndex((prev) => (prev + 1) % backgroundImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const typeTitle = async () => {
      const title = "VetCare Platform";
      let currentText = "";

      for (let i = 0; i <= title.length; i++) {
        currentText = title.slice(0, i);
        setStaticTitle(currentText);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      setShowTitle(true);
      setCurrentMessageIndex(0);
    };

    typeTitle();
  }, []);

  useEffect(() => {
    if (!showTitle) return;

    const typeMessage = async () => {
      const message = messages[currentMessageIndex];
      let currentText = "";

      for (let i = 0; i <= message.length; i++) {
        currentText = message.slice(0, i);
        setDisplayedText(currentText);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      await new Promise(resolve => setTimeout(resolve, 2000));
      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
    };

    typeMessage();
  }, [currentMessageIndex, showTitle]);

  useEffect(() => {
    const handleScroll = () => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => {
        const rect = el.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight - 100;
        if (isVisible) {
          el.classList.add('animate-fade-in');
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <HeroSection
        backgroundImages={backgroundImages}
        currentBgIndex={currentBgIndex}
        staticTitle={staticTitle}
      />
      <ServicesSection />
      <TestimonialsSection />
      <WhyChooseUsSection />
      <StatsSection />
      <ContactSection />
      <Footer />
    </div>
  );
};

export default Landing;
