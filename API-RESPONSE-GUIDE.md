# API Response Standardization Guide

This guide outlines the standard structure for API responses in the Veterinary SaaS system to ensure consistency between backend and frontend.

## Table of Contents
1. [Response Structure](#response-structure)
2. [Pagination Structure](#pagination-structure)
3. [Common Response Types](#common-response-types)
4. [Entity-Specific Response Formats](#entity-specific-response-formats)
5. [Error Handling](#error-handling)
6. [Implementation Guidelines](#implementation-guidelines)

## Response Structure

All API responses should follow this standard structure:

```typescript
// Success response with data
{
  success: true,
  status: 200, // HTTP status code
  message: "Operation successful",
  data: { ... } // Response data
}

// Success response with paginated data
{
  success: true,
  status: 200,
  message: "Operation successful",
  data: {
    data: [ ... ], // Array of items
    pagination: { ... } // Pagination metadata
  }
}

// Error response
{
  success: false,
  status: 400, // HTTP status code
  message: "Error message"
}
```

## Pagination Structure

For endpoints that return paginated data:

```typescript
{
  success: true,
  status: 200,
  message: "Items retrieved successfully",
  data: {
    data: [ ... ], // Array of items
    pagination: {
      totalCount: 100, // Total number of items
      page: 1, // Current page
      limit: 10, // Items per page
      offset: 0, // Offset from start
      totalPages: 10 // Total number of pages
    }
  }
}
```

## Common Response Types

### Standard Response (Single Item)

```typescript
interface StandardResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data: T;
}
```

### Paginated Response (Multiple Items)

```typescript
interface PaginatedResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: T[];
    pagination: PaginationData;
  };
}

interface PaginationData {
  totalCount: number;
  page: number;
  limit: number;
  offset: number;
  totalPages: number;
}
```

### Message-Only Response

```typescript
interface MessageResponse {
  success: boolean;
  status: number;
  message: string;
}
```

## Entity-Specific Response Formats

### Staff Response

Backend should return staff data with populated references:

```typescript
// Staff object with populated references
{
  _id: "staff123",
  userId: {
    _id: "user123",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneNumber: "1234567890"
  },
  clinicId: {
    _id: "clinic123",
    clinicName: "Main Clinic",
    address: "123 Main St"
  },
  roleId: {
    _id: "role123",
    roleId: 1,
    name: "Veterinarian",
    permissions: [1, 2, 3]
  },
  jobTitle: "Senior Veterinarian",
  // Other staff properties...
}
```

Frontend should handle both populated and unpopulated references:

```typescript
// Staff type definition
interface Staff extends BaseEntity {
  // User information (populated)
  userId?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
  } | string;
  
  // Clinic information (populated)
  clinicId?: {
    _id: string;
    clinicName: string;
    address?: string;
  } | string;
  
  // Other properties...
}
```

### Pet Response

Backend should return pet data with populated references:

```typescript
// Pet object with populated references
{
  _id: "pet123",
  name: "Buddy",
  species: {
    _id: "species123",
    name: "Dog"
  },
  breed: {
    _id: "breed123",
    breedName: "Labrador"
  },
  owner: {
    _id: "client123",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>"
  },
  // Other pet properties...
}
```

## Error Handling

All error responses should follow this format:

```typescript
{
  success: false,
  status: 400, // Appropriate HTTP status code
  message: "Descriptive error message"
}
```

Common HTTP status codes:
- 400: Bad Request (client error)
- 401: Unauthorized (authentication required)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (resource doesn't exist)
- 500: Internal Server Error (server error)

## Implementation Guidelines

### Backend Implementation

1. Use the `sendResponse` utility for all controller responses:

```javascript
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

// For single item responses
return sendResponse(res, 200, true, "Item retrieved successfully", item);

// For paginated responses
const paginatedResults = await paginateResults(Model, query, options);
return sendResponse(res, 200, true, "Items retrieved successfully", paginatedResults);

// For error responses
return sendResponse(res, 404, false, "Item not found");
```

2. Always populate relevant references:

```javascript
// Example: Populate references in staff
const staffMember = await Staff.findById(staffId)
  .populate('userId', 'firstName lastName email phoneNumber')
  .populate('clinicId', 'clinicName address')
  .populate('roleId', 'name permissions')
  .select('-password -__v');
```

### Frontend Implementation

1. Define proper TypeScript interfaces for all API responses:

```typescript
// Example: Staff response interfaces
export interface StaffResponse {
  success: boolean;
  status: number;
  message: string;
  data: Staff | null;
}

export interface StaffsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Staff[];
    pagination: PaginationData;
  };
}
```

2. Handle both populated and unpopulated references:

```typescript
// Example: Safely accessing nested properties
const staffName = staff.userId 
  ? (typeof staff.userId === 'string' 
    ? staff.name || `Staff ID: ${staff._id}` 
    : `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`)
  : staff.name || `Staff ID: ${staff._id}`;
```

3. Provide fallback values for potentially missing data:

```typescript
// Example: Safe data access with fallbacks
const pets = petsData?.data?.data || [];
const pagination = petsData?.data?.pagination || { totalCount: 0 };
```

By following these guidelines, we ensure consistent data handling between the backend and frontend, making the application more robust and easier to maintain.
