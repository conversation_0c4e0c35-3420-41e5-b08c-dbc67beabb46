import { motion } from 'framer-motion';

interface TriangleDecorationProps {
  size?: number;
  color?: string;
  top?: string;
  left?: string;
  right?: string;
  bottom?: string;
  rotate?: number;
  delay?: number;
  className?: string;
}

const TriangleDecoration = ({
  size = 30,
  color = 'bg-primary',
  top,
  left,
  right,
  bottom,
  rotate = 0,
  delay = 0,
  className = '',
}: TriangleDecorationProps) => {
  const variants = {
    hidden: { 
      opacity: 0, 
      scale: 0,
      rotate: rotate - 90
    },
    visible: { 
      opacity: 0.7, 
      scale: 1,
      rotate,
      transition: {
        duration: 0.8,
        delay,
        ease: [0.22, 1, 0.36, 1]
      }
    },
    hover: {
      rotate: rotate + 15,
      scale: 1.1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const style: React.CSSProperties = {
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderWidth: `0 ${size/2}px ${size}px ${size/2}px`,
    borderColor: `transparent transparent ${color.startsWith('bg-') ? 'var(--primary)' : color} transparent`,
    position: 'absolute',
    top,
    left,
    right,
    bottom,
    zIndex: 0,
    filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15))',
  };

  return (
    <motion.div
      style={style}
      className={className}
      variants={variants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
    />
  );
};

export default TriangleDecoration;
