/**
 * Custom Error Classes
 * 
 * This file defines custom error classes for standardized error handling across the application.
 * Each error class includes a status code and optional data for detailed error responses.
 */

// Base API Error class
export class APIError extends Error {
    constructor(message, statusCode = 500, data = null) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.data = data;
        Error.captureStackTrace(this, this.constructor);
    }
}

// 400 Bad Request - Invalid input or parameters
export class BadRequestError extends APIError {
    constructor(message = 'Bad request', data = null) {
        super(message, 400, data);
    }
}

// 401 Unauthorized - Authentication required
export class UnauthorizedError extends APIError {
    constructor(message = 'Authentication required', data = null) {
        super(message, 401, data);
    }
}

// 403 Forbidden - Authenticated but not authorized
export class ForbiddenError extends APIError {
    constructor(message = 'Access denied', data = null) {
        super(message, 403, data);
    }
}

// 404 Not Found - Resource not found
export class NotFoundError extends APIError {
    constructor(message = 'Resource not found', data = null) {
        super(message, 404, data);
    }
}

// 409 Conflict - Resource conflict (e.g., duplicate entry)
export class ConflictError extends APIError {
    constructor(message = 'Resource conflict', data = null) {
        super(message, 409, data);
    }
}

// 422 Unprocessable Entity - Validation error
export class ValidationError extends APIError {
    constructor(message = 'Validation failed', data = null) {
        super(message, 422, data);
    }
}

// 429 Too Many Requests - Rate limit exceeded
export class RateLimitError extends APIError {
    constructor(message = 'Rate limit exceeded', data = null) {
        super(message, 429, data);
    }
}

// 500 Internal Server Error - Unexpected server error
export class InternalServerError extends APIError {
    constructor(message = 'Internal server error', data = null) {
        super(message, 500, data);
    }
}

// 503 Service Unavailable - Service temporarily unavailable
export class ServiceUnavailableError extends APIError {
    constructor(message = 'Service unavailable', data = null) {
        super(message, 503, data);
    }
}

// Helper function to convert any error to an APIError
export const toAPIError = (error) => {
    if (error instanceof APIError) {
        return error;
    }
    
    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
        return new ValidationError(
            'Validation failed',
            Object.values(error.errors).map(err => err.message)
        );
    }
    
    // Handle Mongoose CastError (invalid ObjectId)
    if (error.name === 'CastError') {
        return new BadRequestError(`Invalid ${error.path}: ${error.value}`);
    }
    
    // Handle Mongoose duplicate key error
    if (error.code === 11000) {
        const field = Object.keys(error.keyValue)[0];
        return new ConflictError(`Duplicate value for ${field}: ${error.keyValue[field]}`);
    }
    
    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
        return new UnauthorizedError('Invalid token');
    }
    
    if (error.name === 'TokenExpiredError') {
        return new UnauthorizedError('Token expired');
    }
    
    // Default to internal server error
    console.error('Unhandled error:', error);
    return new InternalServerError(error.message || 'Something went wrong');
};
