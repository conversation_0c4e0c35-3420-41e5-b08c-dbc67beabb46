import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from '../models/user.model.js';
import Role from '../models/role.model.js';
import Permission from '../models/permission.model.js';
import { config } from 'dotenv';
import { DB_URI } from '../config/env.js';

// Load environment variables
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

const initializeDatabase = async () => {
    try {
        console.log('Connecting to database...');
        await mongoose.connect(DB_URI);
        console.log('Connected to database');

        // Wait a bit for collections to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Initialize permissions first
        console.log('\n=== Initializing Permissions ===');
        await Permission.initializePermissions();

        // Initialize roles
        console.log('\n=== Initializing Roles ===');
        await Role.initializeRoles();

        // Wait a bit more for auto-increment to be ready
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check if admin already exists
        const existingAdmin = await User.findOne({ email: '<EMAIL>' });

        if (existingAdmin) {
            console.log('\n=== Default admin already exists ===');
            console.log(`Email: ${existingAdmin.email}`);
            console.log(`User ID: ${existingAdmin.userId}`);
            await mongoose.disconnect();
            return;
        }

        // Get the super_admin role
        const superAdminRole = await Role.findOne({ roleName: 'super_admin' });
        if (!superAdminRole) {
            throw new Error('Super admin role not found');
        }

        // Create default admin
        console.log('\n=== Creating Default Admin ===');
        const hashedPassword = await bcrypt.hash('pass123', 10);

        const adminUser = await User.create({
            email: '<EMAIL>',
            password: hashedPassword,
            firstName: 'Admin',
            middleName: '',
            lastName: 'User',
            phoneNumber: '1234567890',
            address: '123 Admin Street',
            dob: new Date('1990-01-01'),
            roleId: superAdminRole.roleId,
            permissions: superAdminRole.permissions,
            status: 1
        });

        console.log('Default admin created successfully:');
        console.log(`Email: ${adminUser.email}`);
        console.log(`Password: pass123`);
        console.log(`User ID: ${adminUser.userId}`);
        console.log(`Role ID: ${adminUser.roleId}`);

        await mongoose.disconnect();
        console.log('\n=== Database initialization complete ===');
    } catch (error) {
        console.error('Error initializing database:', error);
        process.exit(1);
    }
};

initializeDatabase();
