import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { getClinic, updateClinic, getClinicStaff } from "@/services/clinics";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import LoadingPage from "@/components/common/LoadingPage";
import { Tabs, TabsContent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Edit, Trash2, Crown, Search, Users, Building2, Check, RefreshCw, Settings } from "lucide-react";
import AddStaffToClinicModal from "./components/AddStaffToClinicModal";
import { getClinicOwners, getStaff } from "@/services/staff";
import { motion } from "framer-motion";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { useAuth } from "@/store";

const formSchema = z.object({
  clinicName: z.string().min(1, "Name is required"),
  address: z.string().min(1, "Address is required"),
  phoneNumber: z.string().min(1, "Phone is required"),
  email: z.string().email("Invalid email format")
});

type FormValues = z.infer<typeof formSchema>;

const EditClinic = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user, staff } = useAuth();
  const [activeTab, setActiveTab] = useState("details");
  const [ownerSearchQuery, setOwnerSearchQuery] = useState("");
  const [selectedOwner, setSelectedOwner] = useState<any>(null);
  const [selectedManager, setSelectedManager] = useState<any>(null);

  // Check if user is super admin or clinic owner
  const isAdmin = user?.role === 'super_admin';
  const isClinicOwner = staff?.isClinicOwner;

  const { data: clinicResponse, isLoading, error } = useQuery({
    queryKey: ["clinic", id],
    queryFn: () => getClinic(id!),
    enabled: !!id,
  });

  // Extract clinic data from response
  const clinic = clinicResponse?.data;

  // Fetch clinic staff
  const { data: staffData, isLoading: isStaffLoading } = useQuery({
    queryKey: ["clinic", id, "staff"],
    queryFn: () => getClinicStaff(id!),
    enabled: !!id && activeTab === "staff",
  });

  // Fetch clinic owners for assignment (admin only)
  const { data: clinicOwnersData } = useQuery({
    queryKey: ["clinic-owners", ownerSearchQuery],
    queryFn: async () => {
      const response = await getClinicOwners({
        search: ownerSearchQuery,
        // Remove hasClinic filter to get ALL clinic owners
        limit: 50
      });
      return response.data?.data || [];
    },
    enabled: activeTab === "owner" && isAdmin,
  });

  // Fetch staff for clinic manager assignment (clinic owner only)
  const { data: staffForManagerData } = useQuery({
    queryKey: ["staff-for-manager", id],
    queryFn: async () => {
      const response = await getStaff({
        clinicId: id,
        limit: 100,
        status: 1 // Only active staff
      });
      return response.data?.data || [];
    },
    enabled: activeTab === "manager" && isClinicOwner && !!id,
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      clinicName: "",
      address: "",
      phoneNumber: "",
      email: ""
    },
    values: clinic ? {
      clinicName: clinic.clinicName || "",
      address: clinic.address || "",
      phoneNumber: clinic.phoneNumber || "",
      email: clinic.email || ""
    } : undefined
  });

  // Assign clinic owner mutation (admin only)
  const assignOwnerMutation = useMutation({
    mutationFn: async ({ clinicId, ownerId }: { clinicId: number; ownerId: number }) => {
      return await api.put(`/clinics/${clinicId}/assign-owner`, { ownerId });
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Clinic owner assigned successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["clinic", id] });
      queryClient.invalidateQueries({ queryKey: ["clinics"] });
      setSelectedOwner(null);
      setOwnerSearchQuery("");
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to assign clinic owner",
      });
    },
  });

  // Assign clinic manager mutation (clinic owner only)
  const assignManagerMutation = useMutation({
    mutationFn: async ({ clinicId, managerId }: { clinicId: number; managerId: number }) => {
      return await api.put(`/clinics/${clinicId}/assign-manager`, { managerId });
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Clinic manager assigned successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["clinic", id] });
      queryClient.invalidateQueries({ queryKey: ["staff-for-manager", id] });
      setSelectedManager(null);
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to assign clinic manager",
      });
    },
  });

  const handleAssignOwner = () => {
    if (!clinic || !selectedOwner) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select an owner",
      });
      return;
    }

    assignOwnerMutation.mutate({
      clinicId: clinic.clinicId,
      ownerId: selectedOwner.staffId
    });
  };

  const handleAssignManager = () => {
    if (!clinic || !selectedManager) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a manager",
      });
      return;
    }

    assignManagerMutation.mutate({
      clinicId: clinic.clinicId,
      managerId: selectedManager.staffId
    });
  };

  const onSubmit = async (values: FormValues) => {
    if (!id) return;

    try {
      const clinicData = {
        clinicName: values.clinicName,
        address: values.address,
        phoneNumber: values.phoneNumber,
        email: values.email
      };

      await updateClinic(id, clinicData);
      await queryClient.invalidateQueries({ queryKey: ["clinics"] });
      await queryClient.invalidateQueries({ queryKey: ["clinic", id] });

      toast({
        title: "Success",
        description: "Clinic updated successfully",
      });

      navigate("/admin/clinics");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update clinic",
        variant: "destructive",
      });
    }
  };

  // Handle error state with useEffect to avoid calling toast during render
  useEffect(() => {
    if (error || (!isLoading && clinicResponse && !clinicResponse.success)) {
      toast({
        title: "Error",
        description: clinicResponse?.message || "Failed to load clinic information",
        variant: "destructive",
      });
    }
  }, [error, clinicResponse, isLoading, toast]);

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error || !clinic || (clinicResponse && !clinicResponse.success)) {
    return null;
  }

  // Get staff list from response
  const clinicStaff = staffData?.data?.data || [];

  return (
    <div className="p-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Manage Clinic: {clinic?.clinicName}</CardTitle>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => navigate("/admin/clinics")}>
              Back to Clinics
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="details">Clinic Details</TabsTrigger>
              {isAdmin && <TabsTrigger value="owner">Clinic Owner</TabsTrigger>}
              {isClinicOwner && <TabsTrigger value="manager">Clinic Manager</TabsTrigger>}
              <TabsTrigger value="staff">Staff Management</TabsTrigger>
            </TabsList>

            <TabsContent value="details">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="clinicName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter clinic name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => navigate("/admin/clinics")}>
                      Cancel
                    </Button>
                    <Button type="submit">Update Clinic</Button>
                  </div>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="owner">
              <div className="space-y-6">
                {/* Current Owner Display */}
                {clinic?.ownerInfo ? (
                  <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                    <CardHeader>
                      <CardTitle className="flex items-center text-green-900">
                        <Crown className="mr-2 h-5 w-5" />
                        Current Clinic Owner
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-green-900">
                            {clinic.ownerInfo.firstName} {clinic.ownerInfo.lastName}
                          </h3>
                          <p className="text-green-700">{clinic.ownerInfo.email}</p>
                          <p className="text-green-600 text-sm">{clinic.ownerInfo.jobTitle}</p>
                        </div>
                        <Badge variant="default" className="bg-green-100 text-green-800 border-green-300">
                          <Crown className="mr-1 h-3 w-3" />
                          Owner
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                    <CardHeader>
                      <CardTitle className="flex items-center text-yellow-900">
                        <Building2 className="mr-2 h-5 w-5" />
                        No Owner Assigned
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-yellow-800">
                        This clinic doesn't have an assigned owner. Use the search below to find and assign a clinic owner.
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Instructions */}
                <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                  <CardHeader>
                    <CardTitle className="flex items-center text-blue-900">
                      <Search className="mr-2 h-5 w-5" />
                      Find & Assign Clinic Owner
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-blue-800">
                        Search through all clinic owners. The current owner (if any) will be highlighted in blue.
                        You can assign a new owner or change the existing one.
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-blue-700">
                        <div className="flex items-center">
                          <Crown className="w-3 h-3 text-blue-600 mr-2" />
                          Current owner
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                          Available for assignment
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                          Has another clinic
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Search and Assign Owner */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Search className="mr-2 h-5 w-5" />
                      {clinic?.ownerInfo ? 'Change Clinic Owner' : 'Assign Clinic Owner'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="owner-search" className="text-base font-medium">
                        Search by Name or Phone Number
                      </Label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        <Input
                          id="owner-search"
                          placeholder="Type name or phone number to search clinic owners..."
                          value={ownerSearchQuery}
                          onChange={(e) => setOwnerSearchQuery(e.target.value)}
                          className="pl-12 h-12 text-base border-2 focus:border-primary"
                        />
                      </div>
                      {ownerSearchQuery && (
                        <p className="text-sm text-gray-600">
                          Searching for: <span className="font-medium">"{ownerSearchQuery}"</span>
                        </p>
                      )}
                    </div>

                    {/* Results */}
                    <div className="space-y-4">
                      {clinicOwnersData?.length === 0 ? (
                        <div className="text-center py-12">
                          <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            {ownerSearchQuery ? 'No clinic owners found' : 'No available clinic owners'}
                          </h3>
                          <p className="text-gray-500">
                            {ownerSearchQuery
                              ? `No clinic owners match "${ownerSearchQuery}". Try a different search term.`
                              : 'All clinic owners are already assigned to clinics.'
                            }
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                          {clinicOwnersData?.map((owner: any) => {
                            // Check if this owner is the current clinic's owner
                            const isCurrentOwner = clinic?.owner === owner.staffId ||
                                                 clinic?.ownerId === owner.staffId ||
                                                 (clinic?.ownerInfo && clinic.ownerInfo.staffId === owner.staffId);

                            // Check if owner has a clinic assigned
                            const hasClinic = owner.clinic || owner.clinicId !== 9999;

                            // Determine availability
                            const isAvailable = !hasClinic || isCurrentOwner;

                            return (
                              <motion.div
                                key={owner.staffId}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.2 }}
                                className={`p-5 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                                  isCurrentOwner
                                    ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
                                    : selectedOwner?.staffId === owner.staffId
                                    ? 'border-primary bg-primary/5 shadow-lg'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                                onClick={() => setSelectedOwner(owner)}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                      <h4 className="font-semibold text-lg text-gray-900">
                                        {owner.fullName || `${owner.firstName} ${owner.lastName}`}
                                      </h4>
                                      {isCurrentOwner && (
                                        <Crown className="h-5 w-5 text-blue-600 ml-2" />
                                      )}
                                      {selectedOwner?.staffId === owner.staffId && !isCurrentOwner && (
                                        <Check className="h-5 w-5 text-primary ml-2" />
                                      )}
                                    </div>

                                    <div className="space-y-1 mb-3">
                                      <p className="text-sm text-gray-600 flex items-center">
                                        <span className="w-16 text-gray-500">Email:</span>
                                        {owner.email}
                                      </p>
                                      {owner.phoneNumber && (
                                        <p className="text-sm text-gray-600 flex items-center">
                                          <span className="w-16 text-gray-500">Phone:</span>
                                          {owner.phoneNumber}
                                        </p>
                                      )}
                                      <p className="text-sm text-gray-600 flex items-center">
                                        <span className="w-16 text-gray-500">Title:</span>
                                        {owner.jobTitle}
                                      </p>
                                      {owner.clinic && (
                                        <p className="text-sm text-gray-600 flex items-center">
                                          <span className="w-16 text-gray-500">Clinic:</span>
                                          {owner.clinic.clinicName}
                                        </p>
                                      )}
                                    </div>

                                    <div className="flex flex-wrap gap-2">
                                      {isCurrentOwner ? (
                                        <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 border-blue-300">
                                          <Crown className="mr-1 h-3 w-3" />
                                          Current Owner
                                        </Badge>
                                      ) : isAvailable ? (
                                        <Badge variant="default" className="text-xs bg-green-100 text-green-800 border-green-300">
                                          ✓ Available for Assignment
                                        </Badge>
                                      ) : (
                                        <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800 border-yellow-300">
                                          ⚠ Has Another Clinic
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>
                      )}
                    </div>

                    {/* Assignment Action */}
                    {selectedOwner && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                          <CardHeader>
                            <CardTitle className="flex items-center text-blue-900">
                              <Crown className="mr-2 h-5 w-5" />
                              Ready to Assign
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              <div className="bg-white p-4 rounded-lg border border-blue-200">
                                <h4 className="font-semibold text-blue-900 mb-2">Assignment Summary</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Clinic:</span>
                                    <span className="font-medium">{clinic.clinicName}</span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">New Owner:</span>
                                    <span className="font-medium">
                                      {selectedOwner.fullName || `${selectedOwner.firstName} ${selectedOwner.lastName}`}
                                    </span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Owner Email:</span>
                                    <span className="font-medium">{selectedOwner.email}</span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Title:</span>
                                    <span className="font-medium">{selectedOwner.jobTitle}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="flex justify-end space-x-3">
                                <Button
                                  variant="outline"
                                  onClick={() => setSelectedOwner(null)}
                                  disabled={assignOwnerMutation.isPending}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={handleAssignOwner}
                                  disabled={assignOwnerMutation.isPending}
                                  className="bg-blue-600 hover:bg-blue-700 text-white px-6"
                                  size="lg"
                                >
                                  {assignOwnerMutation.isPending ? (
                                    <>
                                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                      Assigning...
                                    </>
                                  ) : (
                                    <>
                                      <Crown className="mr-2 h-4 w-4" />
                                      {clinic?.ownerInfo ? 'Change Owner' : 'Assign as Owner'}
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="manager">
              <div className="space-y-6">
                {/* Current Manager Display */}
                {clinic?.managerInfo ? (
                  <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <CardHeader>
                      <CardTitle className="flex items-center text-blue-900">
                        <Settings className="mr-2 h-5 w-5" />
                        Current Clinic Manager
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-blue-900">
                            {clinic.managerInfo.firstName} {clinic.managerInfo.lastName}
                          </h3>
                          <p className="text-blue-700">{clinic.managerInfo.email}</p>
                          <p className="text-blue-600 text-sm">{clinic.managerInfo.jobTitle}</p>
                        </div>
                        <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-300">
                          <Settings className="mr-1 h-3 w-3" />
                          Manager
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                    <CardHeader>
                      <CardTitle className="flex items-center text-yellow-900">
                        <Settings className="mr-2 h-5 w-5" />
                        No Manager Assigned
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-yellow-800">
                        This clinic doesn't have an assigned manager. Select a staff member below to assign as clinic manager.
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Staff Selection for Manager */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Settings className="mr-2 h-5 w-5" />
                      {clinic?.managerInfo ? 'Change Clinic Manager' : 'Assign Clinic Manager'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      {staffForManagerData?.length === 0 ? (
                        <div className="text-center py-12">
                          <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            No staff members found
                          </h3>
                          <p className="text-gray-500">
                            Add staff members to this clinic first before assigning a manager.
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                          {staffForManagerData?.map((staffMember: any) => {
                            const isCurrentManager = clinic?.managerId === staffMember.staffId;
                            const isSelected = selectedManager?.staffId === staffMember.staffId;

                            return (
                              <motion.div
                                key={staffMember.staffId}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.2 }}
                                className={`p-5 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                                  isCurrentManager
                                    ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
                                    : isSelected
                                    ? 'border-primary bg-primary/5 shadow-lg'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                                onClick={() => setSelectedManager(staffMember)}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                      <h4 className="font-semibold text-lg text-gray-900">
                                        {staffMember.firstName} {staffMember.lastName}
                                      </h4>
                                      {isCurrentManager && (
                                        <Settings className="h-5 w-5 text-blue-600 ml-2" />
                                      )}
                                      {isSelected && !isCurrentManager && (
                                        <Check className="h-5 w-5 text-primary ml-2" />
                                      )}
                                    </div>

                                    <div className="space-y-1 mb-3">
                                      <p className="text-sm text-gray-600 flex items-center">
                                        <span className="w-16 text-gray-500">Email:</span>
                                        {staffMember.email}
                                      </p>
                                      {staffMember.phoneNumber && (
                                        <p className="text-sm text-gray-600 flex items-center">
                                          <span className="w-16 text-gray-500">Phone:</span>
                                          {staffMember.phoneNumber}
                                        </p>
                                      )}
                                      <p className="text-sm text-gray-600 flex items-center">
                                        <span className="w-16 text-gray-500">Title:</span>
                                        {staffMember.jobTitle}
                                      </p>
                                    </div>

                                    <div className="flex flex-wrap gap-2">
                                      {isCurrentManager ? (
                                        <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 border-blue-300">
                                          <Settings className="mr-1 h-3 w-3" />
                                          Current Manager
                                        </Badge>
                                      ) : (
                                        <Badge variant="default" className="text-xs bg-green-100 text-green-800 border-green-300">
                                          ✓ Available for Assignment
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>
                      )}
                    </div>

                    {/* Assignment Action */}
                    {selectedManager && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                          <CardHeader>
                            <CardTitle className="flex items-center text-blue-900">
                              <Settings className="mr-2 h-5 w-5" />
                              Ready to Assign Manager
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              <div className="bg-white p-4 rounded-lg border border-blue-200">
                                <h4 className="font-semibold text-blue-900 mb-2">Assignment Summary</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Clinic:</span>
                                    <span className="font-medium">{clinic?.clinicName}</span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">New Manager:</span>
                                    <span className="font-medium">
                                      {selectedManager.firstName} {selectedManager.lastName}
                                    </span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Manager Email:</span>
                                    <span className="font-medium">{selectedManager.email}</span>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Title:</span>
                                    <span className="font-medium">{selectedManager.jobTitle}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="flex justify-end space-x-3">
                                <Button
                                  variant="outline"
                                  onClick={() => setSelectedManager(null)}
                                  disabled={assignManagerMutation.isPending}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={handleAssignManager}
                                  disabled={assignManagerMutation.isPending}
                                  className="bg-blue-600 hover:bg-blue-700 text-white px-6"
                                  size="lg"
                                >
                                  {assignManagerMutation.isPending ? (
                                    <>
                                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                      Assigning...
                                    </>
                                  ) : (
                                    <>
                                      <Settings className="mr-2 h-4 w-4" />
                                      {clinic?.managerInfo ? 'Change Manager' : 'Assign as Manager'}
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="staff">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Clinic Staff</h3>
                <AddStaffToClinicModal
                  clinicId={id || ""}
                  clinicName={clinic?.clinicName || ""}
                  trigger={
                    <Button>
                      <UserPlus className="h-4 w-4 mr-2" /> Add Staff
                    </Button>
                  }
                  onSuccess={() => {
                    queryClient.invalidateQueries({ queryKey: ["clinic", id, "staff"] });
                    toast({
                      title: "Success",
                      description: "Staff added to clinic successfully",
                    });
                  }}
                />
              </div>

              {isStaffLoading ? (
                <div className="flex justify-center py-8">
                  <p>Loading staff data...</p>
                </div>
              ) : clinicStaff.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <p className="text-muted-foreground mb-4">No staff members found for this clinic</p>
                  <AddStaffToClinicModal
                    clinicId={id || ""}
                    clinicName={clinic?.clinicName || ""}
                    trigger={
                      <Button>
                        <UserPlus className="h-4 w-4 mr-2" /> Add Staff
                      </Button>
                    }
                    onSuccess={() => {
                      queryClient.invalidateQueries({ queryKey: ["clinic", id, "staff"] });
                      toast({
                        title: "Success",
                        description: "Staff added to clinic successfully",
                      });
                    }}
                  />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicStaff.map((staff) => {
                      // Ensure we have a valid key
                      const staffKey = staff._id || staff.staffId || Math.random().toString();

                      // Get staff name from either userId or direct properties
                      let staffName = 'Unknown Staff';
                      try {
                        if (staff.userId && typeof staff.userId === 'object') {
                          staffName = `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
                        } else if (staff.firstName || staff.lastName) {
                          staffName = `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
                        } else if (staff.name) {
                          staffName = String(staff.name);
                        }
                        if (!staffName || staffName === '') {
                          staffName = `Staff ID: ${staff._id || staff.staffId || 'Unknown'}`;
                        }
                      } catch (e) {
                        staffName = `Staff ID: ${staff._id || staff.staffId || 'Unknown'}`;
                      }

                      // Get staff email
                      let staffEmail = 'N/A';
                      try {
                        if (staff.userId && typeof staff.userId === 'object' && staff.userId.email) {
                          staffEmail = String(staff.userId.email);
                        } else if (staff.email) {
                          staffEmail = String(staff.email);
                        }
                      } catch (e) {
                        staffEmail = 'N/A';
                      }

                      // Get role name - handle different role object structures
                      let roleName = 'N/A';
                      try {
                        if (staff.roleId && typeof staff.roleId === 'object') {
                          // Handle populated role object
                          roleName = String(staff.roleId.name || staff.roleId.roleName || 'N/A');
                        } else if (staff.role) {
                          // Handle direct role property
                          if (typeof staff.role === 'string') {
                            roleName = staff.role;
                          } else if (typeof staff.role === 'object') {
                            roleName = String(staff.role.name || staff.role.roleName || 'N/A');
                          }
                        }
                      } catch (e) {
                        roleName = 'N/A';
                      }

                      return (
                        <TableRow key={staffKey}>
                          <TableCell className="font-medium">{staffName}</TableCell>
                          <TableCell>{staffEmail}</TableCell>
                          <TableCell>{roleName}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${staff.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {staff.status === 1 ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>


    </div>
  );
};

export default EditClinic;
