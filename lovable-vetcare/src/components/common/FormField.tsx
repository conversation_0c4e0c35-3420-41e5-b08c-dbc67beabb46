import React from 'react';
import { 
  Form<PERSON>ield as ShadcnFormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from "@/components/ui/form";
import { Input, InputProps } from "@/components/ui/input";
import { Textarea, TextareaProps } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectItemProps
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { FieldPath, FieldValues, UseFormReturn } from 'react-hook-form';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

// Define option type for select fields
export interface SelectOption {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

// Base props for all field types
interface BaseFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName;
  label?: string;
  description?: string;
  form: UseFormReturn<TFieldValues>;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showValidation?: boolean;
}

// Input field props
interface InputFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFieldProps<TFieldValues, TName>, Omit<InputProps, 'name'> {}

// Textarea field props
interface TextareaFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFieldProps<TFieldValues, TName>, Omit<TextareaProps, 'name'> {}

// Select field props
interface SelectFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFieldProps<TFieldValues, TName> {
  options: SelectOption[];
  placeholder?: string;
  searchable?: boolean;
  icon?: React.ReactNode;
}

// Checkbox field props
interface CheckboxFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFieldProps<TFieldValues, TName> {
  checkboxLabel?: string;
}

// Switch field props
interface SwitchFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFieldProps<TFieldValues, TName> {
  switchLabel?: string;
}

// Input field component
export function InputField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  form,
  required,
  disabled,
  className,
  showValidation = true,
  ...props
}: InputFieldProps<TFieldValues, TName>) {
  const { formState } = form;
  const error = formState.errors[name];
  const isValid = formState.touchedFields[name] && !error;
  
  return (
    <ShadcnFormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div className="relative">
              <Input
                {...field}
                {...props}
                disabled={disabled}
                state={error ? "error" : isValid && showValidation ? "success" : "default"}
              />
              {showValidation && (
                <>
                  {error && (
                    <div className="absolute right-3 top-1/2 -translate-y-1/2">
                      <AlertCircle className="h-4 w-4 text-destructive" />
                    </div>
                  )}
                  {isValid && (
                    <div className="absolute right-3 top-1/2 -translate-y-1/2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                  )}
                </>
              )}
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Textarea field component
export function TextareaField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  form,
  required,
  disabled,
  className,
  showValidation = true,
  ...props
}: TextareaFieldProps<TFieldValues, TName>) {
  const { formState } = form;
  const error = formState.errors[name];
  const isValid = formState.touchedFields[name] && !error;
  
  return (
    <ShadcnFormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <Textarea
              {...field}
              {...props}
              disabled={disabled}
              state={error ? "error" : isValid && showValidation ? "success" : "default"}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Select field component
export function SelectField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  description,
  form,
  required,
  disabled,
  className,
  options,
  placeholder,
  searchable,
  icon,
  showValidation = true,
}: SelectFieldProps<TFieldValues, TName>) {
  const { formState } = form;
  const error = formState.errors[name];
  const isValid = formState.touchedFields[name] && !error;
  
  return (
    <ShadcnFormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="flex items-center gap-1">
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
          )}
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
            value={field.value}
            disabled={disabled}
          >
            <FormControl>
              <SelectTrigger
                state={error ? "error" : isValid && showValidation ? "success" : "default"}
                icon={icon}
              >
                <SelectValue placeholder={placeholder || `Select ${label?.toLowerCase() || 'option'}`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent searchable={searchable}>
              {options.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  description={option.description}
                  icon={option.icon}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Checkbox field component
export function CheckboxField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  checkboxLabel,
  description,
  form,
  required,
  disabled,
  className,
}: CheckboxFieldProps<TFieldValues, TName>) {
  return (
    <ShadcnFormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("flex flex-row items-start space-x-3 space-y-0 p-2", className)}>
          <FormControl>
            <Checkbox
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
            />
          </FormControl>
          <div className="space-y-1 leading-none">
            {label && (
              <FormLabel className="flex items-center gap-1 cursor-pointer">
                {checkboxLabel || label}
                {required && <span className="text-destructive">*</span>}
              </FormLabel>
            )}
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
}

// Switch field component
export function SwitchField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  label,
  switchLabel,
  description,
  form,
  required,
  disabled,
  className,
}: SwitchFieldProps<TFieldValues, TName>) {
  return (
    <ShadcnFormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("flex flex-row items-center justify-between p-2", className)}>
          <div className="space-y-0.5">
            {label && (
              <FormLabel className="flex items-center gap-1">
                {switchLabel || label}
                {required && <span className="text-destructive">*</span>}
              </FormLabel>
            )}
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </div>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
