import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { searchClients, ClientSearchParams } from "@/services/clients";
import { Client } from "@/store/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";

interface SearchOwnerProps {
  onSelectOwner: (owner: Client) => void;
  selectedOwnerId?: string;
}

const SearchOwner = ({ onSelectOwner, selectedOwnerId }: SearchOwnerProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchType, setSearchType] = useState<"id" | "name" | "phone">("name");
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<Client[]>([]);
  const [selectedOwner, setSelectedOwner] = useState<Client | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Search for owners based on the search term and type
  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    setIsLoading(true);
    try {
      const params: ClientSearchParams = {};

      // Set the appropriate search parameter based on the search type
      if (searchType === "id") {
        params.clientId = searchTerm;
      } else if (searchType === "name") {
        params.name = searchTerm;
      } else if (searchType === "phone") {
        params.phoneNumber = searchTerm;
      }

      console.log("Search params:", params);

      const response = await searchClients(params);
      if (response.success && response.data) {
        setSearchResults(response.data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Error searching for owners:", error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle owner selection
  const handleSelectOwner = (owner: Client) => {
    setSelectedOwner(owner);
    onSelectOwner(owner);
    setIsDialogOpen(false);
  };

  // Load selected owner details if selectedOwnerId is provided
  useEffect(() => {
    const loadSelectedOwner = async () => {
      if (selectedOwnerId) {
        setIsLoading(true);
        try {
          const response = await searchClients({ userProfileId: selectedOwnerId });
          if (response.success && response.data && response.data.data.length > 0) {
            // Fix: Access the first item in the data array correctly
            setSelectedOwner(response.data.data[0]);
          }
        } catch (error) {
          console.error("Error loading selected owner:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadSelectedOwner();
  }, [selectedOwnerId]);

  return (
    <div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start text-left font-normal"
            type="button"
          >
            {selectedOwner ?
              `${selectedOwner.firstName} ${selectedOwner.lastName} (${selectedOwner.phoneNumber})` :
              "Search for owner"}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Search Owner</DialogTitle>
            <DialogDescription>
              Search for a client by name, ID, or phone number
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="flex-1">
                <Input
                  placeholder="Search by name, ID, or phone number"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  variant={searchType === "name" ? "default" : "outline"}
                  onClick={() => setSearchType("name")}
                  type="button"
                >
                  Name
                </Button>
                <Button
                  variant={searchType === "id" ? "default" : "outline"}
                  onClick={() => setSearchType("id")}
                  type="button"
                >
                  ID
                </Button>
                <Button
                  variant={searchType === "phone" ? "default" : "outline"}
                  onClick={() => setSearchType("phone")}
                  type="button"
                >
                  Phone
                </Button>
              </div>
              <Button onClick={handleSearch} disabled={isLoading} type="button">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Search"}
              </Button>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : searchResults.length > 0 ? (
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {searchResults.map((owner) => (
                      <TableRow key={owner.clientId}>
                        <TableCell>{owner.clientId}</TableCell>
                        <TableCell>{`${owner.firstName} ${owner.lastName}`}</TableCell>
                        <TableCell>{owner.email}</TableCell>
                        <TableCell>{owner.phoneNumber}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            onClick={() => handleSelectOwner(owner)}
                            type="button"
                          >
                            Select
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : searchTerm && !isLoading ? (
              <div className="text-center py-8 text-gray-500">
                No owners found matching your search criteria
              </div>
            ) : null}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SearchOwner;
