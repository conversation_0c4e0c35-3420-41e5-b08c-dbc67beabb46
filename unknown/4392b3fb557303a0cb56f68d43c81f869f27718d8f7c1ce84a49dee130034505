import { PaginatedResponse, StandardResponse } from '@/store/types';
import { api } from './api';

// Common pagination parameters interface
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
  status?: number;
  [key: string]: any; // Allow additional filter parameters
}

// Generic function to fetch paginated data
export async function getPaginatedData<T>(
  endpoint: string,
  params: PaginationParams = {}
): Promise<PaginatedResponse<T>> {
  try {
    const queryParams = new URLSearchParams();

    // Always include pagination parameters
    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    // Add all other parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<PaginatedResponse<T>>(`${endpoint}?${queryParams.toString()}`);
  } catch (error: any) {
    // Return a standardized error response
    return {
      success: false,
      status: error.status || 500,
      message: error.message || `Failed to fetch ${endpoint}`,
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: params.page || 1,
          limit: params.limit || 10,
          offset: ((params.page || 1) - 1) * (params.limit || 10),
          totalPages: 0
        }
      }
    };
  }
}

// Generic CRUD operations for all entities

// Generic function to create an entity
export async function createEntity<T, D>(
  endpoint: string,
  data: D
): Promise<StandardResponse<T>> {
  try {
    return await api.post<StandardResponse<T>>(endpoint, data);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || `Failed to create ${endpoint}`,
      data: null as unknown as T
    };
  }
}

// Generic function to fetch a single entity by ID
export async function getEntityById<T>(
  endpoint: string,
  id: string
): Promise<StandardResponse<T>> {
  try {
    return await api.get<StandardResponse<T>>(`${endpoint}/${id}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || `Failed to fetch ${endpoint}`,
      data: null as unknown as T
    };
  }
}

// Generic function to update an entity
export async function updateEntity<T, D>(
  endpoint: string,
  id: string,
  data: D
): Promise<StandardResponse<T>> {
  try {
    return await api.put<StandardResponse<T>>(`${endpoint}/${id}`, data);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || `Failed to update ${endpoint}`,
      data: null as unknown as T
    };
  }
}

// Generic function to delete an entity
export async function deleteEntity(
  endpoint: string,
  id: string
): Promise<StandardResponse<null>> {
  try {
    return await api.delete<StandardResponse<null>>(`${endpoint}/${id}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || `Failed to delete ${endpoint}`,
      data: null
    };
  }
}
