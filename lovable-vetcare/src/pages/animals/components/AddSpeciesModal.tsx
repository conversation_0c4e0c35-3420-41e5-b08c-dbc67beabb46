import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { createSpecies } from "@/services/species";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

interface AddSpeciesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Define form validation schema
const formSchema = z.object({
  name: z.string().min(1, "Species name is required"),
  description: z.string().optional(),
  imageUrl: z.string().optional(),
  status: z.number().default(1)
});

type FormValues = z.infer<typeof formSchema>;

const AddSpeciesModal = ({ open, onOpenChange }: AddSpeciesModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("url");

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      imageUrl: "",
      status: 1
    }
  });

  const mutation = useMutation({
    mutationFn: async (data: FormValues) => {
      // If we have a file, we need to handle it differently
      if (selectedFile) {
        return createSpecies(data, selectedFile);
      }
      return createSpecies(data);
    },
    onSuccess: async (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Species added successfully",
        });
        await queryClient.invalidateQueries({ queryKey: ["species"] });
        resetForm();
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: data.message || "Something went wrong",
          variant: "destructive",
          duration: 2500,
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add species",
        variant: "destructive",
        duration: 2500,
      });
    }
  });

  const resetForm = () => {
    form.reset();
    setSelectedFile(null);
    setPreviewUrl("");
    setActiveTab("url");
  };

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setPreviewUrl("");
    form.setValue("imageUrl", "");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Species</DialogTitle>
          <DialogDescription>
            Enter the details for the new animal species.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Species Name *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter species name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Enter species description"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <Label>Species Image</Label>
              <Tabs
                defaultValue="url"
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="url">Image URL</TabsTrigger>
                  <TabsTrigger value="upload">Upload Image</TabsTrigger>
                </TabsList>
                <TabsContent value="url">
                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Image URL</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="url"
                            placeholder="Enter image URL"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
                <TabsContent value="upload">
                  <div className="space-y-2">
                    <Label htmlFor="imageUpload">Upload Image</Label>
                    <div className="border-2 border-dashed rounded-md p-4 text-center">
                      <Input
                        id="imageUpload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleFileChange}
                      />
                      <Label
                        htmlFor="imageUpload"
                        className="cursor-pointer flex flex-col items-center justify-center gap-2"
                      >
                        {previewUrl ? (
                          <div className="relative">
                            <img
                              src={previewUrl}
                              alt="Preview"
                              className="max-h-40 object-contain"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute top-0 right-0 bg-white rounded-full"
                              onClick={(e) => {
                                e.preventDefault();
                                handleRemoveImage();
                              }}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <>
                            <Upload className="h-8 w-8 text-gray-400" />
                            <span>Click to upload an image</span>
                          </>
                        )}
                      </Label>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetForm();
                  onOpenChange(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Adding..." : "Add Species"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSpeciesModal;
