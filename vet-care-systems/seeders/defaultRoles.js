import Role from '../models/role.model.js';
import Permission from '../models/permission.model.js';

const defaultRoles = [
    {
        roleId: 1001,
        roleName: 'Veterinarian',
        description: 'Licensed veterinarian with full medical privileges',
        category: 'medical',
        permissions: [],
        status: 1
    },
    {
        roleId: 1002,
        roleName: 'Veterinary Technician',
        description: 'Veterinary technician with medical support privileges',
        category: 'medical',
        permissions: [],
        status: 1
    },
    {
        roleId: 1003,
        roleName: 'Receptionist',
        description: 'Front desk staff with appointment and client management privileges',
        category: 'administrative',
        permissions: [],
        status: 1
    },
    {
        roleId: 1004,
        roleName: 'Clinic Manager',
        description: 'Clinic manager with administrative and operational privileges',
        category: 'management',
        permissions: [],
        status: 1
    },
    {
        roleId: 1005,
        roleName: 'Groomer',
        description: 'Pet grooming specialist',
        category: 'service',
        permissions: [],
        status: 1
    },
    {
        roleId: 1006,
        roleName: 'Kennel Assistant',
        description: 'Animal care assistant with basic care privileges',
        category: 'care',
        permissions: [],
        status: 1
    }
];

export const seedDefaultRoles = async () => {
    try {
        console.log('Seeding default roles...');
        
        // Check if roles already exist
        const existingRoles = await Role.countDocuments();
        if (existingRoles > 0) {
            console.log('Roles already exist, skipping seeding');
            return;
        }

        // Create default roles
        for (const roleData of defaultRoles) {
            const existingRole = await Role.findOne({ roleId: roleData.roleId });
            if (!existingRole) {
                await Role.create(roleData);
                console.log(`Created role: ${roleData.roleName}`);
            }
        }

        console.log('Default roles seeded successfully');
    } catch (error) {
        console.error('Error seeding default roles:', error);
    }
};

export default seedDefaultRoles;
