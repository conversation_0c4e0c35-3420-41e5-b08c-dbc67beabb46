import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Inventory Model
 *
 * This model represents inventory items in the veterinary clinic.
 * It tracks medications, supplies, and other items used in the clinic.
 */
const inventorySchema = new mongoose.Schema({
    // Auto-increment ID
    inventoryItemId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Basic item information
    name: {
        type: String,
        required: [true, "Item name is required"],
        trim: true,
        index: true
    },
    itemCode: {
        type: String,
        trim: true,
        unique: true,
        sparse: true // Allow null/undefined values
    },
    description: {
        type: String,
        trim: true
    },

    // Categorization
    category: {
        type: String,
        enum: ['medication', 'vaccine', 'supply', 'equipment', 'food', 'other'],
        required: [true, "Category is required"],
        index: true
    },

    // For medications
    isMedication: {
        type: Boolean,
        default: false,
        index: true
    },
    dosageForm: {
        type: String,
        enum: ['tablet', 'capsule', 'liquid', 'injection', 'cream', 'ointment', 'powder', 'other', null],
        default: null
    },
    activeIngredient: {
        type: String,
        trim: true
    },
    concentration: {
        type: String,
        trim: true
    },

    // Inventory tracking
    quantity: {
        type: Number,
        required: [true, "Quantity is required"],
        min: 0,
        default: 0
    },
    unit: {
        type: String,
        required: [true, "Unit is required"],
        trim: true
    },
    reorderLevel: {
        type: Number,
        default: 10,
        min: 0
    },

    // Pricing
    costPrice: {
        type: Number,
        required: [true, "Cost price is required"],
        min: 0
    },
    sellingPrice: {
        type: Number,
        required: [true, "Selling price is required"],
        min: 0
    },
    currency: {
        type: String,
        required: [true, "Currency is required"],
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },

    // Supplier information
    supplier: {
        name: {
            type: String,
            trim: true
        },
        contactPerson: {
            type: String,
            trim: true
        },
        phoneNumber: {
            type: String,
            trim: true
        },
        email: {
            type: String,
            trim: true,
            lowercase: true
        }
    },

    // Expiration tracking
    expiryDate: {
        type: Date
    },
    batchNumber: {
        type: String,
        trim: true
    },

    // Location in clinic
    location: {
        type: String,
        trim: true
    },

    // Clinic relationship
    clinicId: {
        type: Number,
        ref: 'Clinic',
        required: true,
        index: true
    },

    // Status
    status: {
        type: String,
        enum: ['active', 'discontinued', 'expired', 'recalled'],
        default: 'active',
        index: true
    },

    // Usage tracking
    lastUsedDate: {
        type: Date
    },

    // Transaction history
    transactions: [{
        type: {
            type: String,
            enum: ['purchase', 'sale', 'adjustment', 'transfer', 'expired', 'damaged'],
            required: true
        },
        quantity: {
            type: Number,
            required: true
        },
        date: {
            type: Date,
            default: Date.now
        },
        performedBy: {
            type: Number,
            ref: 'Staff'
        },
        notes: String,
        relatedRecord: {
            recordType: {
                type: String,
                enum: ['healthRecord', 'purchase', 'transfer', 'other']
            },
            recordId: Number
        }
    }]
}, { timestamps: true });

// Add indexes for better query performance
inventorySchema.index({ name: 1, clinicId: 1 });
inventorySchema.index({ category: 1, clinicId: 1 });
inventorySchema.index({ isMedication: 1, clinicId: 1 });
inventorySchema.index({ expiryDate: 1 });
inventorySchema.index({ quantity: 1, reorderLevel: 1 });

// Add auto-increment plugin
inventorySchema.plugin(AutoIncrement, { inc_field: 'inventoryItemId', start_seq: 1001 });
inventorySchema.index({ inventoryItemId: 1 }, { unique: true });

const Inventory = mongoose.model('Inventory', inventorySchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Inventory.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Inventory.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Inventory;
