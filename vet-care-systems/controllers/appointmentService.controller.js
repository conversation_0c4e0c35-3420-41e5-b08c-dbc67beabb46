import AppointmentService from '../models/appointmentService.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Add service to appointment
 */
export const addAppointmentService = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            serviceId,
            serviceName,
            serviceDescription,
            category,
            quantity = 1,
            unitPrice,
            currency = 'KES',
            discount = { percentage: 0, amount: 0, reason: '' },
            performedBy,
            assistedBy = [],
            duration = {},
            notes = '',
            isEmergency = false,
            emergencyCharge = 0,
            afterHoursCharge = 0,
            isAfterHours = false
        } = req.body;

        if (!serviceId || !serviceName || !unitPrice) {
            return sendResponse(res, 400, false, "Service ID, name, and unit price are required");
        }

        const service = await AppointmentService.create({
            appointmentId: parseInt(appointmentId),
            serviceId: parseInt(serviceId),
            serviceName,
            serviceDescription,
            category,
            quantity,
            unitPrice,
            currency,
            discount,
            performedBy: performedBy || req.user?.staffId || req.user?.userId || 1001,
            assistedBy,
            duration,
            notes,
            isEmergency,
            emergencyCharge,
            afterHoursCharge,
            isAfterHours,
            addedBy: req.user?.staffId || req.user?.userId || 1001
        });

        // Get populated service for response
        const populatedService = await AppointmentService.findOne({ 
            appointmentServiceId: service.appointmentServiceId 
        }).lean();
        
        // Add staff data
        const staffData = await AppointmentService.model('Staff').findOne({ 
            staffId: service.performedBy 
        }).lean();
        
        if (staffData) {
            populatedService.performedByData = staffData;
            populatedService.performedByName = `${staffData.firstName} ${staffData.lastName}`;
        }

        return sendResponse(res, 201, true, "Service added to appointment successfully", populatedService);
    } catch (error) {
        console.error("Add appointment service error:", error);
        return sendResponse(res, 400, false, `Failed to add service: ${error.message}`);
    }
};

/**
 * Get all services for an appointment
 */
export const getAppointmentServices = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { status, category } = req.query;

        let query = { appointmentId: parseInt(appointmentId) };
        
        if (status) query.status = status;
        if (category) query.category = category;

        const services = await AppointmentService.find(query)
            .sort({ createdAt: -1 })
            .lean();

        // Populate staff data for each service
        const populatedServices = await Promise.all(services.map(async (service) => {
            const staffData = await AppointmentService.model('Staff').findOne({ 
                staffId: service.performedBy 
            }).lean();
            
            if (staffData) {
                service.performedByData = staffData;
                service.performedByName = `${staffData.firstName} ${staffData.lastName}`;
            }
            
            // Add assisted by staff data
            if (service.assistedBy && service.assistedBy.length > 0) {
                const assistedByData = await Promise.all(
                    service.assistedBy.map(async (staffId) => {
                        const staff = await AppointmentService.model('Staff').findOne({ 
                            staffId 
                        }).lean();
                        return staff ? {
                            staffId: staff.staffId,
                            name: `${staff.firstName} ${staff.lastName}`
                        } : null;
                    })
                );
                service.assistedByData = assistedByData.filter(Boolean);
            }
            
            return service;
        }));

        // Calculate totals
        const totals = {
            subtotal: populatedServices.reduce((sum, service) => sum + service.totalPrice, 0),
            totalDiscount: populatedServices.reduce((sum, service) => {
                const discountAmount = service.discount.percentage > 0 
                    ? (service.totalPrice * service.discount.percentage) / 100
                    : service.discount.amount || 0;
                return sum + discountAmount;
            }, 0),
            totalEmergencyCharges: populatedServices.reduce((sum, service) => sum + (service.emergencyCharge || 0), 0),
            totalAfterHoursCharges: populatedServices.reduce((sum, service) => sum + (service.afterHoursCharge || 0), 0),
            grandTotal: populatedServices.reduce((sum, service) => sum + service.finalAmount, 0)
        };

        return sendResponse(res, 200, true, "Appointment services retrieved successfully", {
            services: populatedServices,
            totals,
            count: populatedServices.length
        });
    } catch (error) {
        console.error("Get appointment services error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve services: ${error.message}`);
    }
};

/**
 * Update appointment service
 */
export const updateAppointmentService = async (req, res) => {
    try {
        const { serviceId } = req.params;
        const updateData = { 
            ...req.body,
            modifiedBy: req.user?.staffId || req.user?.userId
        };

        const service = await AppointmentService.findOneAndUpdate(
            { appointmentServiceId: parseInt(serviceId) },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Add staff data
        const staffData = await AppointmentService.model('Staff').findOne({ 
            staffId: service.performedBy 
        }).lean();
        
        if (staffData) {
            service.performedByData = staffData;
            service.performedByName = `${staffData.firstName} ${staffData.lastName}`;
        }

        return sendResponse(res, 200, true, "Service updated successfully", service);
    } catch (error) {
        console.error("Update appointment service error:", error);
        return sendResponse(res, 400, false, `Failed to update service: ${error.message}`);
    }
};

/**
 * Delete appointment service
 */
export const deleteAppointmentService = async (req, res) => {
    try {
        const { serviceId } = req.params;

        const service = await AppointmentService.findOneAndDelete({ 
            appointmentServiceId: parseInt(serviceId) 
        });

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        return sendResponse(res, 200, true, "Service deleted successfully");
    } catch (error) {
        console.error("Delete appointment service error:", error);
        return sendResponse(res, 500, false, `Failed to delete service: ${error.message}`);
    }
};

/**
 * Update service status
 */
export const updateServiceStatus = async (req, res) => {
    try {
        const { serviceId } = req.params;
        const { status, startTime, endTime, complications, notes } = req.body;

        const updateData = {
            status,
            modifiedBy: req.user?.staffId || req.user?.userId
        };

        if (startTime) updateData.startTime = new Date(startTime);
        if (endTime) updateData.endTime = new Date(endTime);
        if (complications) updateData.complications = complications;
        if (notes) updateData.notes = notes;

        // Calculate actual duration if both times are provided
        if (startTime && endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            updateData['duration.actual'] = Math.round((end - start) / (1000 * 60)); // in minutes
        }

        const service = await AppointmentService.findOneAndUpdate(
            { appointmentServiceId: parseInt(serviceId) },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        return sendResponse(res, 200, true, "Service status updated successfully", service);
    } catch (error) {
        console.error("Update service status error:", error);
        return sendResponse(res, 400, false, `Failed to update service status: ${error.message}`);
    }
};

/**
 * Get services by category
 */
export const getServicesByCategory = async (req, res) => {
    try {
        const { appointmentId, category } = req.params;

        const services = await AppointmentService.find({
            appointmentId: parseInt(appointmentId),
            category
        }).sort({ createdAt: -1 }).lean();

        // Populate staff data
        const populatedServices = await Promise.all(services.map(async (service) => {
            const staffData = await AppointmentService.model('Staff').findOne({ 
                staffId: service.performedBy 
            }).lean();
            
            if (staffData) {
                service.performedByData = staffData;
                service.performedByName = `${staffData.firstName} ${staffData.lastName}`;
            }
            
            return service;
        }));

        return sendResponse(res, 200, true, `${category} services retrieved successfully`, populatedServices);
    } catch (error) {
        console.error("Get services by category error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve ${category} services: ${error.message}`);
    }
};
