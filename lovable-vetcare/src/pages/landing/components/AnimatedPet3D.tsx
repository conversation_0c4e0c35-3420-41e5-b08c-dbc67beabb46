import { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';

interface AnimatedPet3DProps {
  imageSrc: string;
  alt: string;
  delay?: number;
  className?: string;
}

const AnimatedPet3D = ({ imageSrc, alt, delay = 0, className = '' }: AnimatedPet3DProps) => {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.3 });
  
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [controls, isInView]);

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: delay,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const imageVariants = {
    hover: {
      scale: 1.05,
      rotateY: 15,
      rotateX: -10,
      z: 50,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const shadowVariants = {
    hover: {
      opacity: 0.7,
      scale: 1.05,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      className={`relative perspective-1000 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate={controls}
      whileHover="hover"
    >
      <motion.div 
        className="absolute bottom-0 w-4/5 h-[10px] bg-black/20 blur-md rounded-full mx-auto left-0 right-0"
        variants={shadowVariants}
      />
      <motion.div
        className="relative z-10 transform-3d"
        variants={imageVariants}
        style={{ transformStyle: 'preserve-3d' }}
      >
        <img 
          src={imageSrc} 
          alt={alt} 
          className="w-full h-full object-cover rounded-2xl shadow-2xl"
        />
      </motion.div>
    </motion.div>
  );
};

export default AnimatedPet3D;
