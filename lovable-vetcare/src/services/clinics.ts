
import { Clinic, PaginationData, Staff } from '@/store/types';
import { api } from './api';

interface ClinicsListResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Clinic[];
    pagination: PaginationData;
  };
}

interface ClinicResponse {
  success: boolean;
  status: number;
  message: string;
  data: Clinic | null;
}

interface StaffListResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Staff[];
    pagination: PaginationData;
  };
}

interface GetClinicsParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: number;
  ownerId?: number;
}

export const getClinics = async (params?: GetClinicsParams): Promise<ClinicsListResponse> => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status !== undefined) queryParams.append('status', params.status.toString());
    if (params?.ownerId) queryParams.append('ownerId', params.ownerId.toString());

    const url = `/clinics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<ClinicsListResponse>(url);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch clinics',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get a single clinic by ID
 */
export const getClinic = async (clinicId: string): Promise<ClinicResponse> => {
  try {
    return await api.get<ClinicResponse>(`/clinics/${clinicId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch clinic',
      data: null
    };
  }
};

/**
 * Create a new clinic
 */
export const createClinic = async (clinicData: Partial<Clinic>): Promise<ClinicResponse> => {
  try {
    return await api.post<ClinicResponse>('/clinics', clinicData);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create clinic',
      data: null
    };
  }
};

/**
 * Update an existing clinic
 */
export const updateClinic = async (clinicId: string, clinicData: Partial<Clinic>): Promise<ClinicResponse> => {
  try {
    return await api.put<ClinicResponse>(`/clinics/${clinicId}`, clinicData);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update clinic',
      data: null
    };
  }
};

/**
 * Get staff members for a specific clinic
 */
export const getClinicStaff = async (clinicId: string): Promise<StaffListResponse> => {
  try {
    return await api.get<StaffListResponse>(`/staff?clinicId=${clinicId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch clinic staff',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

