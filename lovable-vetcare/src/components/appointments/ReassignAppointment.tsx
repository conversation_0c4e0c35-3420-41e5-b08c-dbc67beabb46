import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { UserCheck, AlertTriangle, Clock, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface Staff {
  staffId: number;
  firstName: string;
  lastName: string;
  email: string;
  specialization?: string;
  status: number;
}

interface ReassignAppointmentProps {
  appointmentId: number;
  currentStaffId: number;
  currentStaffName: string;
  onReassignSuccess?: () => void;
}

const ReassignAppointment: React.FC<ReassignAppointmentProps> = ({
  appointmentId,
  currentStaffId,
  currentStaffName,
  onReassignSuccess
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  const [reason, setReason] = useState('');
  const [notifyClient, setNotifyClient] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch available staff members
  const { data: staffResponse, isLoading: isLoadingStaff } = useQuery({
    queryKey: ['staff', 'active'],
    queryFn: async () => {
      const response = await api.get('/staff?status=1&limit=100');
      return response.data;
    }
  });

  // Reassign appointment mutation
  const reassignMutation = useMutation({
    mutationFn: async (data: { newStaffId: number; reason: string; notifyClient: boolean }) => {
      const response = await api.put(`/appointments/${appointmentId}/reassign`, data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      setIsOpen(false);
      setSelectedStaffId('');
      setReason('');
      setNotifyClient(false);

      toast({
        title: "Success",
        description: `Appointment reassigned to ${data.data.reassignmentDetails.newStaff}`,
      });

      if (onReassignSuccess) {
        onReassignSuccess();
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to reassign appointment",
        variant: "destructive",
      });
    }
  });

  const handleReassign = () => {
    if (!selectedStaffId) {
      toast({
        title: "Error",
        description: "Please select a staff member",
        variant: "destructive",
      });
      return;
    }

    if (!reason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for reassignment",
        variant: "destructive",
      });
      return;
    }

    reassignMutation.mutate({
      newStaffId: parseInt(selectedStaffId),
      reason: reason.trim(),
      notifyClient
    });
  };

  const availableStaff = staffResponse?.data?.data?.filter(
    (staff: Staff) => staff.staffId !== currentStaffId && staff.status === 1
  ) || [];

  const selectedStaff = availableStaff.find((staff: Staff) =>
    staff.staffId.toString() === selectedStaffId
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <UserCheck className="h-4 w-4 mr-2" />
          Reassign
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Reassign Appointment
          </DialogTitle>
          <DialogDescription>
            Reassign this appointment to a different staff member. This action will notify the new staff member and create a note in the appointment history.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current assignment info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">Currently assigned to:</span>
                <span className="font-medium">{currentStaffName}</span>
              </div>
            </CardContent>
          </Card>

          {/* Warning alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Reassigning this appointment will notify the new staff member and create a note in the appointment history.
            </AlertDescription>
          </Alert>

          {/* Staff selection */}
          <div>
            <Label htmlFor="newStaff">Reassign to</Label>
            <Select value={selectedStaffId} onValueChange={setSelectedStaffId}>
              <SelectTrigger>
                <SelectValue placeholder="Select staff member" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingStaff ? (
                  <SelectItem value="loading" disabled>Loading staff...</SelectItem>
                ) : availableStaff.length === 0 ? (
                  <SelectItem value="no-staff" disabled>No available staff</SelectItem>
                ) : (
                  availableStaff.map((staff: Staff) => (
                    <SelectItem key={staff.staffId} value={staff.staffId.toString()}>
                      <div className="flex flex-col">
                        <span>{staff.firstName} {staff.lastName}</span>
                        {staff.specialization && (
                          <span className="text-xs text-gray-500">{staff.specialization}</span>
                        )}
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Selected staff info */}
          {selectedStaff && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-3">
                <div className="text-sm">
                  <div className="font-medium">{selectedStaff.firstName} {selectedStaff.lastName}</div>
                  <div className="text-gray-600">{selectedStaff.email}</div>
                  {selectedStaff.specialization && (
                    <div className="text-blue-600 text-xs mt-1">{selectedStaff.specialization}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Reason */}
          <div>
            <Label htmlFor="reason">Reason for reassignment *</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Please provide a reason for this reassignment..."
              rows={3}
              className="mt-1"
            />
          </div>

          {/* Notify client option */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="notifyClient"
              checked={notifyClient}
              onCheckedChange={(checked) => setNotifyClient(checked as boolean)}
            />
            <Label htmlFor="notifyClient" className="text-sm">
              Notify client about staff change
            </Label>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={reassignMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReassign}
              disabled={reassignMutation.isPending || !selectedStaffId || !reason.trim()}
            >
              {reassignMutation.isPending ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Reassigning...
                </>
              ) : (
                <>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Reassign
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReassignAppointment;
