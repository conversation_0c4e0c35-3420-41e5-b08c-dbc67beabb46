
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/store";
import { Eye, EyeOff, Mail, Lock, MoonStar, Sun } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { useTheme } from "next-themes";

const formSchema = z.object({
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    remember: z.boolean().optional(),
});

const Login = () => {
    const navigate = useNavigate();
    const { toast } = useToast();
    const { login } = useAuth();
    const [showPassword, setShowPassword] = useState(false);
    const [currentBgIndex, setCurrentBgIndex] = useState(0);
    const { theme, setTheme } = useTheme();

    const backgroundImages = [
        "/pets/cat.jpg",
        "/pets/dog.jpg",
        "/pets/parrot.jpg",
        "/pets/horse.jpg",
        "/pets/pig.jpg",
        "/pets/goat.jpg"
    ];

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentBgIndex((prev) => (prev + 1) % backgroundImages.length);
        }, 5000);

        return () => clearInterval(interval);
    }, []);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: "",
            password: "",
            remember: false,
        },
    });

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            // First attempt with default login (which now checks for admin email)
            const response = await login(values.email, values.password);

            if (response.status === 200) {
                toast({
                    title: "Success",
                    description: response.message ?? "Logged in successfully",
                });
                navigate("/dashboard");
                return;
            }

            // If login failed and it's not the admin email, show error
            if (values.email !== '<EMAIL>') {
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: response?.message || "Invalid credentials",
                });
                return;
            }

            // If we're here, it means the admin login failed
            // This shouldn't happen if the store is correctly handling the admin email
            toast({
                variant: "destructive",
                title: "Error",
                description: "Admin login failed. Please contact system support.",
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Something went wrong. Please try again.",
            });
        }
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const toggleTheme = () => {
        setTheme(theme === "dark" ? "light" : "dark");
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
            {/* Theme toggle button */}
            <button
                onClick={toggleTheme}
                className="absolute top-5 right-5 p-2 rounded-full bg-background/50 backdrop-blur-sm border border-border z-50"
                aria-label="Toggle theme"
            >
                {theme === "dark" ? <Sun className="h-5 w-5" /> : <MoonStar className="h-5 w-5" />}
            </button>

            {/* Background with changing images */}
            <div className="absolute inset-0 overflow-hidden">
                {backgroundImages.map((img, index) => (
                    <div
                        key={img}
                        className={`absolute inset-0 transition-opacity duration-1000 bg-cover bg-center ${
                            index === currentBgIndex ? 'opacity-20' : 'opacity-0'
                        }`}
                        style={{ backgroundImage: `url(${img})` }}
                    />
                ))}
                <div className="absolute inset-0 bg-gradient-to-b from-primary-600/40 via-primary-600/20 to-background dark:from-gray-900/70 dark:via-gray-900/50 dark:to-background" />
            </div>

            <div className="w-full max-w-md z-10">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-primary dark:text-primary-200">VetCare Platform</h1>
                    <p className="mt-2 text-foreground/90 dark:text-white/90">Welcome back to your veterinary management system</p>
                </div>

                <Card className="w-full shadow-lg border-0 bg-white/90 backdrop-blur-sm dark:bg-gray-800/90">
                    <CardHeader className="space-y-1">
                        <CardTitle className="text-2xl font-bold text-center dark:text-white">Sign in</CardTitle>
                        <CardDescription className="text-center dark:text-gray-300">
                            Enter your credentials to access your account
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">Email</FormLabel>
                                            <div className="relative">
                                                <FormControl>
                                                    <div className="relative">
                                                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                                                            <Mail className="h-5 w-5" />
                                                        </div>
                                                        <Input
                                                            type="email"
                                                            placeholder="Enter your email"
                                                            className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600"
                                                            {...field}
                                                        />
                                                    </div>
                                                </FormControl>
                                            </div>
                                            <FormMessage className="dark:text-red-400" />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="dark:text-white">Password</FormLabel>
                                            <div className="relative">
                                                <FormControl>
                                                    <div className="relative">
                                                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                                                            <Lock className="h-5 w-5" />
                                                        </div>
                                                        <Input
                                                            type={showPassword ? "text" : "password"}
                                                            placeholder="Enter your password"
                                                            className="pl-10 dark:text-white dark:placeholder:text-gray-400 dark:bg-gray-700/70 dark:border-gray-600"
                                                            {...field}
                                                        />
                                                        <button
                                                            type="button"
                                                            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400"
                                                            onClick={togglePasswordVisibility}
                                                        >
                                                            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                                        </button>
                                                    </div>
                                                </FormControl>
                                            </div>
                                            <FormMessage className="dark:text-red-400" />
                                        </FormItem>
                                    )}
                                />
                                <div className="flex items-center justify-between">
                                    <FormField
                                        control={form.control}
                                        name="remember"
                                        render={({ field }) => (
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id="remember"
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                    className="dark:border-gray-600"
                                                />
                                                <label
                                                    htmlFor="remember"
                                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 dark:text-gray-300"
                                                >
                                                    Remember me
                                                </label>
                                            </div>
                                        )}
                                    />
                                    <Button variant="link" className="text-sm text-primary p-0 dark:text-primary-200" onClick={() => navigate("/forgot-password")}>
                                        Forgot password?
                                    </Button>
                                </div>
                                <Button type="submit" className="w-full rounded-md">
                                    Sign in
                                </Button>
                            </form>
                        </Form>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-4">
                        <div className="relative w-full">
                            <div className="absolute inset-0 flex items-center">
                                <span className="w-full border-t border-gray-200 dark:border-gray-700"></span>
                            </div>
                            <div className="relative flex justify-center text-xs uppercase">
                                <span className="bg-card px-2 text-muted-foreground dark:bg-gray-800 dark:text-gray-400">
                                    Or continue with
                                </span>
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 w-full">
                            <Button variant="outline" className="w-full dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700">
                                Google
                            </Button>
                            <Button variant="outline" className="w-full dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700">
                                Microsoft
                            </Button>
                        </div>
                        <div className="text-center mt-2">
                            <Button variant="link" onClick={() => navigate("/register/owner")} className="dark:text-primary-200">
                                Don't have an account? Sign up
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </div>
        </div>
    );
};

export default Login;
