import Service from '../models/service.model.js';

const defaultServices = [
  // Consultation Services
  {
    serviceName: 'General Consultation',
    serviceCode: 'CONS-001',
    description: 'Basic veterinary consultation and examination',
    category: 'consultation',
    defaultPrice: 1500,
    currency: 'KES',
    estimatedDuration: 30,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Follow-up Consultation',
    serviceCode: 'CONS-002',
    description: 'Follow-up examination after treatment',
    category: 'consultation',
    defaultPrice: 1000,
    currency: 'KES',
    estimatedDuration: 20,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Emergency Consultation',
    serviceCode: 'CONS-003',
    description: 'Emergency veterinary consultation',
    category: 'emergency',
    defaultPrice: 3000,
    currency: 'KES',
    estimatedDuration: 45,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Vaccination Services
  {
    serviceName: 'Rabies Vaccination',
    serviceCode: 'VAC-001',
    description: 'Rabies vaccination for dogs and cats',
    category: 'vaccination',
    defaultPrice: 800,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'DHPP Vaccination (Dogs)',
    serviceCode: 'VAC-002',
    description: 'Distemper, Hepatitis, Parvovirus, Parainfluenza vaccination',
    category: 'vaccination',
    defaultPrice: 1200,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'FVRCP Vaccination (Cats)',
    serviceCode: 'VAC-003',
    description: 'Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia vaccination',
    category: 'vaccination',
    defaultPrice: 1200,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Surgery Services
  {
    serviceName: 'Spay Surgery (Female)',
    serviceCode: 'SURG-001',
    description: 'Ovariohysterectomy for female dogs and cats',
    category: 'surgery',
    defaultPrice: 8000,
    currency: 'KES',
    estimatedDuration: 90,
    requiresAnesthesia: true,
    requiresSpecialist: true,
    specialistType: 'surgeon',
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Neuter Surgery (Male)',
    serviceCode: 'SURG-002',
    description: 'Castration for male dogs and cats',
    category: 'surgery',
    defaultPrice: 6000,
    currency: 'KES',
    estimatedDuration: 60,
    requiresAnesthesia: true,
    requiresSpecialist: true,
    specialistType: 'surgeon',
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Laboratory Services
  {
    serviceName: 'Complete Blood Count (CBC)',
    serviceCode: 'LAB-001',
    description: 'Full blood analysis including white and red blood cells',
    category: 'laboratory',
    defaultPrice: 2500,
    currency: 'KES',
    estimatedDuration: 60,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Blood Chemistry Panel',
    serviceCode: 'LAB-002',
    description: 'Comprehensive metabolic panel',
    category: 'laboratory',
    defaultPrice: 3500,
    currency: 'KES',
    estimatedDuration: 90,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Urinalysis',
    serviceCode: 'LAB-003',
    description: 'Complete urine analysis',
    category: 'laboratory',
    defaultPrice: 1500,
    currency: 'KES',
    estimatedDuration: 30,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Imaging Services
  {
    serviceName: 'X-Ray (Single View)',
    serviceCode: 'IMG-001',
    description: 'Single radiographic image',
    category: 'imaging',
    defaultPrice: 2000,
    currency: 'KES',
    estimatedDuration: 20,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'X-Ray (Multiple Views)',
    serviceCode: 'IMG-002',
    description: 'Multiple radiographic images',
    category: 'imaging',
    defaultPrice: 3500,
    currency: 'KES',
    estimatedDuration: 30,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Ultrasound',
    serviceCode: 'IMG-003',
    description: 'Ultrasound examination',
    category: 'imaging',
    defaultPrice: 4000,
    currency: 'KES',
    estimatedDuration: 45,
    requiresSpecialist: true,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Dental Services
  {
    serviceName: 'Dental Cleaning',
    serviceCode: 'DENT-001',
    description: 'Professional dental cleaning and scaling',
    category: 'dental',
    defaultPrice: 5000,
    currency: 'KES',
    estimatedDuration: 60,
    requiresAnesthesia: true,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Tooth Extraction',
    serviceCode: 'DENT-002',
    description: 'Surgical tooth extraction',
    category: 'dental',
    defaultPrice: 3000,
    currency: 'KES',
    estimatedDuration: 45,
    requiresAnesthesia: true,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Grooming Services
  {
    serviceName: 'Basic Grooming',
    serviceCode: 'GROOM-001',
    description: 'Bath, brush, nail trim, ear cleaning',
    category: 'grooming',
    defaultPrice: 2000,
    currency: 'KES',
    estimatedDuration: 60,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Full Grooming with Haircut',
    serviceCode: 'GROOM-002',
    description: 'Complete grooming service with styling',
    category: 'grooming',
    defaultPrice: 3500,
    currency: 'KES',
    estimatedDuration: 90,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Wellness Services
  {
    serviceName: 'Annual Wellness Exam',
    serviceCode: 'WELL-001',
    description: 'Comprehensive annual health examination',
    category: 'wellness',
    defaultPrice: 2500,
    currency: 'KES',
    estimatedDuration: 45,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Puppy/Kitten Wellness Package',
    serviceCode: 'WELL-002',
    description: 'Complete wellness package for young pets',
    category: 'wellness',
    defaultPrice: 4000,
    currency: 'KES',
    estimatedDuration: 60,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Medication Services
  {
    serviceName: 'Medication Administration',
    serviceCode: 'MED-001',
    description: 'Administration of prescribed medications',
    category: 'medication',
    defaultPrice: 500,
    currency: 'KES',
    estimatedDuration: 10,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Deworming Treatment',
    serviceCode: 'MED-002',
    description: 'Parasite treatment and deworming',
    category: 'medication',
    defaultPrice: 800,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },

  // Additional Services for Appointment Types
  {
    serviceName: 'DHPP Vaccination',
    serviceCode: 'VAC-003',
    description: 'Distemper, Hepatitis, Parvovirus, Parainfluenza vaccination',
    category: 'vaccination',
    defaultPrice: 1200,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'FVRCP Vaccination',
    serviceCode: 'VAC-004',
    description: 'Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia vaccination',
    category: 'vaccination',
    defaultPrice: 1000,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Blood Test',
    serviceCode: 'LAB-001',
    description: 'Complete blood count and chemistry panel',
    category: 'laboratory',
    defaultPrice: 2500,
    currency: 'KES',
    estimatedDuration: 30,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Urinalysis',
    serviceCode: 'LAB-002',
    description: 'Urine analysis and testing',
    category: 'laboratory',
    defaultPrice: 1500,
    currency: 'KES',
    estimatedDuration: 20,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Fecal Examination',
    serviceCode: 'LAB-003',
    description: 'Stool sample analysis for parasites',
    category: 'laboratory',
    defaultPrice: 800,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Tumor Removal',
    serviceCode: 'SURG-003',
    description: 'Surgical removal of tumors and masses',
    category: 'surgery',
    defaultPrice: 12000,
    currency: 'KES',
    estimatedDuration: 120,
    requiresAnesthesia: true,
    requiresSpecialist: true,
    specialistType: 'surgeon',
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Hospitalization Care',
    serviceCode: 'HOSP-001',
    description: 'In-patient monitoring and care',
    category: 'hospitalization',
    defaultPrice: 3000,
    currency: 'KES',
    estimatedDuration: 1440, // 24 hours
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Pet Boarding',
    serviceCode: 'BOARD-001',
    description: 'Pet boarding and accommodation services',
    category: 'boarding',
    defaultPrice: 2000,
    currency: 'KES',
    estimatedDuration: 1440, // 24 hours
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Microchip Implantation',
    serviceCode: 'MICRO-001',
    description: 'Pet identification microchip implantation',
    category: 'identification',
    defaultPrice: 1500,
    currency: 'KES',
    estimatedDuration: 15,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Physical Therapy Session',
    serviceCode: 'PHYS-001',
    description: 'Rehabilitation and physical therapy',
    category: 'therapy',
    defaultPrice: 2500,
    currency: 'KES',
    estimatedDuration: 60,
    requiresSpecialist: true,
    specialistType: 'physiotherapist',
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Behavioral Assessment',
    serviceCode: 'BEHAV-001',
    description: 'Pet behavior evaluation and consultation',
    category: 'behavior',
    defaultPrice: 2000,
    currency: 'KES',
    estimatedDuration: 60,
    requiresSpecialist: true,
    specialistType: 'behaviorist',
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Nutritional Assessment',
    serviceCode: 'NUTR-001',
    description: 'Diet and nutrition consultation',
    category: 'nutrition',
    defaultPrice: 1800,
    currency: 'KES',
    estimatedDuration: 45,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'Euthanasia Service',
    serviceCode: 'EUTH-001',
    description: 'Humane end-of-life service',
    category: 'end-of-life',
    defaultPrice: 5000,
    currency: 'KES',
    estimatedDuration: 60,
    requiresSpecialist: true,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  },
  {
    serviceName: 'General Service',
    serviceCode: 'GEN-001',
    description: 'General veterinary service',
    category: 'general',
    defaultPrice: 1000,
    currency: 'KES',
    estimatedDuration: 30,
    isActive: true,
    isCustom: false,
    createdBy: 1001
  }
];

export const seedDefaultServices = async () => {
  try {
    console.log('Seeding default services...');

    // Check if services already exist
    const existingServices = await Service.countDocuments();
    if (existingServices > 0) {
      console.log('Services already exist, skipping seeding');
      return;
    }

    // Insert default services one by one to handle auto-increment properly
    let insertedCount = 0;
    for (const serviceData of defaultServices) {
      try {
        await Service.create(serviceData);
        insertedCount++;
      } catch (error) {
        if (error.code === 11000) {
          console.log(`Service ${serviceData.serviceName} already exists, skipping...`);
        } else {
          console.error(`Error creating service ${serviceData.serviceName}:`, error.message);
        }
      }
    }

    console.log(`Successfully seeded ${insertedCount} default services`);
  } catch (error) {
    console.error('Error seeding default services:', error);
  }
};

export default defaultServices;
