# Automated Workflows Guide

This document outlines the implementation of automated workflows in the Vet-Care SaaS system, focusing on the transitions between appointments, health records, reminders, invoices, and receipts.

## Table of Contents

1. [Workflow Architecture](#workflow-architecture)
2. [Appointment to Health Record Workflow](#appointment-to-health-record-workflow)
3. [Reminder System](#reminder-system)
4. [Billing and Invoicing Workflow](#billing-and-invoicing-workflow)
5. [Implementation Guide](#implementation-guide)
6. [Testing and Monitoring](#testing-and-monitoring)

## Workflow Architecture

### Event-Driven Architecture

The automated workflows will be implemented using an event-driven architecture:

1. **Event Publishers**: Components that emit events when specific actions occur
2. **Event Subscribers**: Components that listen for events and trigger workflows
3. **Workflow Processors**: Components that execute the workflow steps
4. **State Managers**: Components that track the state of each workflow instance

### Workflow Engine

Implement a workflow engine with the following capabilities:

1. **Workflow Definition**: JSON-based workflow definitions
2. **State Management**: Track the state of each workflow instance
3. **Transition Rules**: Define conditions for moving between workflow states
4. **Action Handlers**: Execute actions when transitions occur
5. **Error Handling**: Handle exceptions and provide retry mechanisms

```javascript
// Example workflow definition
const appointmentToHealthRecordWorkflow = {
  name: "AppointmentToHealthRecord",
  initialState: "scheduled",
  states: {
    scheduled: {
      transitions: [
        {
          event: "appointment.checkin",
          target: "in_progress",
          actions: ["createDraftHealthRecord"]
        },
        {
          event: "appointment.cancel",
          target: "cancelled",
          actions: ["notifyStaff"]
        }
      ]
    },
    in_progress: {
      transitions: [
        {
          event: "appointment.complete",
          target: "completed",
          actions: ["promptHealthRecordCompletion"]
        }
      ]
    },
    completed: {
      transitions: [
        {
          event: "healthRecord.create",
          target: "record_created",
          actions: ["generateInvoice", "scheduleFollowUp"]
        }
      ]
    },
    cancelled: {
      // Terminal state
    },
    record_created: {
      // Terminal state
    }
  }
};
```

## Appointment to Health Record Workflow

### Workflow Steps

1. **Appointment Scheduling**
   - Client books appointment
   - System creates appointment record
   - System sends confirmation to client
   - System adds appointment to clinic calendar

2. **Appointment Check-in**
   - Pet arrives at clinic
   - Staff checks in the appointment
   - System creates draft health record
   - System pre-fills information from appointment

3. **Appointment Completion**
   - Vet completes the examination/procedure
   - Staff marks appointment as completed
   - System prompts for health record completion
   - Vet completes the health record

4. **Post-Appointment Actions**
   - System generates invoice
   - System schedules follow-up if needed
   - System sends summary to client

### Implementation Details

```javascript
// Event handler for appointment check-in
const handleAppointmentCheckin = async (event) => {
  const { appointmentId } = event.payload;
  
  try {
    // Get appointment details
    const appointment = await Appointment.findById(appointmentId)
      .populate('petId')
      .populate('serviceId')
      .populate('clinicId');
    
    // Create draft health record
    const draftHealthRecord = await HealthRecord.create({
      petId: appointment.petId._id,
      clinicId: appointment.clinicId._id,
      serviceId: appointment.serviceId._id,
      appointmentId: appointment._id,
      recordType: appointment.serviceId.category || 'consultation',
      date: new Date(),
      description: `Draft record for ${appointment.serviceId.name}`,
      status: 'draft',
      performedBy: event.userId
    });
    
    // Update appointment with draft health record
    await Appointment.findByIdAndUpdate(appointmentId, {
      status: 'in_progress',
      draftHealthRecordId: draftHealthRecord._id,
      checkinTime: new Date()
    });
    
    // Emit event for next step in workflow
    eventEmitter.emit('appointment.in_progress', {
      appointmentId,
      draftHealthRecordId: draftHealthRecord._id
    });
    
    return draftHealthRecord;
  } catch (error) {
    console.error('Error in appointment check-in workflow:', error);
    throw error;
  }
};

// Register event handler
eventEmitter.on('appointment.checkin', handleAppointmentCheckin);
```

## Reminder System

### Types of Reminders

1. **Appointment Reminders**
   - Pre-appointment reminders (24h, 1h before)
   - Missed appointment follow-ups
   - Rescheduling reminders

2. **Follow-up Reminders**
   - Based on health record follow-up dates
   - Staff notifications for follow-up calls
   - Client reminders for follow-up appointments

3. **Vaccination Reminders**
   - Based on pet vaccination schedules
   - Series completion reminders
   - Annual vaccination reminders

4. **Medication Reminders**
   - Medication refill reminders
   - Medication administration reminders
   - Prescription renewal reminders

### Reminder Workflow

1. **Reminder Creation**
   - System automatically creates reminders based on events
   - Staff can manually create reminders
   - Reminders are scheduled for specific dates/times

2. **Reminder Processing**
   - Scheduled job checks for due reminders
   - System sends reminders via preferred channels
   - System tracks delivery and read status

3. **Reminder Follow-up**
   - System tracks response to reminders
   - Staff can view pending and completed reminders
   - System escalates unresponded reminders

### Implementation Details

```javascript
// Scheduled job to process reminders
const processReminders = async () => {
  const now = new Date();
  
  // Find reminders that are due
  const dueReminders = await Reminder.find({
    scheduledTime: { $lte: now },
    status: 'pending'
  }).populate('recipientId').populate('entityId');
  
  for (const reminder of dueReminders) {
    try {
      // Determine recipient and channel
      const recipient = reminder.recipientId;
      const preferredChannel = recipient.communicationPreferences.preferredChannel || 'email';
      
      // Send reminder based on channel
      switch (preferredChannel) {
        case 'email':
          await sendEmailReminder(reminder);
          break;
        case 'sms':
          await sendSmsReminder(reminder);
          break;
        case 'push':
          await sendPushNotification(reminder);
          break;
        default:
          await sendEmailReminder(reminder);
      }
      
      // Update reminder status
      await Reminder.findByIdAndUpdate(reminder._id, {
        status: 'sent',
        sentTime: new Date()
      });
      
      // Emit event for tracking
      eventEmitter.emit('reminder.sent', {
        reminderId: reminder._id,
        recipientId: recipient._id,
        channel: preferredChannel
      });
    } catch (error) {
      console.error(`Error processing reminder ${reminder._id}:`, error);
      
      // Update reminder with error
      await Reminder.findByIdAndUpdate(reminder._id, {
        status: 'error',
        errorMessage: error.message,
        retryCount: (reminder.retryCount || 0) + 1
      });
    }
  }
};

// Schedule the job to run every minute
setInterval(processReminders, 60000);
```

## Billing and Invoicing Workflow

### Workflow Steps

1. **Service Recording**
   - Health record is completed
   - System identifies billable services and items
   - System calculates costs based on clinic pricing

2. **Invoice Generation**
   - System creates invoice with line items
   - System applies any applicable discounts
   - System calculates taxes
   - System finalizes invoice amount

3. **Payment Processing**
   - Client receives invoice notification
   - Client views invoice online
   - Client makes payment
   - System processes payment
   - System generates receipt

4. **Account Updates**
   - System updates client account balance
   - System records payment in financial system
   - System updates clinic financial reports

### Implementation Details

```javascript
// Event handler for health record completion
const handleHealthRecordComplete = async (event) => {
  const { healthRecordId } = event.payload;
  
  try {
    // Get health record details
    const healthRecord = await HealthRecord.findById(healthRecordId)
      .populate('petId')
      .populate('serviceId')
      .populate('clinicId')
      .populate({
        path: 'petId',
        populate: {
          path: 'ownerId',
          model: 'Client'
        }
      });
    
    // Get client from pet
    const client = healthRecord.petId.ownerId;
    
    // Create invoice items
    const invoiceItems = [
      {
        type: 'service',
        serviceId: healthRecord.serviceId._id,
        description: healthRecord.serviceId.name,
        quantity: 1,
        unitPrice: healthRecord.serviceId.price,
        total: healthRecord.serviceId.price
      }
    ];
    
    // Add medications to invoice
    if (healthRecord.medications && healthRecord.medications.length > 0) {
      for (const medication of healthRecord.medications) {
        // Get medication price from inventory
        const medicationItem = await Inventory.findOne({
          clinicId: healthRecord.clinicId._id,
          itemName: medication.name
        });
        
        if (medicationItem) {
          invoiceItems.push({
            type: 'medication',
            inventoryId: medicationItem._id,
            description: `${medication.name} - ${medication.dosage}`,
            quantity: 1, // Calculate based on dosage and duration
            unitPrice: medicationItem.sellingPrice,
            total: medicationItem.sellingPrice
          });
        }
      }
    }
    
    // Calculate totals
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const taxRate = 0.16; // Get from clinic settings
    const taxAmount = subtotal * taxRate;
    const total = subtotal + taxAmount;
    
    // Create invoice
    const invoice = await Invoice.create({
      clientId: client._id,
      petId: healthRecord.petId._id,
      clinicId: healthRecord.clinicId._id,
      healthRecordId: healthRecord._id,
      invoiceDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      items: invoiceItems,
      subtotal,
      taxRate,
      taxAmount,
      total,
      status: 'pending',
      notes: `Invoice for ${healthRecord.serviceId.name} on ${new Date().toLocaleDateString()}`
    });
    
    // Update health record with invoice
    await HealthRecord.findByIdAndUpdate(healthRecordId, {
      'billingDetails.invoiceId': invoice._id,
      'billingDetails.amount': total,
      'billingDetails.paymentStatus': 'pending'
    });
    
    // Send invoice notification
    await sendInvoiceNotification(client, invoice);
    
    // Emit event for next step in workflow
    eventEmitter.emit('invoice.created', {
      invoiceId: invoice._id,
      clientId: client._id,
      healthRecordId: healthRecord._id
    });
    
    return invoice;
  } catch (error) {
    console.error('Error in invoice generation workflow:', error);
    throw error;
  }
};

// Register event handler
eventEmitter.on('healthRecord.complete', handleHealthRecordComplete);
```

## Implementation Guide

### Backend Implementation

1. **Event System**
   - Implement event emitter/listener system
   - Create event types for all workflow transitions
   - Implement event logging for audit trails

2. **Workflow Engine**
   - Implement workflow definition parser
   - Create state machine for workflow execution
   - Implement action handlers for each workflow step

3. **Scheduler**
   - Implement job scheduler for reminders
   - Create recurring jobs for regular tasks
   - Implement retry mechanism for failed jobs

### Frontend Implementation

1. **Workflow UI**
   - Create visual indicators for workflow states
   - Implement guided workflows for staff
   - Show progress indicators for multi-step processes

2. **Notification Center**
   - Create notification center for staff
   - Implement real-time updates for workflow events
   - Allow staff to take action directly from notifications

3. **Client Portal**
   - Implement appointment booking interface
   - Create invoice payment portal
   - Show reminder notifications to clients

## Testing and Monitoring

### Testing Strategy

1. **Unit Testing**
   - Test individual workflow components
   - Mock event system for isolated testing
   - Verify action handlers work correctly

2. **Integration Testing**
   - Test complete workflow execution
   - Verify correct state transitions
   - Test error handling and recovery

3. **End-to-End Testing**
   - Test workflows from user perspective
   - Verify notifications are sent correctly
   - Test payment processing

### Monitoring

1. **Workflow Monitoring**
   - Track workflow execution metrics
   - Monitor for stuck workflows
   - Alert on workflow failures

2. **Performance Monitoring**
   - Track execution time for workflow steps
   - Monitor database performance
   - Identify bottlenecks in workflow processing

3. **Business Metrics**
   - Track conversion rates (appointments to invoices)
   - Monitor reminder effectiveness
   - Measure payment collection rates
