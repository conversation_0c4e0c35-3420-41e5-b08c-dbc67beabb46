import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, TrendingUp, <PERSON>, Zap, Sparkles } from "lucide-react";
import LottieAnimation from "./LottieAnimation";
import TriangleDecoration from "./TriangleDecoration";

const impactItems = [
  {
    icon: PawPrint,
    title: "Pet-Centric Care",
    description: "Designed with pets' unique needs in mind",
    animation: "/animation/lottie/cat.json",
    color: "bg-primary"
  },
  {
    icon: Heart,
    title: "Improved Outcomes",
    description: "Better health outcomes through comprehensive care",
    animation: "/animation/lottie/dog.json",
    color: "bg-secondary"
  },
  {
    icon: TrendingUp,
    title: "Practice Growth",
    description: "Tools to help your practice thrive and expand",
    animation: "/animation/lottie/bird.json",
    color: "bg-accent"
  },
  {
    icon: Award,
    title: "Quality Standards",
    description: "Maintain the highest standards of veterinary care",
    animation: "/animation/lottie/stethoscope.json",
    color: "bg-primary"
  },
  {
    icon: Zap,
    title: "Efficiency Boost",
    description: "Streamlined workflows save time and reduce errors",
    animation: "/animation/lottie/pill.json",
    color: "bg-secondary"
  },
  {
    icon: Spark<PERSON>,
    title: "Innovation",
    description: "Cutting-edge technology for modern practices",
    animation: "/animation/lottie/stethoscope.json",
    color: "bg-accent"
  }
];

const ImpactSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="impact" className="py-20 bg-gradient-to-b from-background to-background/90 scroll-mt-20 relative overflow-hidden">
      {/* Background decorations */}
      <TriangleDecoration top="5%" left="2%" size={80} color="bg-primary/10" rotate={15} delay={0.2} />
      <TriangleDecoration top="10%" right="5%" size={60} color="bg-secondary/10" rotate={45} delay={0.3} />
      <TriangleDecoration bottom="15%" left="10%" size={70} color="bg-accent/10" rotate={-30} delay={0.4} />

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            Making an Impact
          </h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            VetCare provides a comprehensive suite of tools designed specifically for modern veterinary practices
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {impactItems.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-primary/10 shadow-xl relative overflow-hidden"
            >
              {/* Large Lottie animation in background */}
              <div className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none">
                <LottieAnimation
                  animationPath={item.animation}
                  className="w-full h-full"
                  speed={0.5}
                />
              </div>

              <div className={`${item.color}/20 p-4 rounded-2xl mb-4 w-fit relative z-10`}>
                <item.icon className={`h-8 w-8 ${item.color}`} />
              </div>
              <h3 className="text-2xl font-bold mb-3 relative z-10">{item.title}</h3>
              <p className="text-foreground/70 mb-6 relative z-10">{item.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ImpactSection;
