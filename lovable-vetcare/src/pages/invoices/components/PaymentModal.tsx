import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { 
  CreditCard, 
  Smartphone, 
  Banknote, 
  Building, 
  FileText,
  Shield
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { processPayment } from '@/services/payments';

const paymentSchema = z.object({
  amount: z.number().min(1, 'Amount must be greater than 0'),
  paymentMethod: z.enum(['cash', 'mpesa', 'credit_card', 'debit_card', 'bank_transfer', 'cheque', 'insurance']),
  description: z.string().optional(),
  notes: z.string().optional(),
  // M-Pesa specific
  mpesaPhoneNumber: z.string().optional(),
  // Card specific
  cardLastFourDigits: z.string().optional(),
  cardType: z.string().optional(),
  // Bank transfer specific
  bankReference: z.string().optional(),
  bankName: z.string().optional(),
  // Cheque specific
  chequeNumber: z.string().optional(),
  chequeBank: z.string().optional(),
  // Insurance specific
  insuranceProvider: z.string().optional(),
  insuranceClaimNumber: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

interface PaymentModalProps {
  invoice: any;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  invoice,
  isOpen,
  onClose,
  onSuccess
}) => {
  const { toast } = useToast();
  const [selectedMethod, setSelectedMethod] = useState<string>('');

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      amount: invoice.amountDue,
      description: `Payment for Invoice ${invoice.invoiceNumber}`,
    }
  });

  const paymentMutation = useMutation({
    mutationFn: (data: PaymentFormData) => {
      const paymentDetails: any = {};
      
      // Add method-specific details
      if (data.paymentMethod === 'mpesa') {
        paymentDetails.mpesaPhoneNumber = data.mpesaPhoneNumber;
      } else if (data.paymentMethod === 'credit_card' || data.paymentMethod === 'debit_card') {
        paymentDetails.cardLastFourDigits = data.cardLastFourDigits;
        paymentDetails.cardType = data.cardType;
      } else if (data.paymentMethod === 'bank_transfer') {
        paymentDetails.bankReference = data.bankReference;
        paymentDetails.bankName = data.bankName;
      } else if (data.paymentMethod === 'cheque') {
        paymentDetails.chequeNumber = data.chequeNumber;
        paymentDetails.chequeBank = data.chequeBank;
      } else if (data.paymentMethod === 'insurance') {
        paymentDetails.insuranceProvider = data.insuranceProvider;
        paymentDetails.insuranceClaimNumber = data.insuranceClaimNumber;
      }

      return processPayment(invoice.invoiceId, {
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        paymentDetails,
        description: data.description,
        notes: data.notes,
      });
    },
    onSuccess: (response) => {
      toast({
        title: "Payment Successful",
        description: `Payment of ${invoice.currency} ${form.getValues('amount').toLocaleString()} processed successfully`,
      });
      onSuccess();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Payment Failed",
        description: error.message || "Failed to process payment",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: PaymentFormData) => {
    paymentMutation.mutate(data);
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash':
        return <Banknote className="h-5 w-5" />;
      case 'mpesa':
        return <Smartphone className="h-5 w-5" />;
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="h-5 w-5" />;
      case 'bank_transfer':
        return <Building className="h-5 w-5" />;
      case 'cheque':
        return <FileText className="h-5 w-5" />;
      case 'insurance':
        return <Shield className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const renderMethodSpecificFields = () => {
    switch (selectedMethod) {
      case 'mpesa':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="mpesaPhoneNumber">M-Pesa Phone Number</Label>
              <Input
                id="mpesaPhoneNumber"
                placeholder="************"
                {...form.register('mpesaPhoneNumber')}
              />
            </div>
          </div>
        );
      
      case 'credit_card':
      case 'debit_card':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cardLastFourDigits">Last 4 Digits</Label>
                <Input
                  id="cardLastFourDigits"
                  placeholder="1234"
                  maxLength={4}
                  {...form.register('cardLastFourDigits')}
                />
              </div>
              <div>
                <Label htmlFor="cardType">Card Type</Label>
                <Select onValueChange={(value) => form.setValue('cardType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select card type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="visa">Visa</SelectItem>
                    <SelectItem value="mastercard">Mastercard</SelectItem>
                    <SelectItem value="amex">American Express</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        );
      
      case 'bank_transfer':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bankName">Bank Name</Label>
              <Input
                id="bankName"
                placeholder="Bank name"
                {...form.register('bankName')}
              />
            </div>
            <div>
              <Label htmlFor="bankReference">Reference Number</Label>
              <Input
                id="bankReference"
                placeholder="Transfer reference"
                {...form.register('bankReference')}
              />
            </div>
          </div>
        );
      
      case 'cheque':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="chequeNumber">Cheque Number</Label>
              <Input
                id="chequeNumber"
                placeholder="Cheque number"
                {...form.register('chequeNumber')}
              />
            </div>
            <div>
              <Label htmlFor="chequeBank">Bank</Label>
              <Input
                id="chequeBank"
                placeholder="Bank name"
                {...form.register('chequeBank')}
              />
            </div>
          </div>
        );
      
      case 'insurance':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="insuranceProvider">Insurance Provider</Label>
              <Input
                id="insuranceProvider"
                placeholder="Insurance company"
                {...form.register('insuranceProvider')}
              />
            </div>
            <div>
              <Label htmlFor="insuranceClaimNumber">Claim Number</Label>
              <Input
                id="insuranceClaimNumber"
                placeholder="Claim reference"
                {...form.register('insuranceClaimNumber')}
              />
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Process Payment</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Payment Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>Invoice Number:</span>
                <span className="font-medium">{invoice.invoiceNumber}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Amount:</span>
                <span>{invoice.currency} {invoice.totalAmount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Amount Paid:</span>
                <span className="text-green-600">
                  {invoice.currency} {invoice.amountPaid.toLocaleString()}
                </span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Amount Due:</span>
                <span className="text-red-600">
                  {invoice.currency} {invoice.amountDue.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Details */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="amount">Payment Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                max={invoice.amountDue}
                {...form.register('amount', { valueAsNumber: true })}
              />
              {form.formState.errors.amount && (
                <p className="text-sm text-red-600 mt-1">
                  {form.formState.errors.amount.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Select 
                onValueChange={(value) => {
                  form.setValue('paymentMethod', value as any);
                  setSelectedMethod(value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">
                    <div className="flex items-center gap-2">
                      <Banknote className="h-4 w-4" />
                      Cash
                    </div>
                  </SelectItem>
                  <SelectItem value="mpesa">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4" />
                      M-Pesa
                    </div>
                  </SelectItem>
                  <SelectItem value="credit_card">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Credit Card
                    </div>
                  </SelectItem>
                  <SelectItem value="debit_card">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Debit Card
                    </div>
                  </SelectItem>
                  <SelectItem value="bank_transfer">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Bank Transfer
                    </div>
                  </SelectItem>
                  <SelectItem value="cheque">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Cheque
                    </div>
                  </SelectItem>
                  <SelectItem value="insurance">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Insurance
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Method-specific fields */}
            {renderMethodSpecificFields()}

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                placeholder="Payment description"
                {...form.register('description')}
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes"
                {...form.register('notes')}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={paymentMutation.isPending}
              className="min-w-[120px]"
            >
              {paymentMutation.isPending ? "Processing..." : "Process Payment"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;
