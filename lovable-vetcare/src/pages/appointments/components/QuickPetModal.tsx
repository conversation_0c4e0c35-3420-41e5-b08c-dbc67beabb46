import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { createPet } from '@/services/pets';
import { getSpecies } from '@/services/species';
import { getBreedsBySpecies } from '@/services/breeds';

interface QuickPetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPetCreated: (pet: any) => void;
  clientId: string;
}

export const QuickPetModal: React.FC<QuickPetModalProps> = ({
  isOpen,
  onClose,
  onPetCreated,
  clientId
}) => {
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    name: '',
    speciesId: '',
    breedId: '',
    gender: '',
    dateOfBirth: '',
    color: '',
    weight: '',
    microchipNumber: '',
    notes: ''
  });

  // Fetch species
  const { data: speciesData } = useQuery({
    queryKey: ['species'],
    queryFn: () => getSpecies({ page: 1, limit: 100 })
  });

  // Fetch breeds based on selected species
  const { data: breedsData } = useQuery({
    queryKey: ['breeds', formData.speciesId],
    queryFn: () => getBreedsBySpecies({
      speciesId: formData.speciesId
    }),
    enabled: !!formData.speciesId
  });

  const species = speciesData?.data?.data || [];
  const breeds = breedsData?.data?.data || [];

  const mutation = useMutation({
    mutationFn: createPet,
    onSuccess: (data) => {
      toast.success('Pet created successfully');
      onPetCreated(data.data);
      queryClient.invalidateQueries({ queryKey: ['pets'] });
      handleClose();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create pet');
    }
  });

  const handleClose = () => {
    setFormData({
      name: '',
      speciesId: '',
      breedId: '',
      gender: '',
      dateOfBirth: '',
      color: '',
      weight: '',
      microchipNumber: '',
      notes: ''
    });
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim() || !formData.speciesId || !formData.gender) {
      toast.error('Please fill in required fields (Name, Species, Gender)');
      return;
    }

    const submitData = {
      ...formData,
      clientId: parseInt(clientId),
      speciesId: parseInt(formData.speciesId),
      breedId: formData.breedId ? parseInt(formData.breedId) : undefined,
      weight: formData.weight ? parseFloat(formData.weight) : undefined
    };

    mutation.mutate(submitData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset breed when species changes
    if (field === 'speciesId') {
      setFormData(prev => ({
        ...prev,
        speciesId: value,
        breedId: ''
      }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Quick Add Pet</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Pet Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter pet name"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="speciesId">Species *</Label>
              <Select
                value={formData.speciesId}
                onValueChange={(value) => handleInputChange('speciesId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select species" />
                </SelectTrigger>
                <SelectContent>
                  {species.map((s: any) => (
                    <SelectItem key={s.speciesId} value={s.speciesId?.toString() || ""}>
                      {s.speciesName || s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="breedId">Breed</Label>
              <Select
                value={formData.breedId}
                onValueChange={(value) => handleInputChange('breedId', value)}
                disabled={!formData.speciesId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select breed" />
                </SelectTrigger>
                <SelectContent>
                  {breeds.map((breed: any) => (
                    <SelectItem key={breed.breedId} value={breed.breedId?.toString() || ""}>
                      {breed.breedName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="gender">Gender *</Label>
              <Select
                value={formData.gender}
                onValueChange={(value) => handleInputChange('gender', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateOfBirth">Date of Birth</Label>
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Input
                id="color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                placeholder="Pet color"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="weight">Weight (kg)</Label>
              <Input
                id="weight"
                type="number"
                step="0.1"
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', e.target.value)}
                placeholder="Weight in kg"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="microchipNumber">Microchip Number</Label>
            <Input
              id="microchipNumber"
              value={formData.microchipNumber}
              onChange={(e) => handleInputChange('microchipNumber', e.target.value)}
              placeholder="Microchip number"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes"
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={mutation.isPending}
            >
              {mutation.isPending ? 'Creating...' : 'Create Pet'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
