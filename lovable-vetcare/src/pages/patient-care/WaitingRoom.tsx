import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, UserCheck, UserMinus, PawPrint, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// Mock data for demonstration
const mockPatients = [
  {
    id: "1",
    petName: "Max",
    petType: "Dog",
    breed: "Golden Retriever",
    ownerName: "<PERSON>",
    appointmentTime: "09:00 AM",
    checkInTime: "08:45 AM",
    waitTime: 15,
    status: "waiting",
    reason: "Annual Checkup",
    priority: "normal",
    avatar: "/pets/dog.jpg"
  },
  {
    id: "2",
    petName: "<PERSON>",
    petType: "Cat",
    breed: "Siamese",
    ownerName: "<PERSON>",
    appointmentTime: "09:15 AM",
    checkInTime: "09:05 AM",
    waitTime: 10,
    status: "waiting",
    reason: "Vaccination",
    priority: "normal",
    avatar: "/pets/cat.jpg"
  },
  {
    id: "3",
    petName: "Buddy",
    petType: "Dog",
    breed: "Labrador",
    ownerName: "Michael Brown",
    appointmentTime: "09:30 AM",
    checkInTime: "09:20 AM",
    waitTime: 5,
    status: "with-doctor",
    reason: "Limping",
    priority: "urgent",
    avatar: "/pets/dog.jpg"
  },
  {
    id: "4",
    petName: "Whiskers",
    petType: "Cat",
    breed: "Maine Coon",
    ownerName: "Emily Davis",
    appointmentTime: "10:00 AM",
    checkInTime: "09:50 AM",
    waitTime: 0,
    status: "waiting",
    reason: "Dental Cleaning",
    priority: "normal",
    avatar: "/pets/cat.jpg"
  },
  {
    id: "5",
    petName: "Rocky",
    petType: "Dog",
    breed: "German Shepherd",
    ownerName: "David Wilson",
    appointmentTime: "10:15 AM",
    checkInTime: null,
    waitTime: null,
    status: "scheduled",
    reason: "Skin Condition",
    priority: "normal",
    avatar: "/pets/dog.jpg"
  }
];

type Patient = typeof mockPatients[0];
type PatientStatus = "scheduled" | "waiting" | "with-doctor" | "completed";

const WaitingRoom = () => {
  const [patients, setPatients] = useState<Patient[]>(mockPatients);
  const [activeTab, setActiveTab] = useState<string>("all");
  const { toast } = useToast();

  // Update wait times every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setPatients(prevPatients => 
        prevPatients.map(patient => {
          if (patient.status === "waiting" && patient.waitTime !== null) {
            return { ...patient, waitTime: patient.waitTime + 1 };
          }
          return patient;
        })
      );
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const handleCheckIn = (patientId: string) => {
    setPatients(prevPatients => 
      prevPatients.map(patient => {
        if (patient.id === patientId) {
          const now = new Date();
          const formattedTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          return { 
            ...patient, 
            status: "waiting", 
            checkInTime: formattedTime,
            waitTime: 0
          };
        }
        return patient;
      })
    );
    
    toast({
      title: "Patient Checked In",
      description: "The patient has been added to the waiting room.",
    });
  };

  const handleStartExam = (patientId: string) => {
    setPatients(prevPatients => 
      prevPatients.map(patient => {
        if (patient.id === patientId) {
          return { ...patient, status: "with-doctor" };
        }
        return patient;
      })
    );
    
    toast({
      title: "Exam Started",
      description: "The patient is now with the doctor.",
    });
  };

  const handleCompleteVisit = (patientId: string) => {
    setPatients(prevPatients => 
      prevPatients.map(patient => {
        if (patient.id === patientId) {
          return { ...patient, status: "completed" };
        }
        return patient;
      })
    );
    
    toast({
      title: "Visit Completed",
      description: "The patient visit has been marked as completed.",
    });
  };

  const filteredPatients = patients.filter(patient => {
    if (activeTab === "all") return true;
    return patient.status === activeTab;
  });

  const getStatusBadge = (status: PatientStatus) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline" className="bg-slate-100">Scheduled</Badge>;
      case "waiting":
        return <Badge variant="secondary" className="bg-amber-100 text-amber-800">Waiting</Badge>;
      case "with-doctor":
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">With Doctor</Badge>;
      case "completed":
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>;
      default:
        return null;
    }
  };

  const getPriorityBadge = (priority: string) => {
    if (priority === "urgent") {
      return <Badge variant="destructive" className="ml-2">Urgent</Badge>;
    }
    return null;
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Waiting Room</h1>
        <Button>Check In New Patient</Button>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="waiting">Waiting</TabsTrigger>
          <TabsTrigger value="with-doctor">With Doctor</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPatients.map((patient) => (
              <Card key={patient.id} className={
                patient.priority === "urgent" 
                  ? "border-red-300 shadow-md" 
                  : "shadow-sm"
              }>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={patient.avatar} alt={patient.petName} />
                        <AvatarFallback>
                          <PawPrint size={18} />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{patient.petName}</CardTitle>
                        <CardDescription>{patient.petType} - {patient.breed}</CardDescription>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      {getStatusBadge(patient.status)}
                      {getPriorityBadge(patient.priority)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Owner:</span>
                      <span className="font-medium">{patient.ownerName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Appointment:</span>
                      <span className="font-medium">{patient.appointmentTime}</span>
                    </div>
                    {patient.checkInTime && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Checked In:</span>
                        <span className="font-medium">{patient.checkInTime}</span>
                      </div>
                    )}
                    {patient.waitTime !== null && patient.status === "waiting" && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Wait Time:</span>
                        <span className="font-medium flex items-center">
                          <Clock size={14} className="mr-1" />
                          {patient.waitTime} min
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Reason:</span>
                      <span className="font-medium">{patient.reason}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-2 pt-2">
                  {patient.status === "scheduled" && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleCheckIn(patient.id)}
                    >
                      <UserCheck size={16} className="mr-1" />
                      Check In
                    </Button>
                  )}
                  {patient.status === "waiting" && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleStartExam(patient.id)}
                    >
                      <AlertCircle size={16} className="mr-1" />
                      Start Exam
                    </Button>
                  )}
                  {patient.status === "with-doctor" && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleCompleteVisit(patient.id)}
                    >
                      <CheckCircle2 size={16} className="mr-1" />
                      Complete Visit
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WaitingRoom;
