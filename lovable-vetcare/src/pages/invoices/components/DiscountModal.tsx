import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Percent, DollarSign } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { applyDiscountToInvoice } from '@/services/invoices';

const discountSchema = z.object({
  discountType: z.enum(['percentage', 'fixed']),
  discountValue: z.number().min(0.01, 'Discount value must be greater than 0'),
  reason: z.string().min(1, 'Reason is required'),
});

type DiscountFormData = z.infer<typeof discountSchema>;

interface DiscountModalProps {
  invoice: any;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DiscountModal: React.FC<DiscountModalProps> = ({
  invoice,
  isOpen,
  onClose,
  onSuccess
}) => {
  const { toast } = useToast();

  const form = useForm<DiscountFormData>({
    resolver: zodResolver(discountSchema),
    defaultValues: {
      discountType: 'percentage',
      discountValue: 0,
      reason: '',
    }
  });

  const discountType = form.watch('discountType');
  const discountValue = form.watch('discountValue');

  // Calculate discount amount preview
  const calculateDiscountAmount = () => {
    if (!discountValue || discountValue <= 0) return 0;
    
    if (discountType === 'percentage') {
      return (invoice.subtotal * discountValue) / 100;
    } else {
      return Math.min(discountValue, invoice.subtotal);
    }
  };

  const discountAmount = calculateDiscountAmount();
  const newTotal = Math.max(0, invoice.totalAmount - discountAmount);

  const discountMutation = useMutation({
    mutationFn: (data: DiscountFormData) => {
      return applyDiscountToInvoice(invoice.invoiceId, data);
    },
    onSuccess: (response) => {
      toast({
        title: "Discount Applied",
        description: `Discount of ${invoice.currency} ${discountAmount.toLocaleString()} applied successfully`,
      });
      onSuccess();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Apply Discount",
        description: error.message || "Failed to apply discount",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: DiscountFormData) => {
    discountMutation.mutate(data);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Apply Discount</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Current Invoice Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Invoice</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>Invoice Number:</span>
                <span className="font-medium">{invoice.invoiceNumber}</span>
              </div>
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{invoice.currency} {invoice.subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Current Total:</span>
                <span className="font-semibold">
                  {invoice.currency} {invoice.totalAmount.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Discount Details */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="discountType">Discount Type</Label>
              <Select 
                onValueChange={(value) => form.setValue('discountType', value as 'percentage' | 'fixed')}
                defaultValue="percentage"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select discount type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4" />
                      Percentage
                    </div>
                  </SelectItem>
                  <SelectItem value="fixed">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Fixed Amount
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="discountValue">
                {discountType === 'percentage' ? 'Discount Percentage' : `Discount Amount (${invoice.currency})`}
              </Label>
              <Input
                id="discountValue"
                type="number"
                step={discountType === 'percentage' ? '0.1' : '0.01'}
                min="0"
                max={discountType === 'percentage' ? '100' : invoice.subtotal}
                placeholder={discountType === 'percentage' ? '10' : '100'}
                {...form.register('discountValue', { valueAsNumber: true })}
              />
              {form.formState.errors.discountValue && (
                <p className="text-sm text-red-600 mt-1">
                  {form.formState.errors.discountValue.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="reason">Reason for Discount</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for applying discount (e.g., Senior citizen discount, Loyalty discount, etc.)"
                {...form.register('reason')}
              />
              {form.formState.errors.reason && (
                <p className="text-sm text-red-600 mt-1">
                  {form.formState.errors.reason.message}
                </p>
              )}
            </div>
          </div>

          {/* Discount Preview */}
          {discountValue > 0 && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-lg text-green-800">Discount Preview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Discount Amount:</span>
                  <span className="font-semibold text-green-600">
                    -{invoice.currency} {discountAmount.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>New Total:</span>
                  <span className="font-semibold">
                    {invoice.currency} {newTotal.toLocaleString()}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Savings:</span>
                  <span>
                    {discountType === 'percentage' 
                      ? `${discountValue}% off`
                      : `${invoice.currency} ${discountValue.toLocaleString()} off`
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={discountMutation.isPending || discountAmount <= 0}
              className="min-w-[120px]"
            >
              {discountMutation.isPending ? "Applying..." : "Apply Discount"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DiscountModal;
