import express from 'express';
import {
    processPayment,
    getPaymentById,
    getPaymentsByInvoice,
    refundPayment
} from '../controllers/payment.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Process payment for invoice
router.post('/invoice/:invoiceId', processPayment);

// Get payment by ID
router.get('/:paymentId', getPaymentById);

// Get payments for invoice
router.get('/invoice/:invoiceId/payments', getPaymentsByInvoice);

// Refund payment
router.post('/:paymentId/refund', refundPayment);

export default router;
