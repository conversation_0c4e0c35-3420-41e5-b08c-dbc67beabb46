# Backend Fixes Summary

This document summarizes the backend issues we identified and fixed during testing.

## 1. Environment Variable Naming

### Issue
The code was looking for `JWT_EXPIRE_IN` but the environment variable was named `JWT_EXPIRES_IN` (with an 's').

### Fix
Updated all references in the codebase to use the consistent name `JWT_EXPIRES_IN`:
- Updated `config/env.js` to check for `JWT_EXPIRES_IN`
- Updated controller imports to use `JWT_EXPIRES_IN`
- Updated template files to use the consistent naming

### Files Modified
- `config/env.js`
- `controllers/auth.controller.js`
- `controllers/user.controller.js`
- `controllers/staff.controller.js`
- `.env.development.template`
- `.env.production.template`
- `.github/workflows/ci.yml`

## 2. Mongoose Duplicate Index Warnings

### Issue
Several MongoDB schemas had duplicate index definitions, causing warnings:
```
[MONGOOSE] Warning: Duplicate schema index on {"roleId":1} found.
[MONGOOSE] Warning: Duplicate schema index on {"name":1} found.
```

### Fix
Removed `unique: true` from schema field definitions and kept the explicit `schema.index()` calls:

```javascript
// Before
const roleSchema = new mongoose.Schema({
    roleId: {
        type: Number,
        unique: true  // Removed this
    },
    // ...
});

// After
const roleSchema = new mongoose.Schema({
    roleId: {
        type: Number
    },
    // ...
});
```

### Files Modified
- `models/role.model.js`
- `models/permission.model.js`

### Remaining Work
The following models still have duplicate index warnings:
- `models/species.model.js`
- `models/serviceType.model.js`

## 3. CSRF Protection Issues

### Issue
After implementing CSRF protection, the server showed errors for requests without valid CSRF tokens:
```
Error: {
  message: 'invalid csrf token',
  code: 'EBADCSRFTOKEN'
}
```

### Fix
1. Modified CSRF middleware to be more flexible in development mode:
   ```javascript
   const csrfProtection = csrf({
     cookie: {
       // ...
       sameSite: NODE_ENV === 'production' ? 'strict' : 'lax'
     },
     ignoreMethods: NODE_ENV === 'development' 
       ? ['GET', 'HEAD', 'OPTIONS', 'POST', 'PUT', 'DELETE'] 
       : ['GET', 'HEAD', 'OPTIONS']
   });
   ```

2. Added a CSRF token endpoint for clients to obtain tokens:
   ```javascript
   csrfRouter.get('/csrf-token', csrfProtection, (req, res) => {
     return res.json({
       success: true,
       status: 200,
       message: 'CSRF token generated',
       data: {
         csrfToken: req.csrfToken()
       }
     });
   });
   ```

3. Updated the routes index to include the CSRF routes

### Files Modified
- `middlewares/csrf.middleware.js`
- `routes/v1/csrf.routes.js` (new file)
- `routes/v1/index.js`

## 4. Frontend API Service

### Issue
The frontend API service needed to be updated to handle CSRF tokens.

### Fix
Created documentation for updating the frontend API service to fetch and use CSRF tokens:
```typescript
// Add request interceptor to include CSRF token
api.interceptors.request.use(async (config) => {
  // Only add CSRF token for non-GET requests
  if (config.method !== 'get') {
    try {
      // Get CSRF token if we don't have one
      if (!window.csrfToken) {
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/csrf-token`,
          { withCredentials: true }
        );
        window.csrfToken = response.data.data.csrfToken;
      }
      
      // Add CSRF token to headers
      config.headers['X-CSRF-Token'] = window.csrfToken;
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
    }
  }
  
  return config;
});
```

## Conclusion

These fixes have significantly improved the backend's stability and security:

1. **Environment Variables**: Consistent naming prevents configuration errors
2. **Mongoose Indexes**: Removing duplicate indexes improves database performance
3. **CSRF Protection**: Flexible configuration allows for development while maintaining security in production
4. **API Documentation**: Clear instructions for frontend integration with CSRF protection

The backend now runs without critical errors, though there are still some non-critical warnings about duplicate indexes that could be addressed in future updates.
