import Appointment from '../models/appointment.model.js';
import AppointmentType from '../models/appointmentType.model.js';
import Staff from '../models/staff.model.js';
import HealthRecord from '../models/healthRecord.model.js';
import ServiceType from '../models/serviceType.model.js';
import PetClinicRelationship from '../models/petClinicRelationship.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

/**
 * Create a new appointment
 */
export const createAppointment = async (req, res) => {
    try {
        const appointment = await Appointment.create(req.body);

        // Manual population since we're using numeric IDs instead of ObjectIds
        const populatedAppointment = await Appointment.findOne({ appointmentId: appointment.appointmentId })
            .lean();

        // Get related data manually
        const [pet, staff, client, appointmentTypes] = await Promise.all([
            appointment.petId ? Appointment.model('Pet').findOne({ petId: appointment.petId }).lean() : null,
            appointment.staffId ? Appointment.model('Staff').findOne({ staffId: appointment.staffId }).lean() : null,
            appointment.clientId ? Appointment.model('Client').findOne({ clientId: appointment.clientId }).lean() : null,
            appointment.appointmentTypes?.length > 0 ?
                Appointment.model('AppointmentType').find({
                    appointmentTypeId: { $in: appointment.appointmentTypes.map(at => at.appointmentTypeId) }
                }).lean() : []
        ]);

        // Add populated data
        if (pet) {
            populatedAppointment.petData = pet;
            populatedAppointment.petName = pet.petName || pet.name;
        }
        if (staff) {
            populatedAppointment.staffData = staff;
            populatedAppointment.staffName = `${staff.firstName} ${staff.lastName}`;
        }
        if (client) {
            populatedAppointment.clientData = client;
            populatedAppointment.clientName = `${client.firstName} ${client.lastName}`;
        }
        if (appointmentTypes.length > 0) {
            populatedAppointment.appointmentTypesData = appointmentTypes;
        }

        return sendResponse(res, 201, true, "Appointment created successfully", populatedAppointment);
    } catch (error) {
        console.error("Appointment creation error:", error);
        return sendResponse(res, 400, false, `Appointment validation failed: ${error.message}`);
    }
};

/**
 * Get all appointments with pagination and filters
 */
export const getAllAppointments = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "dateTime",
            sortOrder = "asc",
            status,
            petId,
            staffId,
            clinicId,
            startDate,
            endDate,
            search,
            petName,
            ownerName,
            phone,
            createdBy,
            updatedBy
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status) query.status = status;
        if (petId) query.petId = petId;
        if (staffId) query.staffId = staffId;
        if (clinicId) query.clinicId = clinicId;
        if (createdBy) query.createdBy = createdBy;
        if (updatedBy) query.updatedBy = updatedBy;

        // Handle date range filters
        if (startDate && endDate) {
            query.dateTime = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        } else if (startDate) {
            query.dateTime = { $gte: new Date(startDate) };
        } else if (endDate) {
            query.dateTime = { $lte: new Date(endDate) };
        }

        // Handle specific search parameters
        let searchPetIds = [];
        let searchClientIds = [];
        let searchStaffIds = [];

        // Search by pet name
        if (petName) {
            const pets = await Appointment.model('Pet').find({
                petName: { $regex: petName, $options: "i" }
            }).distinct('petId');
            searchPetIds = pets;
        }

        // Search by owner name
        if (ownerName) {
            const clients = await Appointment.model('Client').find({
                $or: [
                    { firstName: { $regex: ownerName, $options: "i" } },
                    { lastName: { $regex: ownerName, $options: "i" } },
                    { $expr: { $regexMatch: {
                        input: { $concat: ["$firstName", " ", "$lastName"] },
                        regex: ownerName,
                        options: "i"
                    }}}
                ]
            }).distinct('clientId');
            searchClientIds = clients;
        }

        // Search by phone number
        if (phone) {
            const clients = await Appointment.model('Client').find({
                phone: { $regex: phone, $options: "i" }
            }).distinct('clientId');
            searchClientIds = [...new Set([...searchClientIds, ...clients])];
        }

        // Handle general search across multiple related entities
        if (search) {
            // Get all related entities in parallel
            const [pets, vets, clients] = await Promise.all([
                Appointment.model('Pet').find({
                    petName: { $regex: search, $options: "i" }
                }).distinct('petId'),
                Appointment.model('Staff').find({
                    $or: [
                        { firstName: { $regex: search, $options: "i" } },
                        { lastName: { $regex: search, $options: "i" } },
                        { $expr: { $regexMatch: {
                            input: { $concat: ["$firstName", " ", "$lastName"] },
                            regex: search,
                            options: "i"
                        }}}
                    ]
                }).distinct('staffId'),
                Appointment.model('Client').find({
                    $or: [
                        { firstName: { $regex: search, $options: "i" } },
                        { lastName: { $regex: search, $options: "i" } },
                        { phone: { $regex: search, $options: "i" } },
                        { $expr: { $regexMatch: {
                            input: { $concat: ["$firstName", " ", "$lastName"] },
                            regex: search,
                            options: "i"
                        }}}
                    ]
                }).distinct('clientId')
            ]);

            searchPetIds = [...new Set([...searchPetIds, ...pets])];
            searchStaffIds = [...new Set([...searchStaffIds, ...vets])];
            searchClientIds = [...new Set([...searchClientIds, ...clients])];
        }

        // Apply search filters to query
        if (searchPetIds.length > 0 || searchClientIds.length > 0 || searchStaffIds.length > 0 || search) {
            query.$or = [];

            if (searchPetIds.length > 0) {
                query.$or.push({ petId: { $in: searchPetIds } });
            }

            if (searchClientIds.length > 0) {
                query.$or.push({ clientId: { $in: searchClientIds } });
            }

            if (searchStaffIds.length > 0) {
                query.$or.push({ staffId: { $in: searchStaffIds } });
            }

            // Also search in notes and reason fields
            if (search) {
                query.$or.push(
                    { notes: { $regex: search, $options: "i" } },
                    { reason: { $regex: search, $options: "i" } }
                );
            }

            // If no results found from related entities but search terms exist, ensure no results
            if (query.$or.length === 0 && (petName || ownerName || phone || search)) {
                query._id = { $exists: false }; // This will return no results
            }
        }

        // Get all users for createdBy/updatedBy population
        const users = await Staff.find().select('-password -__v').lean();
        const userMap = users.reduce((map, user) => {
            map[user.staffId.toString()] = user;
            return map;
        }, {});

        // Get appointments and count in parallel (without populate since we use numeric IDs)
        const [appointments, totalCount, appointmentTypes] = await Promise.all([
            Appointment.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Appointment.countDocuments(query),
            // Get all appointment types in one query
            AppointmentType.find().lean()
        ]);

        // Create appointment type map for efficient lookups
        const appointmentTypeMap = appointmentTypes.reduce((map, type) => {
            map[type.appointmentTypeId.toString()] = type;
            return map;
        }, {});

        // Get all unique pet IDs, staff IDs, and client IDs from appointments
        const petIds = [...new Set(appointments.map(apt => apt.petId).filter(Boolean))];
        const staffIds = [...new Set(appointments.map(apt => apt.staffId).filter(Boolean))];
        const clientIds = [...new Set(appointments.map(apt => apt.clientId).filter(Boolean))];

        // Fetch all related data in parallel
        const [pets, staff, clients] = await Promise.all([
            petIds.length > 0 ? Appointment.model('Pet').find({ petId: { $in: petIds } }).lean() : [],
            staffIds.length > 0 ? Appointment.model('Staff').find({ staffId: { $in: staffIds } }).lean() : [],
            clientIds.length > 0 ? Appointment.model('Client').find({ clientId: { $in: clientIds } }).lean() : []
        ]);

        // Create lookup maps
        const petMap = pets.reduce((map, pet) => {
            map[pet.petId.toString()] = pet;
            return map;
        }, {});

        const staffMap = staff.reduce((map, staffMember) => {
            map[staffMember.staffId.toString()] = staffMember;
            return map;
        }, {});

        const clientMap = clients.reduce((map, client) => {
            map[client.clientId.toString()] = client;
            return map;
        }, {});

        // Populate all appointment data
        const populatedAppointments = appointments.map(appointment => {
            // Populate appointment types
            if (appointment.appointmentTypes && appointment.appointmentTypes.length > 0) {
                appointment.appointmentTypes = appointment.appointmentTypes.map(appType => ({
                    ...appType,
                    appointmentTypeId: appointmentTypeMap[appType.appointmentTypeId.toString()] || appType.appointmentTypeId
                }));
            }

            // Populate pet data
            if (appointment.petId && petMap[appointment.petId.toString()]) {
                const pet = petMap[appointment.petId.toString()];
                appointment.petData = pet;
                appointment.petName = pet.petName || pet.name;
            }

            // Populate staff data
            if (appointment.staffId && staffMap[appointment.staffId.toString()]) {
                const staffMember = staffMap[appointment.staffId.toString()];
                appointment.staffData = staffMember;
                appointment.staffName = `${staffMember.firstName} ${staffMember.lastName}`;
            }

            // Populate client data
            if (appointment.clientId && clientMap[appointment.clientId.toString()]) {
                const client = clientMap[appointment.clientId.toString()];
                appointment.clientData = client;
                appointment.clientName = `${client.firstName} ${client.lastName}`;
            }

            // Populate creator/updater if userMap exists
            if (typeof userMap !== 'undefined') {
                if (appointment.createdBy) {
                    appointment.createdBy = userMap[appointment.createdBy.toString()] || appointment.createdBy;
                }
                if (appointment.updatedBy) {
                    appointment.updatedBy = userMap[appointment.updatedBy.toString()] || appointment.updatedBy;
                }
            }

            return appointment;
        });

        return sendResponse(
            res,
            200,
            true,
            "Appointments retrieved successfully",
            paginateResults(populatedAppointments, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};



/**
 * Get appointment types with pagination
 */
export const getAppointmentTypes = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [types, totalCount] = await Promise.all([
            AppointmentType.find({ isActive: true })
                .sort({ name: 1 })
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            AppointmentType.countDocuments({ isActive: true })
        ]);

        // console.log(`Found ${types.length} appointment types out of ${totalCount} total`);

        return sendResponse(
            res,
            200,
            true,
            "Appointment types retrieved successfully",
            paginateResults(types, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get single appointment by ID
 */
export const getAppointmentById = async (req, res) => {
    try {
        const appointment = await Appointment.findOne({ appointmentId: parseInt(req.params.appointmentId) })
            .lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Manual population for numeric IDs
        const [pet, staff, client, appointmentTypes] = await Promise.all([
            appointment.petId ? Appointment.model('Pet').findOne({ petId: appointment.petId }).lean() : null,
            appointment.staffId ? Appointment.model('Staff').findOne({ staffId: appointment.staffId }).lean() : null,
            appointment.clientId ? Appointment.model('Client').findOne({ clientId: appointment.clientId }).lean() : null,
            appointment.appointmentTypes?.length > 0 ?
                Appointment.model('AppointmentType').find({
                    appointmentTypeId: { $in: appointment.appointmentTypes.map(at => at.appointmentTypeId) }
                }).lean() : []
        ]);

        // Add populated data
        if (pet) {
            appointment.petData = pet;
            appointment.petName = pet.petName || pet.name;
        }
        if (staff) {
            appointment.staffData = staff;
            appointment.staffName = `${staff.firstName} ${staff.lastName}`;
        }
        if (client) {
            appointment.clientData = client;
            appointment.clientName = `${client.firstName} ${client.lastName}`;
        }
        if (appointmentTypes.length > 0) {
            appointment.appointmentTypesData = appointmentTypes;
        }

        return sendResponse(res, 200, true, "Appointment retrieved successfully", appointment);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update appointment
 */
export const updateAppointment = async (req, res) => {
    try {
        const appointment = await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(req.params.appointmentId) },
            { $set: req.body },
            { new: true, runValidators: true }
        ).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Manual population for numeric IDs
        const [pet, staff, client, appointmentTypes] = await Promise.all([
            appointment.petId ? Appointment.model('Pet').findOne({ petId: appointment.petId }).lean() : null,
            appointment.staffId ? Appointment.model('Staff').findOne({ staffId: appointment.staffId }).lean() : null,
            appointment.clientId ? Appointment.model('Client').findOne({ clientId: appointment.clientId }).lean() : null,
            appointment.appointmentTypes?.length > 0 ?
                Appointment.model('AppointmentType').find({
                    appointmentTypeId: { $in: appointment.appointmentTypes.map(at => at.appointmentTypeId) }
                }).lean() : []
        ]);

        // Add populated data
        if (pet) {
            appointment.petData = pet;
            appointment.petName = pet.petName || pet.name;
        }
        if (staff) {
            appointment.staffData = staff;
            appointment.staffName = `${staff.firstName} ${staff.lastName}`;
        }
        if (client) {
            appointment.clientData = client;
            appointment.clientName = `${client.firstName} ${client.lastName}`;
        }
        if (appointmentTypes.length > 0) {
            appointment.appointmentTypesData = appointmentTypes;
        }

        return sendResponse(res, 200, true, "Appointment updated successfully", appointment);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete appointment
 */
export const deleteAppointment = async (req, res) => {
    try {
        const appointment = await Appointment.findOneAndDelete({ appointmentId: parseInt(req.params.appointmentId) });

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        return sendResponse(res, 200, true, "Appointment deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Complete appointment and create health record
 */
export const completeAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            diagnosis,
            treatment,
            medications = [],
            labResults = [],
            vitalSigns = {},
            followUpDate,
            followUpInstructions,
            notes,
            serviceId
        } = req.body;

        // Get appointment details
        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        if (appointment.status === 'completed') {
            return sendResponse(res, 400, false, "Appointment already completed");
        }

        // Get appointment type details for service mapping
        const appointmentType = await AppointmentType.findOne({
            appointmentTypeId: appointment.appointmentTypes[0].appointmentTypeId
        }).lean();

        // Auto-map appointment type to service type or use provided serviceId
        let selectedServiceId = serviceId;
        if (!selectedServiceId && appointmentType) {
            // Create a default service type based on appointment type
            selectedServiceId = appointmentType.appointmentTypeId; // Use appointment type ID as service ID for now
        }

        // If still no service ID, create a default one
        if (!selectedServiceId) {
            selectedServiceId = 1001; // Default service type ID
        }

        // Find or create pet-clinic relationship
        let petClinicRelationship = await PetClinicRelationship.findOne({
            petId: appointment.petId,
            clinicId: appointment.clinicId || req.user?.clinicId || 1001
        }).lean();

        if (!petClinicRelationship) {
            petClinicRelationship = await PetClinicRelationship.create({
                petId: appointment.petId,
                clinicId: appointment.clinicId || req.user?.clinicId || 1001,
                registeredBy: appointment.staffId
            });
        }

        // Calculate billing amount with after-hours charge
        const baseAmount = appointment.appointmentTypes[0].price || 1000; // Default price
        const appointmentTime = new Date(appointment.dateTime);
        const appointmentHour = appointmentTime.getHours();

        // Check if appointment is after hours (before 8 AM or after 6 PM)
        const isAfterHours = appointmentHour < 8 || appointmentHour >= 18;
        const afterHoursCharge = isAfterHours ? baseAmount * 0.5 : 0; // 50% surcharge
        const totalAmount = baseAmount + afterHoursCharge;

        // Create health record
        const healthRecord = await HealthRecord.create({
            petId: appointment.petId,
            serviceId: selectedServiceId,
            performedBy: appointment.staffId,
            clinicId: appointment.clinicId || req.user?.clinicId || 1001,
            appointmentId: appointment.appointmentId,
            petClinicRelationshipId: petClinicRelationship.petClinicRelationshipId,
            recordType: appointmentType?.name?.toLowerCase().includes('vaccination') ? 'vaccination' :
                       appointmentType?.name?.toLowerCase().includes('surgery') ? 'surgery' :
                       appointmentType?.name?.toLowerCase().includes('laboratory') ? 'laboratory' :
                       'consultation',
            description: `${appointmentType?.name || 'Appointment'} - ${appointment.reason}`,
            diagnosis,
            treatment,
            medications,
            labResults,
            vitalSigns,
            followUpDate: followUpDate ? new Date(followUpDate) : undefined,
            followUpInstructions,
            notes,
            billingDetails: {
                baseAmount,
                afterHoursCharge,
                amount: totalAmount,
                currency: appointment.appointmentTypes[0].currency || 'KES',
                paymentStatus: 'pending',
                isAfterHours,
                visitTime: appointmentTime
            }
        });

        // Update appointment status
        await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(appointmentId) },
            { status: 'completed' }
        );

        // Get populated health record for response
        const populatedHealthRecord = await HealthRecord.findOne({
            healthRecordId: healthRecord.healthRecordId
        }).lean();

        // Add related data manually (since we removed refs)
        const [petData, staffData, clientData] = await Promise.all([
            HealthRecord.model('Pet').findOne({ petId: appointment.petId }).lean(),
            HealthRecord.model('Staff').findOne({ staffId: appointment.staffId }).lean(),
            HealthRecord.model('Client').findOne({ clientId: appointment.clientId }).lean()
        ]);

        populatedHealthRecord.petData = petData;
        populatedHealthRecord.staffData = staffData;
        populatedHealthRecord.clientData = clientData;

        return sendResponse(res, 200, true, "Appointment completed and health record created", {
            appointment: { ...appointment, status: 'completed' },
            healthRecord: populatedHealthRecord
        });
    } catch (error) {
        console.error("Complete appointment error:", error);
        return sendResponse(res, 400, false, `Failed to complete appointment: ${error.message}`);
    }
};

/**
 * Reassign appointment to different staff member
 */
export const reassignAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { newStaffId, reason, notifyClient = false } = req.body;

        if (!newStaffId) {
            return sendResponse(res, 400, false, "New staff ID is required");
        }

        // Get current appointment
        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        if (appointment.status === 'completed') {
            return sendResponse(res, 400, false, "Cannot reassign completed appointment");
        }

        // Verify new staff exists and is active
        const newStaff = await Appointment.model('Staff').findOne({
            staffId: parseInt(newStaffId),
            status: 1
        }).lean();

        if (!newStaff) {
            return sendResponse(res, 404, false, "New staff member not found or inactive");
        }

        // Get old staff info for logging
        const oldStaff = await Appointment.model('Staff').findOne({
            staffId: appointment.staffId
        }).lean();

        // Update appointment
        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(appointmentId) },
            {
                staffId: parseInt(newStaffId),
                updatedBy: req.user?.staffId || req.user?.userId
            },
            { new: true, runValidators: true }
        ).lean();

        // Create reassignment note
        const AppointmentNote = await import('../models/appointmentNote.model.js').then(m => m.default);
        await AppointmentNote.create({
            appointmentId: parseInt(appointmentId),
            staffId: req.user?.staffId || req.user?.userId || 1001,
            noteType: 'general',
            category: 'other',
            title: 'Appointment Reassigned',
            content: `Appointment reassigned from ${oldStaff?.firstName} ${oldStaff?.lastName} to ${newStaff.firstName} ${newStaff.lastName}. Reason: ${reason || 'No reason provided'}`,
            priority: 'normal',
            visibility: 'all_staff'
        });

        // Populate updated appointment data
        const [pet, staff, client, appointmentTypes] = await Promise.all([
            updatedAppointment.petId ? Appointment.model('Pet').findOne({ petId: updatedAppointment.petId }).lean() : null,
            updatedAppointment.staffId ? Appointment.model('Staff').findOne({ staffId: updatedAppointment.staffId }).lean() : null,
            updatedAppointment.clientId ? Appointment.model('Client').findOne({ clientId: updatedAppointment.clientId }).lean() : null,
            updatedAppointment.appointmentTypes?.length > 0 ?
                Appointment.model('AppointmentType').find({
                    appointmentTypeId: { $in: updatedAppointment.appointmentTypes.map(at => at.appointmentTypeId) }
                }).lean() : []
        ]);

        // Add populated data
        if (pet) {
            updatedAppointment.petData = pet;
            updatedAppointment.petName = pet.petName || pet.name;
        }
        if (staff) {
            updatedAppointment.staffData = staff;
            updatedAppointment.staffName = `${staff.firstName} ${staff.lastName}`;
        }
        if (client) {
            updatedAppointment.clientData = client;
            updatedAppointment.clientName = `${client.firstName} ${client.lastName}`;
        }
        if (appointmentTypes.length > 0) {
            updatedAppointment.appointmentTypesData = appointmentTypes;
        }

        // TODO: Send notification to client if notifyClient is true
        // TODO: Send notification to new staff member
        // TODO: Send notification to old staff member

        return sendResponse(res, 200, true, "Appointment reassigned successfully", {
            appointment: updatedAppointment,
            reassignmentDetails: {
                oldStaff: oldStaff ? `${oldStaff.firstName} ${oldStaff.lastName}` : 'Unknown',
                newStaff: `${newStaff.firstName} ${newStaff.lastName}`,
                reason: reason || 'No reason provided',
                reassignedBy: req.user?.staffId || req.user?.userId,
                reassignedAt: new Date()
            }
        });
    } catch (error) {
        console.error("Reassign appointment error:", error);
        return sendResponse(res, 400, false, `Failed to reassign appointment: ${error.message}`);
    }
};

/**
 * Update appointment progress (services and notes)
 */
export const updateAppointmentProgress = async (req, res) => {
    try {
        console.log('updateAppointmentProgress called with params:', req.params);
        console.log('updateAppointmentProgress called with body:', req.body);

        const { appointmentId } = req.params;
        const { services, notes, status } = req.body;

        console.log('Parsed appointmentId:', appointmentId);

        // Validate appointment exists
        const appointment = await Appointment.findOne({ appointmentId: parseInt(appointmentId) });
        console.log('Found appointment:', appointment ? 'Yes' : 'No');

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Update appointment with services and notes
        const updateData = {};

        if (services) {
            updateData.services = services;
        }

        if (notes) {
            updateData.appointmentNotes = notes;
        }

        if (status) {
            updateData.status = status;
        }

        updateData.updatedAt = new Date();

        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(appointmentId) },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        return sendResponse(res, 200, true, "Appointment progress updated successfully", updatedAppointment);
    } catch (error) {
        console.error("Update appointment progress error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
