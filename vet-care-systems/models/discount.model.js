import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Discount Model
 *
 * This model tracks discounts applied to health records and appointments
 */
const discountSchema = new mongoose.Schema({
    // Auto-increment ID
    discountId: {
        type: Number,
        index: true,
        unique: true,
    },

    // Reference to what the discount was applied to
    healthRecordId: {
        type: Number,
        required: true,
        index: true
    },
    appointmentId: {
        type: Number,
        index: true
    },

    // Discount details
    discountType: {
        type: String,
        enum: ['percentage', 'fixed_amount', 'waiver'],
        required: true
    },
    discountValue: {
        type: Number,
        required: true,
        min: 0
    },
    discountAmount: {
        type: Number,
        required: true,
        min: 0
    },

    // Original and final amounts
    originalAmount: {
        type: Number,
        required: true,
        min: 0
    },
    finalAmount: {
        type: Number,
        required: true,
        min: 0
    },

    // Currency
    currency: {
        type: String,
        required: true,
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },

    // Discount reason and authorization
    reason: {
        type: String,
        required: true,
        trim: true
    },
    authorizedBy: {
        type: Number,
        required: true,
        index: true
    },

    // Client and clinic information
    clientId: {
        type: Number,
        required: true,
        index: true
    },
    clinicId: {
        type: Number,
        required: true,
        index: true
    },

    // Discount category
    category: {
        type: String,
        enum: ['senior_discount', 'loyalty_discount', 'emergency_waiver', 'staff_discount', 'promotional', 'hardship', 'other'],
        required: true
    },

    // Status
    status: {
        type: String,
        enum: ['active', 'revoked', 'expired'],
        default: 'active'
    },

    // Approval workflow
    approvalRequired: {
        type: Boolean,
        default: false
    },
    approvedBy: {
        type: Number
    },
    approvalDate: Date,

    // Validity period
    validFrom: {
        type: Date,
        default: Date.now
    },
    validUntil: Date,

    // Additional notes
    notes: {
        type: String,
        trim: true
    }
}, { timestamps: true });

// Add indexes for better query performance
discountSchema.index({ healthRecordId: 1 });
discountSchema.index({ appointmentId: 1 });
discountSchema.index({ clientId: 1 });
discountSchema.index({ clinicId: 1 });
discountSchema.index({ authorizedBy: 1 });
discountSchema.index({ category: 1 });
discountSchema.index({ status: 1 });
discountSchema.index({ createdAt: -1 });

// Add auto-increment plugin
discountSchema.plugin(AutoIncrement, { inc_field: 'discountId', start_seq: 1001 });
discountSchema.index({ discountId: 1 }, { unique: true });

const Discount = mongoose.model('Discount', discountSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Discount.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Discount.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Discount;
