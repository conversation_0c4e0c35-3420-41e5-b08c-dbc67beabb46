import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { api } from "@/services/api";
import { Shield, Users, Settings, Save, RefreshCw } from "lucide-react";
import { Staff, Role, Permission } from "@/store/types";

interface RolePermissionManagerProps {
  staff: Staff;
  onUpdate: (updatedStaff: Staff) => void;
  isCurrentUser?: boolean;
}

interface ExtendedRole extends Role {
  permissions: Permission[];
}

export const RolePermissionManager = ({ staff, onUpdate, isCurrentUser = false }: RolePermissionManagerProps) => {
  const { toast } = useToast();
  const [roles, setRoles] = useState<ExtendedRole[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<number>(staff.roleId || 0);
  const [specialPermissions, setSpecialPermissions] = useState<number[]>(staff.specialPermissions || []);
  const [revokedPermissions, setRevokedPermissions] = useState<number[]>(staff.revokedPermissions || []);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Fetch roles and permissions
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [rolesResponse, permissionsResponse] = await Promise.all([
          api.get('/roles'),
          api.get('/permissions')
        ]);

        if (rolesResponse.data?.success) {
          setRoles(rolesResponse.data.data.data || []);
        }

        if (permissionsResponse.data?.success) {
          setPermissions(permissionsResponse.data.data.data || []);
        }
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch roles and permissions",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Get current role
  const currentRole = roles.find(role => role.roleId === selectedRoleId);

  // Get effective permissions (role permissions + special - revoked)
  const getEffectivePermissions = () => {
    const rolePermissions = currentRole?.permissions || [];
    const effective = new Set([
      ...rolePermissions.map(p => typeof p === 'object' ? p.permissionId : p),
      ...specialPermissions
    ]);
    
    // Remove revoked permissions
    revokedPermissions.forEach(id => effective.delete(id));
    
    return Array.from(effective);
  };

  // Handle role change
  const handleRoleChange = (roleId: string) => {
    const newRoleId = parseInt(roleId);
    setSelectedRoleId(newRoleId);
    
    // Reset special and revoked permissions when role changes
    setSpecialPermissions([]);
    setRevokedPermissions([]);
  };

  // Handle special permission toggle
  const handleSpecialPermissionToggle = (permissionId: number, checked: boolean) => {
    if (checked) {
      setSpecialPermissions(prev => [...prev.filter(id => id !== permissionId), permissionId]);
      setRevokedPermissions(prev => prev.filter(id => id !== permissionId));
    } else {
      setSpecialPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  // Handle revoked permission toggle
  const handleRevokedPermissionToggle = (permissionId: number, checked: boolean) => {
    if (checked) {
      setRevokedPermissions(prev => [...prev.filter(id => id !== permissionId), permissionId]);
      setSpecialPermissions(prev => prev.filter(id => id !== permissionId));
    } else {
      setRevokedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  // Save changes
  const handleSave = async () => {
    setSaving(true);
    try {
      const updateData = {
        roleId: selectedRoleId,
        specialPermissions,
        revokedPermissions
      };

      const response = await api.put(`/staff/${staff.staffId}/profile`, updateData);

      if (response.data?.success) {
        onUpdate(response.data.data);
        toast({
          title: "Success",
          description: "Role and permissions updated successfully",
        });
      } else {
        throw new Error(response.data?.message || "Failed to update");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update role and permissions",
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset to original values
  const handleReset = () => {
    setSelectedRoleId(staff.roleId || 0);
    setSpecialPermissions(staff.specialPermissions || []);
    setRevokedPermissions(staff.revokedPermissions || []);
  };

  const effectivePermissions = getEffectivePermissions();
  const hasChanges = selectedRoleId !== staff.roleId || 
                    JSON.stringify(specialPermissions) !== JSON.stringify(staff.specialPermissions) ||
                    JSON.stringify(revokedPermissions) !== JSON.stringify(staff.revokedPermissions);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            Role & Permissions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            Role & Permissions
          </div>
          {hasChanges && (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleReset}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset
              </Button>
              <Button size="sm" onClick={handleSave} disabled={saving}>
                <Save className="mr-2 h-4 w-4" />
                {saving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Role Selection */}
        <div>
          <label className="text-sm font-medium mb-2 block">Role</label>
          <Select value={selectedRoleId.toString()} onValueChange={handleRoleChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              {roles.map((role) => (
                <SelectItem key={role.roleId} value={role.roleId.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span>{role.name}</span>
                    <Badge variant="outline" className="ml-2">
                      {role.category}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {currentRole && (
            <p className="text-sm text-muted-foreground mt-1">
              {currentRole.description}
            </p>
          )}
        </div>

        {/* Current Effective Permissions */}
        <div>
          <label className="text-sm font-medium mb-2 block">Effective Permissions</label>
          <div className="flex flex-wrap gap-2">
            {effectivePermissions.map((permId) => {
              const permission = permissions.find(p => p.permissionId === permId);
              return permission ? (
                <Badge key={permId} variant="default">
                  {permission.name}
                </Badge>
              ) : null;
            })}
          </div>
        </div>

        {/* Permission Management */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Special Permissions */}
          <div>
            <label className="text-sm font-medium mb-2 block flex items-center">
              <Users className="mr-2 h-4 w-4" />
              Grant Special Permissions
            </label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {permissions.map((permission) => {
                const isRolePermission = currentRole?.permissions?.some(p => 
                  (typeof p === 'object' ? p.permissionId : p) === permission.permissionId
                );
                const isSpecial = specialPermissions.includes(permission.permissionId);
                
                if (isRolePermission) return null; // Don't show role permissions here
                
                return (
                  <div key={permission.permissionId} className="flex items-center space-x-2">
                    <Checkbox
                      id={`special-${permission.permissionId}`}
                      checked={isSpecial}
                      onCheckedChange={(checked) => 
                        handleSpecialPermissionToggle(permission.permissionId, checked as boolean)
                      }
                    />
                    <label 
                      htmlFor={`special-${permission.permissionId}`}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {permission.name}
                      <span className="text-muted-foreground ml-1">({permission.module})</span>
                    </label>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Revoked Permissions */}
          <div>
            <label className="text-sm font-medium mb-2 block flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              Revoke Role Permissions
            </label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {currentRole?.permissions?.map((perm) => {
                const permissionId = typeof perm === 'object' ? perm.permissionId : perm;
                const permission = permissions.find(p => p.permissionId === permissionId);
                const isRevoked = revokedPermissions.includes(permissionId);
                
                if (!permission) return null;
                
                return (
                  <div key={permissionId} className="flex items-center space-x-2">
                    <Checkbox
                      id={`revoke-${permissionId}`}
                      checked={isRevoked}
                      onCheckedChange={(checked) => 
                        handleRevokedPermissionToggle(permissionId, checked as boolean)
                      }
                    />
                    <label 
                      htmlFor={`revoke-${permissionId}`}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {permission.name}
                      <span className="text-muted-foreground ml-1">({permission.module})</span>
                    </label>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
