import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { updateRole, getRole } from "@/services/roles";
import { useToast } from "@/components/ui/use-toast";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { getPermissions } from "@/services/permissions";
import { Permission, Role } from "@/store/types";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  permissions: z.array(z.number()).min(1, "Select at least one permission")
});

type FormValues = z.infer<typeof formSchema>;

const EditRole = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: role } = useQuery<Role>({
    queryKey: ["role", id],
    queryFn: () => getRole(id!),
    enabled: !!id
  });

  const { data: permissions = [] } = useQuery<Permission[]>({
    queryKey: ["permissions"],
    queryFn: async (): Promise<Permission[]> => {
      const response = await getPermissions();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data.data;
    }
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    values: {
      name: role?.name || "",
      description: role?.description || "",
      // permissions: role?.permissions?.map(p => Number(p._id)) || []
    },
  });

  const onSubmit = async (values: FormValues) => {
    if (!id) return;
    
    try {
      await updateRole(id, {
        name: values.name,
        description: values.description,
        permissions: values.permissions as number[]
      });
      await queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: "Success",
        description: "Role updated successfully",
      });
      navigate("/admin/roles");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update role",
        variant: "destructive",
      });
    }
  };

  if (!role) return null;

  return (
    <div className="p-8">
      <Card>
        <CardHeader>
          <CardTitle>Edit Role</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter role name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter role description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="permissions"
                render={() => (
                  <FormItem>
                    <FormLabel>Permissions</FormLabel>
                    <div className="grid grid-cols-2 gap-4">
                      {permissions.map((permission) => (
                        <FormField
                          key={permission._id}
                          control={form.control}
                          name="permissions"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={permission._id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(Number(permission._id))}
                                    onCheckedChange={(checked) => {
                                      const permissionId = Number(permission._id);
                                      return checked
                                        ? field.onChange([...field.value, permissionId])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== permissionId
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {permission.name}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => navigate("/admin/roles")}
                  type="button"
                >
                  Cancel
                </Button>
                <Button type="submit">Update Role</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditRole;