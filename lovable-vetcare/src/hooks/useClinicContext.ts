/**
 * Custom hook for clinic context and API calls with automatic clinicId inclusion
 */

import { useAuth } from '@/store';

export interface ClinicContextParams {
  [key: string]: any;
}

/**
 * Hook that provides clinic context and automatically includes clinicId in API parameters
 */
export const useClinicContext = () => {
  const { clinic, employee } = useAuth();
  
  // Get clinicId from multiple possible sources
  const getClinicId = (): string | number | undefined => {
    return clinic?.clinicId || employee?.primaryClinicId || employee?.clinicId;
  };

  /**
   * Automatically adds clinicId to API parameters
   */
  const withClinicId = (params: ClinicContextParams = {}): ClinicContextParams => {
    const clinicId = getClinicId();
    
    if (clinicId) {
      return {
        ...params,
        clinicId
      };
    }
    
    return params;
  };

  /**
   * Check if user has clinic context
   */
  const hasClinicContext = (): boolean => {
    return !!getClinicId();
  };

  return {
    clinic,
    clinicId: getClinicId(),
    withClinicId,
    hasClinicContext
  };
};
