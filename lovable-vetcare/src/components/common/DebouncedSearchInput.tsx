import { useState, useEffect, useCallback, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X, Loader2 } from "lucide-react";
import { debounce } from "@/lib/debounce";
import { cn } from "@/lib/utils";

interface DebouncedSearchInputProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onSearch: () => void;
  debounceTime?: number;
  className?: string;
  isLoading?: boolean;
  clearable?: boolean;
  variant?: "default" | "filled" | "outline" | "ghost";
  state?: "default" | "error" | "success";
  icon?: React.ReactNode;
  autoFocus?: boolean;
  onClear?: () => void;
}

/**
 * An enhanced search input component that debounces the search value,
 * provides immediate search on Enter key press, and includes loading state,
 * clear button, and customizable styling
 */
const DebouncedSearchInput = ({
  placeholder = "Search...",
  value,
  onChange,
  onSearch,
  debounceTime = 500,
  className = "",
  isLoading = false,
  clearable = true,
  variant = "default",
  state = "default",
  icon,
  autoFocus = false,
  onClear,
}: DebouncedSearchInputProps) => {
  const [inputValue, setInputValue] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update local state when prop value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Auto-focus if enabled
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Create debounced handler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnChange = useCallback(
    debounce((newValue: string) => {
      onChange(newValue);
      if (newValue.trim()) {
        onSearch();
      }
    }, debounceTime),
    [onChange, onSearch, debounceTime]
  );

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    debouncedOnChange(newValue);
  };

  // Handle key press (for Enter key)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onChange(inputValue); // Update immediately
      if (inputValue.trim()) {
        onSearch(); // Trigger search
      }
    } else if (e.key === "Escape") {
      // Clear input on Escape
      if (inputValue) {
        handleClear();
      } else {
        inputRef.current?.blur();
      }
    }
  };

  const handleClear = () => {
    setInputValue("");
    onChange("");
    if (onClear) {
      onClear();
    }
    inputRef.current?.focus();
  };

  return (
    <div className={cn("relative flex items-center", className)}>
      <div className={cn("relative flex-grow group", isFocused && "z-10")}>
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            icon || <Search className="h-4 w-4" />
          )}
        </div>
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            "pl-10",
            clearable && inputValue && "pr-10"
          )}
          variant={variant}
          state={state}
          disabled={isLoading}
        />
        {clearable && inputValue && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      <Button
        variant="outline"
        size="icon"
        className="ml-2"
        onClick={() => {
          onChange(inputValue); // Update immediately
          if (inputValue.trim()) {
            onSearch(); // Trigger search
          }
        }}
        disabled={isLoading || !inputValue.trim()}
      >
        <Search className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default DebouncedSearchInput;
