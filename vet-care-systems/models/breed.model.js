import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const breedSchema = new mongoose.Schema({
    // Auto-increment ID
    breedId: {
        type: Number,
        index: true,
        unique: true,
    },
    speciesId: {
        type: Number,
        ref: 'Species',
        required: true,
        index: true,
    },
    breedName: {
        type: String,
        required: [true, "Breed name is required"],
        trim: true,
    },
    commonColour: {
        type: String,
        trim: true,
    },
    origin: {
        type: String,
        trim: true,
    },
    sizeCategory: {
        type: String,
        enum: ["small", "medium", "large"],
    },
    lifespan: {
        type: Number,
        min: 1,
    },
    temperament: {
        type: String,
        trim: true,
    },
}, {timestamps: true});

// Add auto-increment plugin
breedSchema.plugin(AutoIncrement, { inc_field: 'breedId', start_seq: 1001 });
breedSchema.index({ breedId: 1 }, { unique: true });

const Breed = mongoose.model('Breed', breedSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Breed.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Breed.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Breed;