import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const appointmentNoteSchema = new mongoose.Schema({
    // Auto-increment ID
    noteId: {
        type: Number,
        index: true,
        unique: true,
    },
    appointmentId: {
        type: Number,
        required: true,
        index: true,
    },
    staffId: {
        type: Number,
        required: true,
        index: true,
    },
    noteType: {
        type: String,
        required: true,
        enum: [
            'general',
            'pre_visit',
            'during_visit', 
            'post_visit',
            'lab_results',
            'grooming_notes',
            'boarding_notes',
            'surgery_notes',
            'vaccination_notes',
            'dental_notes',
            'emergency_notes',
            'follow_up',
            'medication_notes',
            'behavioral_notes',
            'nutrition_notes',
            'imaging_notes',
            'therapy_notes'
        ],
        default: 'general'
    },
    category: {
        type: String,
        required: true,
        enum: [
            'consultation',
            'laboratory',
            'surgery',
            'grooming',
            'boarding',
            'vaccination',
            'dental',
            'emergency',
            'wellness',
            'follow_up',
            'imaging',
            'therapy',
            'nutrition',
            'behavioral',
            'other'
        ]
    },
    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    content: {
        type: String,
        required: true,
        trim: true,
        maxlength: 5000
    },
    priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal'
    },
    isPrivate: {
        type: Boolean,
        default: false
    },
    tags: [{
        type: String,
        trim: true,
        maxlength: 50
    }],
    attachments: [{
        fileName: String,
        fileUrl: String,
        fileType: String,
        fileSize: Number,
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    relatedTo: {
        healthRecordId: Number,
        invoiceId: Number,
        prescriptionId: Number
    },
    visibility: {
        type: String,
        enum: ['all_staff', 'assigned_staff', 'clinic_owner', 'specific_roles'],
        default: 'all_staff'
    },
    visibleToRoles: [{
        type: Number // Role IDs that can see this note
    }],
    isEditable: {
        type: Boolean,
        default: true
    },
    editHistory: [{
        editedBy: Number,
        editedAt: {
            type: Date,
            default: Date.now
        },
        changes: String,
        previousContent: String
    }],
    status: {
        type: String,
        enum: ['draft', 'published', 'archived'],
        default: 'published'
    }
}, { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
appointmentNoteSchema.plugin(AutoIncrement, { inc_field: 'noteId', start_seq: 1001 });
appointmentNoteSchema.index({ noteId: 1 }, { unique: true });

// Indexes for efficient queries
appointmentNoteSchema.index({ appointmentId: 1, noteType: 1 });
appointmentNoteSchema.index({ staffId: 1, createdAt: -1 });
appointmentNoteSchema.index({ category: 1, priority: 1 });
appointmentNoteSchema.index({ tags: 1 });
appointmentNoteSchema.index({ status: 1, visibility: 1 });

// Virtual for staff data
appointmentNoteSchema.virtual('staffData', {
    ref: 'Staff',
    localField: 'staffId',
    foreignField: 'staffId',
    justOne: true
});

// Virtual for appointment data
appointmentNoteSchema.virtual('appointmentData', {
    ref: 'Appointment',
    localField: 'appointmentId',
    foreignField: 'appointmentId',
    justOne: true
});

// Pre-save middleware to update edit history
appointmentNoteSchema.pre('findOneAndUpdate', function() {
    const update = this.getUpdate();
    if (update.content || update.title) {
        // Add to edit history if content or title is being updated
        const editEntry = {
            editedBy: update.editedBy || this.getOptions().context?.userId,
            editedAt: new Date(),
            changes: 'Content or title updated',
            previousContent: this.getQuery().content
        };
        
        if (!update.$push) update.$push = {};
        update.$push.editHistory = editEntry;
    }
});

const AppointmentNote = mongoose.model('AppointmentNote', appointmentNoteSchema);

export default AppointmentNote;
