import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    registerPet,
    getAllPets,
    getPetsByQuery,
    getPet,
    updatePet,
    deletePet,
} from "../controllers/pet.controller.js";

const petRouter = Router();

// Protected routes
petRouter.use(verifyToken);

// Base routes
petRouter.post('/', registerPet);
petRouter.get('/', getAllPets);

// Search routes
petRouter.get('/search', getPetsByQuery);

// Individual pet routes
petRouter.get('/:petId', getPet);
petRouter.put('/:petId', updatePet);
petRouter.delete('/:petId', deletePet);

export default petRouter;