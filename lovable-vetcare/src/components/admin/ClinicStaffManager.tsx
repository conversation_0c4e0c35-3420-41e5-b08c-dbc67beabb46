import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Users,
  UserPlus,
  UserMinus,
  Crown,
  Search,
  ArrowRight,
  ArrowLeft,
  Check,
  X,
  RefreshCw
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { api } from "@/services/api";
import { getClinics } from "@/services/clinics";
import { getClinicOwners } from "@/services/staff";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface ClinicStaffManagerProps {
  onClose?: () => void;
}

export const ClinicStaffManager = ({ onClose }: ClinicStaffManagerProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("find-owner");
  const [selectedClinic, setSelectedClinic] = useState<any>(null);
  const [selectedOwner, setSelectedOwner] = useState<any>(null);
  const [selectedStaff, setSelectedStaff] = useState<any[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch clinics
  const { data: clinicsData } = useQuery({
    queryKey: ["clinics"],
    queryFn: async () => {
      const response = await getClinics();
      return response.data?.data || [];
    }
  });

  // Fetch staff
  const { data: staffData } = useQuery({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await api.get('/staff');
      return response.data?.data?.data || [];
    }
  });

  // Fetch roles
  const { data: rolesData } = useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await api.get('/roles');
      return response.data?.data?.data || [];
    }
  });

  // Fetch clinic owners for assignment
  const { data: clinicOwnersData } = useQuery({
    queryKey: ["clinic-owners", searchQuery],
    queryFn: async () => {
      const response = await getClinicOwners({
        search: searchQuery,
        hasClinic: false, // Get owners without assigned clinics
        limit: 50
      });
      return response.data?.data || [];
    }
  });

  // Assign clinic owner mutation
  const assignOwnerMutation = useMutation({
    mutationFn: async ({ clinicId, ownerId }: { clinicId: number; ownerId: number }) => {
      return await api.put(`/clinics/${clinicId}/assign-owner`, { ownerId });
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Clinic owner assigned successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["clinics"] });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      resetForm();
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to assign clinic owner",
      });
    },
  });

  // Assign staff to clinic mutation
  const assignStaffMutation = useMutation({
    mutationFn: async ({ clinicId, staffIds, roleId }: { clinicId: number; staffIds: number[]; roleId?: number }) => {
      return await api.put(`/clinics/${clinicId}/assign-staff`, { staffIds, roleId });
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Staff assigned to clinic successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["clinics"] });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      resetForm();
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to assign staff to clinic",
      });
    },
  });

  // Remove staff from clinic mutation
  const removeStaffMutation = useMutation({
    mutationFn: async ({ clinicId, staffIds, newClinicId }: { clinicId: number; staffIds: number[]; newClinicId?: number }) => {
      return await api.put(`/clinics/${clinicId}/remove-staff`, { staffIds, newClinicId });
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Staff removed from clinic successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["clinics"] });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      resetForm();
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to remove staff from clinic",
      });
    },
  });

  const resetForm = () => {
    setSelectedClinic(null);
    setSelectedOwner(null);
    setSelectedStaff([]);
    setSelectedRole("");
    setSearchQuery("");
  };

  const handleAssignOwner = () => {
    if (!selectedClinic || !selectedOwner) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select both clinic and owner",
      });
      return;
    }

    assignOwnerMutation.mutate({
      clinicId: selectedClinic.clinicId,
      ownerId: selectedOwner.staffId
    });
  };

  const handleAssignStaff = () => {
    if (!selectedClinic || selectedStaff.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select clinic and at least one staff member",
      });
      return;
    }

    assignStaffMutation.mutate({
      clinicId: selectedClinic.clinicId,
      staffIds: selectedStaff.map(s => s.staffId),
      roleId: selectedRole ? parseInt(selectedRole) : undefined
    });
  };

  const handleRemoveStaff = () => {
    if (!selectedClinic || selectedStaff.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select clinic and at least one staff member",
      });
      return;
    }

    removeStaffMutation.mutate({
      clinicId: selectedClinic.clinicId,
      staffIds: selectedStaff.map(s => s.staffId)
    });
  };

  const filteredStaff = staffData?.filter((staff: any) =>
    staff.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.email?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const availableStaff = filteredStaff.filter((staff: any) =>
    !selectedClinic || staff.clinicId !== selectedClinic.clinicId
  );

  const clinicStaff = filteredStaff.filter((staff: any) =>
    selectedClinic && staff.clinicId === selectedClinic.clinicId
  );

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Clinic-Staff Manager</h1>
          <p className="text-gray-600 mt-1">Assign owners and staff to clinics</p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            <X className="mr-2 h-4 w-4" />
            Close
          </Button>
        )}
      </div>

      {/* Clinic Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building2 className="mr-2 h-5 w-5" />
            Select Clinic
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedClinic?.clinicId?.toString() || ""}
            onValueChange={(value) => {
              const clinic = clinicsData?.find((c: any) => c.clinicId.toString() === value);
              setSelectedClinic(clinic);
              setSelectedOwner(null);
              setSelectedStaff([]);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a clinic to manage" />
            </SelectTrigger>
            <SelectContent>
              {clinicsData?.map((clinic: any) => (
                <SelectItem key={clinic.clinicId} value={clinic.clinicId.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span>{clinic.clinicName}</span>
                    {clinic.ownerInfo && (
                      <Badge variant="outline" className="ml-2">
                        Owner: {clinic.ownerInfo.firstName} {clinic.ownerInfo.lastName}
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedClinic && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="find-owner">Find Owner</TabsTrigger>
              <TabsTrigger value="assign-owner">Assign Owner</TabsTrigger>
              <TabsTrigger value="assign-staff">Assign Staff</TabsTrigger>
              <TabsTrigger value="remove-staff">Remove Staff</TabsTrigger>
            </TabsList>

            <TabsContent value="find-owner" className="mt-6">
              <FindOwnerTab
                clinicOwnersData={clinicOwnersData}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                selectedOwner={selectedOwner}
                setSelectedOwner={setSelectedOwner}
                selectedClinic={selectedClinic}
                onAssign={handleAssignOwner}
                loading={assignOwnerMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="assign-owner" className="mt-6">
              <AssignOwnerTab
                selectedClinic={selectedClinic}
                selectedOwner={selectedOwner}
                setSelectedOwner={setSelectedOwner}
                availableStaff={availableStaff}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                onAssign={handleAssignOwner}
                loading={assignOwnerMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="assign-staff" className="mt-6">
              <AssignStaffTab
                selectedClinic={selectedClinic}
                selectedStaff={selectedStaff}
                setSelectedStaff={setSelectedStaff}
                selectedRole={selectedRole}
                setSelectedRole={setSelectedRole}
                availableStaff={availableStaff}
                rolesData={rolesData}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                onAssign={handleAssignStaff}
                loading={assignStaffMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="remove-staff" className="mt-6">
              <RemoveStaffTab
                selectedClinic={selectedClinic}
                selectedStaff={selectedStaff}
                setSelectedStaff={setSelectedStaff}
                clinicStaff={clinicStaff}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                onRemove={handleRemoveStaff}
                loading={removeStaffMutation.isPending}
              />
            </TabsContent>
          </Tabs>
        </motion.div>
      )}
    </div>
  );
};

// Tab Components
const FindOwnerTab = ({
  clinicOwnersData,
  searchQuery,
  setSearchQuery,
  selectedOwner,
  setSelectedOwner,
  selectedClinic,
  onAssign,
  loading
}: any) => (
  <div className="space-y-6">
    {/* Instructions Card */}
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center text-blue-900">
          <Search className="mr-2 h-5 w-5" />
          Find & Assign Clinic Owner
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-blue-800">
            {selectedClinic
              ? `Search for an available clinic owner to assign to "${selectedClinic.clinicName}"`
              : "First select a clinic above, then search for an available clinic owner to assign"
            }
          </p>
          <div className="flex items-center space-x-4 text-sm text-blue-700">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              Available for assignment
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              Already has a clinic
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    {/* Search Card */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Search className="mr-2 h-5 w-5" />
          Search Clinic Owners
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="search" className="text-base font-medium">Search by Name or Phone Number</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              id="search"
              placeholder="Type name or phone number to search clinic owners..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 h-12 text-base border-2 focus:border-primary"
            />
          </div>
          {searchQuery && (
            <p className="text-sm text-gray-600">
              Searching for: <span className="font-medium">"{searchQuery}"</span>
            </p>
          )}
        </div>
      </CardContent>
    </Card>

    {/* Results Card */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Available Clinic Owners
          </span>
          <Badge variant="outline" className="text-sm">
            {clinicOwnersData?.length || 0} found
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {clinicOwnersData?.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'No clinic owners found' : 'No available clinic owners'}
              </h3>
              <p className="text-gray-500">
                {searchQuery
                  ? `No clinic owners match "${searchQuery}". Try a different search term.`
                  : 'All clinic owners are already assigned to clinics.'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {clinicOwnersData?.map((owner: any) => (
                <motion.div
                  key={owner.staffId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                  className={`p-5 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                    selectedOwner?.staffId === owner.staffId
                      ? 'border-primary bg-primary/5 shadow-lg'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedOwner(owner)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h4 className="font-semibold text-lg text-gray-900">
                          {owner.fullName || `${owner.firstName} ${owner.lastName}`}
                        </h4>
                        {selectedOwner?.staffId === owner.staffId && (
                          <Check className="h-5 w-5 text-primary ml-2" />
                        )}
                      </div>

                      <div className="space-y-1 mb-3">
                        <p className="text-sm text-gray-600 flex items-center">
                          <span className="w-16 text-gray-500">Email:</span>
                          {owner.email}
                        </p>
                        {owner.phoneNumber && (
                          <p className="text-sm text-gray-600 flex items-center">
                            <span className="w-16 text-gray-500">Phone:</span>
                            {owner.phoneNumber}
                          </p>
                        )}
                        <p className="text-sm text-gray-600 flex items-center">
                          <span className="w-16 text-gray-500">Title:</span>
                          {owner.jobTitle}
                        </p>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {owner.clinic ? (
                          <>
                            <Badge variant="outline" className="text-xs">
                              Current: {owner.clinic.clinicName}
                            </Badge>
                            {!owner.availableForAssignment && (
                              <Badge variant="secondary" className="text-xs">
                                <Crown className="mr-1 h-3 w-3" />
                                Already Owner
                              </Badge>
                            )}
                          </>
                        ) : (
                          <Badge variant="default" className="text-xs bg-green-100 text-green-800 border-green-300">
                            ✓ Available for Assignment
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>

    {/* Assignment Preview & Action */}
    {selectedClinic && selectedOwner && (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center text-green-900">
              <Crown className="mr-2 h-5 w-5" />
              Ready to Assign
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">Assignment Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Clinic:</span>
                    <span className="font-medium">{selectedClinic.clinicName}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">New Owner:</span>
                    <span className="font-medium">{selectedOwner.fullName || `${selectedOwner.firstName} ${selectedOwner.lastName}`}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Owner Email:</span>
                    <span className="font-medium">{selectedOwner.email}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Title:</span>
                    <span className="font-medium">{selectedOwner.jobTitle}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedOwner(null);
                  }}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={onAssign}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700 text-white px-6"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Assigning...
                    </>
                  ) : (
                    <>
                      <Crown className="mr-2 h-4 w-4" />
                      Assign as Owner
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )}

    {/* No Selection State */}
    {!selectedClinic && (
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="text-center py-8">
          <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Clinic First</h3>
          <p className="text-gray-500">
            Choose a clinic from the dropdown above to start searching for clinic owners.
          </p>
        </CardContent>
      </Card>
    )}
  </div>
);

const AssignOwnerTab = ({
  selectedClinic,
  selectedOwner,
  setSelectedOwner,
  availableStaff,
  searchQuery,
  setSearchQuery,
  onAssign,
  loading
}: any) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <Crown className="mr-2 h-5 w-5" />
        Assign Owner to {selectedClinic.clinicName}
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="search">Search Staff</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="search"
            placeholder="Search by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Select New Owner</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto">
          {availableStaff.map((staff: any) => (
            <div
              key={staff.staffId}
              className={`p-3 border rounded-lg cursor-pointer transition-all ${
                selectedOwner?.staffId === staff.staffId
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedOwner(staff)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{staff.firstName} {staff.lastName}</h4>
                  <p className="text-sm text-gray-600">{staff.email}</p>
                  <p className="text-sm text-gray-500">{staff.jobTitle}</p>
                </div>
                {selectedOwner?.staffId === staff.staffId && (
                  <Check className="h-5 w-5 text-primary" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={onAssign}
          disabled={!selectedOwner || loading}
          className="flex items-center"
        >
          {loading ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Crown className="mr-2 h-4 w-4" />
          )}
          {loading ? 'Assigning...' : 'Assign as Owner'}
        </Button>
      </div>
    </CardContent>
  </Card>
);

const AssignStaffTab = ({
  selectedClinic,
  selectedStaff,
  setSelectedStaff,
  selectedRole,
  setSelectedRole,
  availableStaff,
  rolesData,
  searchQuery,
  setSearchQuery,
  onAssign,
  loading
}: any) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <UserPlus className="mr-2 h-5 w-5" />
        Assign Staff to {selectedClinic.clinicName}
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="search">Search Staff</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Search by name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Assign Role (Optional)</Label>
          <Select value={selectedRole} onValueChange={setSelectedRole}>
            <SelectTrigger>
              <SelectValue placeholder="Select role for assigned staff" />
            </SelectTrigger>
            <SelectContent>
              {rolesData?.map((role: any) => (
                <SelectItem key={role.roleId} value={role.roleId.toString()}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Select Staff Members ({selectedStaff.length} selected)</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto">
          {availableStaff.map((staff: any) => {
            const isSelected = selectedStaff.some((s: any) => s.staffId === staff.staffId);
            return (
              <div
                key={staff.staffId}
                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                  isSelected
                    ? 'border-primary bg-primary/5'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => {
                  if (isSelected) {
                    setSelectedStaff(selectedStaff.filter((s: any) => s.staffId !== staff.staffId));
                  } else {
                    setSelectedStaff([...selectedStaff, staff]);
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{staff.firstName} {staff.lastName}</h4>
                    <p className="text-sm text-gray-600">{staff.email}</p>
                    <p className="text-sm text-gray-500">{staff.jobTitle}</p>
                  </div>
                  <Checkbox checked={isSelected} readOnly />
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={onAssign}
          disabled={selectedStaff.length === 0 || loading}
          className="flex items-center"
        >
          {loading ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <UserPlus className="mr-2 h-4 w-4" />
          )}
          {loading ? 'Assigning...' : `Assign ${selectedStaff.length} Staff`}
        </Button>
      </div>
    </CardContent>
  </Card>
);

const RemoveStaffTab = ({
  selectedClinic,
  selectedStaff,
  setSelectedStaff,
  clinicStaff,
  searchQuery,
  setSearchQuery,
  onRemove,
  loading
}: any) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center">
        <UserMinus className="mr-2 h-5 w-5" />
        Remove Staff from {selectedClinic.clinicName}
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="search">Search Current Staff</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="search"
            placeholder="Search by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Current Staff Members ({selectedStaff.length} selected for removal)</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-64 overflow-y-auto">
          {clinicStaff.map((staff: any) => {
            const isSelected = selectedStaff.some((s: any) => s.staffId === staff.staffId);
            const isOwner = staff.isClinicOwner && selectedClinic.owner === staff.staffId;

            return (
              <div
                key={staff.staffId}
                className={`p-3 border rounded-lg transition-all ${
                  isOwner
                    ? 'border-yellow-300 bg-yellow-50 cursor-not-allowed'
                    : isSelected
                    ? 'border-red-300 bg-red-50 cursor-pointer'
                    : 'border-gray-200 hover:border-gray-300 cursor-pointer'
                }`}
                onClick={() => {
                  if (isOwner) return;

                  if (isSelected) {
                    setSelectedStaff(selectedStaff.filter((s: any) => s.staffId !== staff.staffId));
                  } else {
                    setSelectedStaff([...selectedStaff, staff]);
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{staff.firstName} {staff.lastName}</h4>
                    <p className="text-sm text-gray-600">{staff.email}</p>
                    <p className="text-sm text-gray-500">{staff.jobTitle}</p>
                    {isOwner && (
                      <Badge variant="secondary" className="mt-1">
                        <Crown className="mr-1 h-3 w-3" />
                        Clinic Owner
                      </Badge>
                    )}
                  </div>
                  {!isOwner && <Checkbox checked={isSelected} readOnly />}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {clinicStaff.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No staff members found in this clinic
        </div>
      )}

      <div className="flex justify-end">
        <Button
          onClick={onRemove}
          disabled={selectedStaff.length === 0 || loading}
          variant="destructive"
          className="flex items-center"
        >
          {loading ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <UserMinus className="mr-2 h-4 w-4" />
          )}
          {loading ? 'Removing...' : `Remove ${selectedStaff.length} Staff`}
        </Button>
      </div>
    </CardContent>
  </Card>
);
