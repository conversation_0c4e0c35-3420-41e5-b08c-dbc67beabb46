import Clinic from "../models/clinic.model.js";
import Staff from "../models/staff.model.js";
import Role from "../models/role.model.js";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import mongoose from "mongoose";
import { JWT_SECRET, JWT_EXPIRES_IN } from "../config/env.js";
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import User from "../models/user.model.js";

export const createClinic = async (req, res) => {
    try {
        const {
            name,
            ownerId,
            phoneNumber,
            email,
            address,
            location,
            operatingHours
        } = req.body;

        const owner = await Staff.findOne({ staffId: parseInt(ownerId) }).lean();
        if (!owner) {
            return sendResponse(res, 404, false, "Owner not found");
        }

        if (!owner.isClinicOwner) {
            return sendResponse(res, 403, false, "User is not authorized to own a clinic");
        }

        const clinic = await Clinic.create({
            clinicName: name,
            ownerId: owner.staffId,
            phoneNumber,
            email,
            address,
            location: location || { type: 'Point', coordinates: [0, 0] },
            operatingHours,
            status: 1
        });

        return sendResponse(res, 201, true, "Clinic created successfully", clinic);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const searchNearbyClinic = async (req, res) => {
    try {
        const {
            latitude,
            longitude,
            radius = 5000, // Default 5km
            page = 1,
            limit = 10
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const query = {
            location: {
                $near: {
                    $geometry: {
                        type: 'Point',
                        coordinates: [parseFloat(longitude), parseFloat(latitude)]
                    },
                    $maxDistance: parseInt(radius)
                }
            },
            status: 1
        };

        const [clinics, totalCount] = await Promise.all([
            Clinic.find(query)
                .populate('ownerId', 'firstName middleName lastName email -_id')
                .select('-__v')
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Clinic.countDocuments(query)
        ]);

        return sendResponse(res, 200, true, "Nearby clinics found successfully", {
            data: clinics,
            pagination: {
                totalCount: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit),
                totalPages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllClinics = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "name",
            sortOrder = "asc",
            search,
            status,
            ownerId
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status !== undefined) query.status = parseInt(status);

        // Filter by owner if ownerId is provided
        if (ownerId) {
            query.owner = parseInt(ownerId);
        }

        if (search) {
            query.$or = [
                { clinicName: { $regex: search, $options: "i" } },
                { email: { $regex: search, $options: "i" } },
                { phoneNumber: { $regex: search, $options: "i" } },
                { address: { $regex: search, $options: "i" } }
            ];
        }

        const [clinics, totalCount] = await Promise.all([
            Clinic.find(query)
                .select('-__v')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Clinic.countDocuments(query)
        ]);

        // Populate owner information for each clinic
        const clinicsWithOwners = await Promise.all(
            clinics.map(async (clinic) => {
                if (clinic.owner) {
                    const owner = await Staff.findOne({ staffId: clinic.owner })
                        .select('staffId firstName lastName email phoneNumber jobTitle')
                        .lean();
                    return { ...clinic, ownerInfo: owner };
                }
                return clinic;
            })
        );

        return sendResponse(
            res,
            200,
            true,
            "Clinics retrieved successfully",
            paginateResults(clinicsWithOwners, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getClinicById = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Use numeric clinicId only
        const query = { clinicId: parseInt(clinicId) };

        const clinic = await Clinic.findOne(query)
            .lean();

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic found successfully", clinic);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        const clinic = await Clinic.findOneAndUpdate(
            query,
            { $set: req.body },
            { new: true, runValidators: true }
        );

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic updated successfully", clinic);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update clinic profile (comprehensive clinic management)
 */
export const updateClinicProfile = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const updateData = req.body;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        // Verify clinic exists
        const existingClinic = await Clinic.findOne(query);
        if (!existingClinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // If owner is being updated, validate the staff member exists
        if (updateData.owner) {
            const staff = await Staff.findOne({ staffId: updateData.owner });
            if (!staff) {
                return sendResponse(res, 404, false, "Invalid owner specified");
            }
        }

        // Update the clinic
        const updatedClinic = await Clinic.findOneAndUpdate(
            query,
            { $set: updateData },
            { new: true, runValidators: true }
        ).lean();

        // Get owner and staff information
        const [owner, staffMembers] = await Promise.all([
            updatedClinic.owner ? Staff.findOne({ staffId: updatedClinic.owner })
                .select('staffId firstName lastName email phoneNumber jobTitle')
                .lean() : null,
            Staff.find({ clinicId: updatedClinic.clinicId })
                .select('staffId firstName lastName email phoneNumber jobTitle roleId')
                .lean()
        ]);

        // Get roles for staff members
        const roleIds = [...new Set(staffMembers.map(s => s.roleId))];
        const roles = await Role.find({ roleId: { $in: roleIds } }).lean();
        const roleMap = roles.reduce((map, role) => {
            map[role.roleId] = role;
            return map;
        }, {});

        // Populate staff with role information
        const populatedStaff = staffMembers.map(staff => ({
            ...staff,
            role: roleMap[staff.roleId] ? {
                roleId: roleMap[staff.roleId].roleId,
                roleName: roleMap[staff.roleId].name || roleMap[staff.roleId].roleName,
                description: roleMap[staff.roleId].description
            } : null
        }));

        // Prepare response data
        const responseData = {
            ...updatedClinic,
            owner: owner,
            staff: populatedStaff,
            staffCount: populatedStaff.length
        };

        return sendResponse(res, 200, true, "Clinic profile updated successfully", responseData);
    } catch (error) {
        console.error("Update clinic profile error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const deleteClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        const clinic = await Clinic.findOneAndDelete(query);

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Switch the current clinic for a user
 * This allows staff members who work at multiple clinics to switch between them
 */
/**
 * Admin endpoint to register a clinic owner with default password
 * This creates both the clinic and the clinic owner staff member
 */
export const registerClinicOwner = async (req, res) => {
    const {
        // Owner details
        firstName,
        middleName,
        lastName,
        email,
        phoneNumber,
        address,
        dob,
        jobTitle,
        employmentDate,
        salary,
        emergencyContact,
        schedule,
        // Clinic details
        clinicName,
        clinicPhoneNumber,
        clinicEmail,
        clinicAddress,
        location,
        operatingHours
    } = req.body;

    let session = null;
    try {
        session = await mongoose.startSession();
        session.startTransaction();

        // Check if staff already exists
        const existingStaff = await Staff.findOne({ email }).session(session);
        if (existingStaff) {
            return sendResponse(res, 409, false, "Staff with this email already exists");
        }

        // Get the clinic admin role (roleId: 1002)
        const clinicOwnerRole = await Role.findOne({ roleId: 1002 }).session(session);
        if (!clinicOwnerRole) {
            return sendResponse(res, 404, false, "Clinic admin role not found");
        }

        // Hash the default password
        const defaultPassword = 'pass123';
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(defaultPassword, salt);

        // Create the clinic first
        const [newClinic] = await Clinic.create([{
            clinicName: clinicName,
            phoneNumber: clinicPhoneNumber || phoneNumber,
            email: clinicEmail || email,
            address: clinicAddress || address,
            location: location || { type: 'Point', coordinates: [0, 0] },
            operatingHours: operatingHours || {
                monday: { open: '08:00', close: '18:00' },
                tuesday: { open: '08:00', close: '18:00' },
                wednesday: { open: '08:00', close: '18:00' },
                thursday: { open: '08:00', close: '18:00' },
                friday: { open: '08:00', close: '18:00' },
                saturday: { open: '08:00', close: '16:00' },
                sunday: { closed: true }
            },
            status: 1
        }], { session });

        // Create the clinic owner staff member
        const staffData = {
            firstName,
            middleName: middleName || '',
            lastName,
            email,
            password: hashedPassword,
            phoneNumber,
            address: address || '',
            dob: dob ? new Date(dob) : null,
            clinicId: newClinic.clinicId,
            primaryClinicId: newClinic.clinicId,
            isClinicOwner: true,
            roleId: clinicOwnerRole.roleId,
            permissions: clinicOwnerRole.permissions,
            jobTitle: jobTitle || 'Clinic Owner',
            employmentDate: employmentDate ? new Date(employmentDate) : new Date(),
            salary: salary || 0,
            emergencyContact: emergencyContact || {
                name: '',
                relationship: '',
                phoneNumber: ''
            },
            schedule: schedule || {
                workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                workHours: { start: '08:00', end: '18:00' }
            },
            status: 1,
            lastLogin: null,
            loginCount: 0,
            clinicActivity: [{
                clinicId: newClinic.clinicId,
                lastActive: new Date(),
                activityCount: 1
            }],
            currentClinicId: newClinic.clinicId
        };

        const [newStaff] = await Staff.create([staffData], { session });

        // Update the clinic with the owner information
        await Clinic.findOneAndUpdate(
            { clinicId: newClinic.clinicId },
            { owner: newStaff.staffId },
            { session }
        );

        // Prepare response data
        const staffWithoutPassword = { ...newStaff._doc };
        delete staffWithoutPassword.password;

        const responseData = {
            clinic: {
                clinicId: newClinic.clinicId,
                clinicName: newClinic.clinicName,
                phoneNumber: newClinic.phoneNumber,
                email: newClinic.email,
                address: newClinic.address,
                status: newClinic.status
            },
            owner: staffWithoutPassword,
            credentials: {
                email: email,
                password: defaultPassword,
                message: "Default password is 'pass123'. Owner should change it on first login."
            }
        };

        await session.commitTransaction();
        return sendResponse(res, 201, true, "Clinic and owner registered successfully", responseData);
    } catch (error) {
        if (session?.inTransaction()) {
            await session.abortTransaction();
        }
        console.error("Register clinic owner error:", error);
        return sendResponse(res, 500, false, error.message);
    } finally {
        if (session) {
            session.endSession();
        }
    }
};

export const switchClinic = async (req, res) => {
    try {
        const { clinicId } = req.body;
        const userId = req.user.userId || 0;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = { status: 1 };
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        // Verify the clinic exists and is active
        const clinic = await Clinic.findOne(query).lean();
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found or inactive");
        }

        // Check if we have a staffId in the request
        if (req.user.staffId) {
            // This is a staff member
            const staff = await Staff.findOne({ staffId: parseInt(req.user.staffId) }).lean();
            if (!staff) {
                return sendResponse(res, 404, false, "Staff not found");
            }

            // Update the staff's current clinic
            await Staff.findOneAndUpdate({ staffId: parseInt(req.user.staffId) }, { currentClinicId: clinicId });
        } else {
            // This is a regular user
            const user = await Staff.findOne({ staffId: parseInt(req.user.staffId) }).lean();
            if (!user) {
                return sendResponse(res, 404, false, "User not found");
            }

            // Update the user's current clinic
            await Staff.findOneAndUpdate({ staffId: parseInt(req.user.staffId) }, { currentClinicId: clinicId });
        }

        // Authorization check will be reimplemented later
        // For now, all authenticated users can access any clinic

        // Current clinic already updated above

        // Generate a new token with the clinic info
        let tokenPayload = {
            userId: userId,
            currentClinicId: clinicId
        };

        // Include staffId in the token if it exists
        if (req.user.staffId) {
            tokenPayload.staffId = req.user.staffId;
        }

        const token = jwt.sign(
            tokenPayload,
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // Get the full clinic details to return
        const clinicDetails = await Clinic.findOne(query)
            .select('clinicId clinicName address phoneNumber email status')
            .lean();

        return sendResponse(res, 200, true, "Clinic switched successfully", {
            token,
            currentClinic: clinicDetails
        });
    } catch (error) {
        console.error("Error switching clinic:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Admin endpoint to assign/attach a clinic to an existing owner
 * This updates the clinic's owner field
 */
export const assignClinicOwner = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { ownerId } = req.body;

        // Validate input
        if (!ownerId) {
            return sendResponse(res, 400, false, "Owner ID is required");
        }

        // Use numeric clinicId only
        const clinicQuery = { clinicId: parseInt(clinicId) };

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify staff member exists and can be an owner
        const staff = await Staff.findOne({ staffId: parseInt(ownerId) });
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Update clinic with new owner
        const updatedClinic = await Clinic.findOneAndUpdate(
            clinicQuery,
            {
                owner: parseInt(ownerId),
                updatedAt: new Date()
            },
            { new: true, runValidators: true }
        ).lean();

        // Update staff member to be clinic owner if not already
        await Staff.findOneAndUpdate(
            { staffId: parseInt(ownerId) },
            {
                isClinicOwner: true,
                clinicId: updatedClinic.clinicId,
                primaryClinicId: updatedClinic.clinicId,
                updatedAt: new Date()
            }
        );

        // Get updated owner information
        const ownerInfo = await Staff.findOne({ staffId: parseInt(ownerId) })
            .select('staffId firstName lastName email phoneNumber jobTitle isClinicOwner')
            .lean();

        const responseData = {
            clinic: {
                clinicId: updatedClinic.clinicId,
                clinicName: updatedClinic.clinicName,
                owner: updatedClinic.owner
            },
            owner: ownerInfo
        };

        return sendResponse(res, 200, true, "Clinic owner assigned successfully", responseData);
    } catch (error) {
        console.error("Assign clinic owner error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Clinic owner endpoint to assign a clinic manager
 * This updates the clinic's manager field
 */
export const assignClinicManager = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { managerId } = req.body;

        // Validate input
        if (!managerId) {
            return sendResponse(res, 400, false, "Manager ID is required");
        }

        // Use numeric clinicId only
        const clinicQuery = { clinicId: parseInt(clinicId) };

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify staff member exists and belongs to this clinic
        const staff = await Staff.findOne({
            staffId: parseInt(managerId),
            clinicId: parseInt(clinicId)
        });
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found in this clinic");
        }

        // Update clinic with new manager
        const updatedClinic = await Clinic.findOneAndUpdate(
            clinicQuery,
            {
                managerId: parseInt(managerId),
                updatedAt: new Date()
            },
            { new: true, runValidators: true }
        ).lean();

        // Update staff member role to manager if needed
        await Staff.findOneAndUpdate(
            { staffId: parseInt(managerId) },
            {
                isManager: true,
                updatedAt: new Date()
            }
        );

        // Get updated manager information
        const managerInfo = await Staff.findOne({ staffId: parseInt(managerId) })
            .select('staffId firstName lastName email phoneNumber jobTitle isManager')
            .lean();

        const responseData = {
            clinic: {
                clinicId: updatedClinic.clinicId,
                clinicName: updatedClinic.clinicName,
                managerId: updatedClinic.managerId
            },
            manager: managerInfo
        };

        return sendResponse(res, 200, true, "Clinic manager assigned successfully", responseData);
    } catch (error) {
        console.error("Assign clinic manager error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Admin endpoint to attach/assign staff members to a clinic
 * This updates staff members' clinicId
 */
export const assignStaffToClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { staffIds, roleId } = req.body;

        // Validate input
        if (!staffIds || !Array.isArray(staffIds) || staffIds.length === 0) {
            return sendResponse(res, 400, false, "Staff IDs array is required");
        }

        // Use numeric clinicId only
        const clinicQuery = { clinicId: parseInt(clinicId) };

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify all staff members exist
        const staffMembers = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) }
        });

        if (staffMembers.length !== staffIds.length) {
            return sendResponse(res, 404, false, "One or more staff members not found");
        }

        // Verify role exists if provided
        let role = null;
        if (roleId) {
            role = await Role.findOne({ roleId: parseInt(roleId) });
            if (!role) {
                return sendResponse(res, 404, false, "Role not found");
            }
        }

        // Update all staff members
        const updateData = {
            clinicId: clinic.clinicId,
            updatedAt: new Date()
        };

        // Add role if provided
        if (roleId) {
            updateData.roleId = parseInt(roleId);
            updateData.permissions = role.permissions;
        }

        const updateResult = await Staff.updateMany(
            { staffId: { $in: staffIds.map(id => parseInt(id)) } },
            { $set: updateData }
        );

        // Get updated staff information
        const updatedStaff = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) }
        })
        .select('staffId firstName lastName email phoneNumber jobTitle clinicId roleId')
        .lean();

        // Get role information for response
        const roleIds = [...new Set(updatedStaff.map(s => s.roleId))];
        const roles = await Role.find({ roleId: { $in: roleIds } }).lean();
        const roleMap = roles.reduce((map, r) => {
            map[r.roleId] = r;
            return map;
        }, {});

        // Populate staff with role information
        const populatedStaff = updatedStaff.map(staff => ({
            ...staff,
            role: roleMap[staff.roleId] ? {
                roleId: roleMap[staff.roleId].roleId,
                roleName: roleMap[staff.roleId].name || roleMap[staff.roleId].roleName,
                description: roleMap[staff.roleId].description
            } : null
        }));

        const responseData = {
            clinic: {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName
            },
            assignedStaff: populatedStaff,
            assignedCount: updateResult.modifiedCount
        };

        return sendResponse(res, 200, true, `${updateResult.modifiedCount} staff members assigned to clinic successfully`, responseData);
    } catch (error) {
        console.error("Assign staff to clinic error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Admin endpoint to remove staff from a clinic
 * This sets their clinicId to null or moves them to another clinic
 */
export const removeStaffFromClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { staffIds, newClinicId } = req.body;

        // Validate input
        if (!staffIds || !Array.isArray(staffIds) || staffIds.length === 0) {
            return sendResponse(res, 400, false, "Staff IDs array is required");
        }

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let clinicQuery = {};
        if (!isNaN(clinicId)) {
            clinicQuery.clinicId = parseInt(clinicId);
        } else {
            clinicQuery._id = clinicId;
        }

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify new clinic exists if provided
        let newClinic = null;
        if (newClinicId) {
            let newClinicQuery = {};
            if (!isNaN(newClinicId)) {
                newClinicQuery.clinicId = parseInt(newClinicId);
            } else {
                newClinicQuery._id = newClinicId;
            }

            newClinic = await Clinic.findOne(newClinicQuery);
            if (!newClinic) {
                return sendResponse(res, 404, false, "New clinic not found");
            }
        }

        // Verify all staff members exist and belong to the clinic
        const staffMembers = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) },
            clinicId: clinic.clinicId
        });

        if (staffMembers.length !== staffIds.length) {
            return sendResponse(res, 404, false, "One or more staff members not found in this clinic");
        }

        // Check if any staff member is the clinic owner
        const clinicOwners = staffMembers.filter(staff =>
            staff.isClinicOwner && clinic.owner === staff.staffId
        );

        if (clinicOwners.length > 0) {
            return sendResponse(res, 400, false, "Cannot remove clinic owner. Please assign a new owner first.");
        }

        // Update staff members
        const updateData = {
            clinicId: newClinic ? newClinic.clinicId : null,
            currentClinicId: newClinic ? newClinic.clinicId : null,
            updatedAt: new Date()
        };

        const updateResult = await Staff.updateMany(
            { staffId: { $in: staffIds.map(id => parseInt(id)) } },
            { $set: updateData }
        );

        // Get updated staff information
        const updatedStaff = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) }
        })
        .select('staffId firstName lastName email phoneNumber jobTitle clinicId')
        .lean();

        const responseData = {
            clinic: {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName
            },
            newClinic: newClinic ? {
                clinicId: newClinic.clinicId,
                clinicName: newClinic.clinicName
            } : null,
            updatedStaff: updatedStaff,
            removedCount: updateResult.modifiedCount
        };

        const message = newClinic
            ? `${updateResult.modifiedCount} staff members moved to ${newClinic.clinicName} successfully`
            : `${updateResult.modifiedCount} staff members removed from clinic successfully`;

        return sendResponse(res, 200, true, message, responseData);
    } catch (error) {
        console.error("Remove staff from clinic error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
