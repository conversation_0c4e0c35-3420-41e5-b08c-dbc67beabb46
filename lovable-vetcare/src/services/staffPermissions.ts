import { api } from './api';
import { Permission } from '@/store/types';

export interface StaffPermissionsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    staff: {
      _id: string;
      staffId?: number;
      userId?: string | number;
      name: string;
      role: string;
    };
    permissions: Permission[];
    specialPermissions: number[];
    revokedPermissions: number[];
  } | null;
}

export interface PermissionActionResponse {
  success: boolean;
  status: number;
  message: string;
  data: any;
}

/**
 * Get staff permissions (combined role and special permissions)
 */
export const getStaffPermissions = async (staffId: string | number): Promise<StaffPermissionsResponse> => {
  try {
    return await api.get<StaffPermissionsResponse>(`/staff/${staffId}/permissions`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch staff permissions',
      data: null
    };
  }
};

/**
 * Grant special permissions to staff member
 */
export const grantSpecialPermissions = async (
  staffId: string | number, 
  permissionIds: number[]
): Promise<PermissionActionResponse> => {
  try {
    return await api.post<PermissionActionResponse>(
      `/staff/${staffId}/permissions`, 
      { permissionIds }
    );
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to grant special permissions',
      data: null
    };
  }
};

/**
 * Revoke permissions from staff member
 */
export const revokePermissions = async (
  staffId: string | number, 
  permissionIds: number[]
): Promise<PermissionActionResponse> => {
  try {
    return await api.delete<PermissionActionResponse>(
      `/staff/${staffId}/permissions`, 
      { data: { permissionIds } }
    );
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to revoke permissions',
      data: null
    };
  }
};

/**
 * Reset staff permissions to role defaults
 */
export const resetStaffPermissions = async (
  staffId: string | number
): Promise<PermissionActionResponse> => {
  try {
    return await api.put<PermissionActionResponse>(
      `/staff/${staffId}/permissions/reset`
    );
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to reset permissions',
      data: null
    };
  }
};
