import PetClinicRelationship from '../models/petClinicRelationship.model.js';
import Pet from '../models/pet.model.js';
import Clinic from '../models/clinic.model.js';
import Client from '../models/client.model.js';
import ClientClinicRelationship from '../models/clientClinicRelationship.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

/**
 * Create a new pet-clinic relationship
 * This is called when a pet visits a clinic for the first time
 */
export const createPetClinicRelationship = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { petId, clinicId, clinicNotes, medicalAlerts } = req.body;

        // Check if pet and clinic exist
        const [pet, clinic] = await Promise.all([
            Pet.findOne({ petId }).populate('owner').session(session),
            Clinic.findOne({ clinicId }).session(session)
        ]);

        if (!pet) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Pet not found");
        }

        if (!clinic) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Check if relationship already exists
        const existingRelationship = await PetClinicRelationship.findOne({
            petId,
            clinicId
        }).session(session);

        if (existingRelationship) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 409, false, "Relationship already exists");
        }

        // Check if client-clinic relationship exists, create if not
        let clientClinicRelationship = await ClientClinicRelationship.findOne({
            clientId: pet.owner.clientId,
            clinicId
        }).session(session);

        if (!clientClinicRelationship) {
            // Create client-clinic relationship
            [clientClinicRelationship] = await ClientClinicRelationship.create([{
                clientId: pet.owner.clientId,
                clinicId,
                registeredBy: req.staff?.staffId || 0,
                dataConsent: {
                    consentGiven: true,
                    consentDate: new Date(),
                    consentDetails: "Consent given during pet relationship creation"
                }
            }], { session });

            // Update client's clinicRelationships array
            await Client.findOneAndUpdate(
                { clientId: pet.owner.clientId },
                { $push: { clinicRelationships: clientClinicRelationship._id } },
                { session }
            );
        }

        // Create the pet-clinic relationship
        const relationship = await PetClinicRelationship.create([{
            petId,
            clinicId,
            clinicNotes,
            medicalAlerts: medicalAlerts || [],
            registeredBy: req.staff?.staffId || 0
        }], { session });

        // Update pet's clinicRelationships array
        await Pet.findOneAndUpdate(
            { petId },
            { $push: { clinicRelationships: relationship[0].petClinicRelationshipId } },
            { session }
        );

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 201, true, "Pet-clinic relationship created successfully", relationship[0]);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Create pet-clinic relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all relationships for a specific pet
 */
export const getPetRelationships = async (req, res) => {
    try {
        const { petId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        // Check if pet exists
        const pet = await Pet.findOne({ petId });
        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Get relationships with pagination
        const relationships = await PetClinicRelationship.find({ petId })
            .populate('clinicId', 'clinicName address phoneNumber email')
            .populate('registeredBy', 'firstName lastName')
            .skip((page - 1) * limit)
            .limit(parseInt(limit))
            .sort({ lastVisitDate: -1 })
            .lean();

        const totalCount = await PetClinicRelationship.countDocuments({ petId });

        return sendResponse(res, 200, true, "Pet relationships retrieved successfully",
            paginateResults(relationships, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get pet relationships error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all pet relationships for a specific clinic
 */
export const getClinicPetRelationships = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { page = 1, limit = 10, status, search } = req.query;

        // Check if clinic exists
        const clinic = await Clinic.findOne({ clinicId });
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Build query
        let query = { clinicId };
        if (status) {
            query.relationshipStatus = status;
        }

        // Handle search
        if (search) {
            // We need to find pets that match the search and then find relationships for those pets
            const petIds = await Pet.find({
                petName: { $regex: search, $options: 'i' }
            }).distinct('_id');

            query.petId = { $in: petIds };
        }

        // Get relationships with pagination
        const relationships = await PetClinicRelationship.find(query)
            .populate('petId', 'petName speciesId breedId')
            .populate({
                path: 'petId',
                populate: [
                    { path: 'speciesId', select: 'speciesName' },
                    { path: 'breedId', select: 'breedName' },
                    { path: 'owner', select: 'firstName lastName email phoneNumber' }
                ]
            })
            .populate('registeredBy', 'firstName lastName')
            .skip((page - 1) * limit)
            .limit(parseInt(limit))
            .sort({ lastVisitDate: -1 })
            .lean();

        const totalCount = await PetClinicRelationship.countDocuments(query);

        return sendResponse(res, 200, true, "Clinic pet relationships retrieved successfully",
            paginateResults(relationships, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Get clinic pet relationships error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get a specific pet-clinic relationship
 */
export const getRelationship = async (req, res) => {
    try {

        const relationship = await PetClinicRelationship.findOne({ petClinicRelationshipId: parseInt(req.params.relationshipId) })
            .populate('petId', 'petName speciesId breedId')
            .populate({
                path: 'petId',
                populate: [
                    { path: 'speciesId', select: 'speciesName' },
                    { path: 'breedId', select: 'breedName' },
                    { path: 'owner', select: 'firstName lastName email phoneNumber' }
                ]
            })
            .populate('clinicId', 'clinicName address phoneNumber email')
            .populate('registeredBy', 'firstName lastName')
            .lean();

        if (!relationship) {
            return sendResponse(res, 404, false, "Relationship not found");
        }

        return sendResponse(res, 200, true, "Relationship retrieved successfully", relationship);
    } catch (error) {
        console.error("Get relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update a pet-clinic relationship
 */
export const updateRelationship = async (req, res) => {
    try {
        const updateData = req.body;

        // Find the relationship
        const relationship = await PetClinicRelationship.findOne({ petClinicRelationshipId: parseInt(req.params.relationshipId) });
        if (!relationship) {
            return sendResponse(res, 404, false, "Relationship not found");
        }

        // Update the relationship
        const updatedRelationship = await PetClinicRelationship.findOneAndUpdate(
            { petClinicRelationshipId: parseInt(req.params.relationshipId) },
            updateData,
            { new: true, runValidators: true }
        )
        .populate('petId', 'petName speciesId breedId')
        .populate({
            path: 'petId',
            populate: [
                { path: 'speciesId', select: 'speciesName' },
                { path: 'breedId', select: 'breedName' },
                { path: 'owner', select: 'firstName lastName email phoneNumber' }
            ]
        })
        .populate('clinicId', 'clinicName address phoneNumber email')
        .populate('registeredBy', 'firstName lastName');

        return sendResponse(res, 200, true, "Relationship updated successfully", updatedRelationship);
    } catch (error) {
        console.error("Update relationship error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
