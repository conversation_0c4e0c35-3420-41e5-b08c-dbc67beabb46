import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const staffSchema = new mongoose.Schema({
    // Auto-increment ID
    staffId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Authentication fields
    firstName: {
        type: String,
        required: [true, "First name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    middleName: {
        type: String,
        required: [false],
        trim: true,
        maxLength: 55,
    },
    lastName: {
        type: String,
        required: [true, "Last name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    email: {
        type: String,
        required: [true, "Email is required"],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/\S+@\S+\.\S+/, 'Please enter a valid email address'],
    },
    password: {
        type: String,
        required: [true, "Password is required"],
        minLength: 6,
    },

    // Primary clinic where the staff member works
    clinicId: {
        type: Number,
        ref: 'Clinic',
        required: [true, "Clinic ID is required"],
        index: true,
    },
    // Additional clinics where the staff member can work
    additionalClinics: [{
        type: Number,
        ref: 'Clinic',
        index: true,
    }],
    // Primary clinic ID (same as clinicId, but used for clarity)
    primaryClinicId: {
        type: Number,
        ref: 'Clinic',
        required: [true, "Primary clinic ID is required"],
        index: true,
    },
    roleId: {
        type: Number,
        ref: 'Role',
        required: [true, 'Role ID is required'],
        index: true,
    },
    // Add special permissions field - permissions granted beyond role
    specialPermissions: [{
        type: Number,
        ref: 'Permission'
    }],
    // Add revoked permissions field - permissions removed from role
    revokedPermissions: [{
        type: Number,
        ref: 'Permission'
    }],
    jobTitle: {
        type: String,
        required: [true, 'Job title is required'],
        trim: true
    },
    employmentDate: {
        type: Date,
        required: [true, 'Employment date is required']
    },
    salary: {
        type: Number,
        required: [true, 'Salary is required']
    },
    isClinicOwner: {
        type: Boolean,
        default: false
    },
    isManager: {
        type: Boolean,
        default: false
    },
    emergencyContact: {
        name: String,
        relationship: String,
        phone: String
    },
    schedule: {
        sunday: { start: String, end: String },
        monday: { start: String, end: String },
        tuesday: { start: String, end: String },
        wednesday: { start: String, end: String },
        thursday: { start: String, end: String },
        friday: { start: String, end: String },
        saturday: { start: String, end: String }
    },
    status: {
        type: Number,
        enum: [0, 1], // 0: Inactive, 1: Active
        default: 1
    },

    // Login tracking fields
    lastLogin: {
        type: Date,
        default: null
    },
    loginCount: {
        type: Number,
        default: 0
    },
    loginHistory: [{
        timestamp: {
            type: Date,
            default: Date.now
        },
        ipAddress: String,
        userAgent: String,
        status: {
            type: String,
            enum: ['success', 'failed'],
            default: 'success'
        }
    }],

    // Current clinic
    currentClinicId: {
        type: Number,
        ref: 'Clinic',
        default: null,
        index: true,
    },

    // Track last active time at each clinic
    clinicActivity: [{
        clinicId: {
            type: Number,
            ref: 'Clinic'
        },
        lastActive: {
            type: Date,
            default: Date.now
        },
        activityCount: {
            type: Number,
            default: 0
        }
    }]
}, {
    timestamps: true,
    collection: 'staff'
});

// Indexes for better query performance
staffSchema.index({ email: 1 }, { unique: true });
staffSchema.index({ clinicId: 1 });
staffSchema.index({ primaryClinicId: 1 });
staffSchema.index({ additionalClinics: 1 });
staffSchema.index({ roleId: 1 });
staffSchema.index({ status: 1 });
staffSchema.index({ 'clinicActivity.clinicId': 1 });
staffSchema.index({ firstName: 1, lastName: 1 });
staffSchema.index({ phoneNumber: 1 });

// Add new method to check if staff has a specific permission
staffSchema.methods.hasPermission = async function(permissionId) {
    // Get the role permissions
    const Role = mongoose.model('Role');
    const role = await Role.findOne({ roleId: this.role }).lean();

    if (!role) return false;

    // Check if permission is revoked
    if (this.revokedPermissions.includes(permissionId)) return false;

    // Check if permission is in role or special permissions
    return role.permissions.includes(permissionId) ||
           this.specialPermissions.includes(permissionId);
};

// Add method to check if staff has access to a specific clinic
staffSchema.methods.hasClinicAccess = function(clinicId) {
    // Ensure we're comparing numbers
    const clinicIdNum = parseInt(clinicId);
    const primaryClinicIdNum = parseInt(this.primaryClinicId);

    // Check if it's the primary clinic
    if (primaryClinicIdNum === clinicIdNum) return true;

    // Check if it's in additional clinics
    return this.additionalClinics.some(id => parseInt(id) === clinicIdNum);
};

// Add method to update clinic activity
staffSchema.methods.updateClinicActivity = async function(clinicId) {
    // Ensure we're using the numeric clinic ID
    const clinicIdNum = parseInt(clinicId);

    // Find the clinic activity entry
    const activityEntry = this.clinicActivity.find(
        entry => parseInt(entry.clinicId) === clinicIdNum
    );

    if (activityEntry) {
        // Update existing entry
        activityEntry.lastActive = new Date();
        activityEntry.activityCount += 1;
    } else {
        // Create new entry
        this.clinicActivity.push({
            clinicId: clinicIdNum,
            lastActive: new Date(),
            activityCount: 1
        });
    }

    // Save the changes
    return this.save();
};

// Add auto-increment plugin
staffSchema.plugin(AutoIncrement, { inc_field: 'staffId', start_seq: 1001 });
staffSchema.index({ staffId: 1 }, { unique: true });

const Staff = mongoose.model('Staff', staffSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = 'staff';
        const existingCollections = await mongoose.connection.db.listCollections().toArray();

        if (existingCollections.some(col => col.name === collectionName)) {
            await mongoose.connection.db.dropCollection(collectionName);
        }

        await Staff.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Staff;
