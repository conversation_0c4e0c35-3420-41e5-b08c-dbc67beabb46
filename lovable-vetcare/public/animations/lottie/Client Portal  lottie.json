{"v": "5.7.6", "fr": 30, "ip": 0, "op": 150, "w": 580, "h": 380, "nm": "Master Composition", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Screen and Code", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [288.5, 181.5, 0], "to": [0, -1.833, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [288.5, 170.5, 0], "to": [0, 0, 0], "ti": [0, -1.833, 0]}, {"t": 149, "s": [288.5, 181.5, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "Code Screen Only", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0, 0, 0], "to": [0, 3.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [0, 20, 0], "to": [0, 0, 0], "ti": [0, 3.333, 0]}, {"t": 149, "s": [0, 0, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Code", "parent": 2, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [22.489, -51.623, 0]}, "a": {"a": 0, "k": [39.363, 67.831, 0]}, "s": {"a": 0, "k": [112.737, 112.737, 100]}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.006, -3.558], [3.952, 69.449], [-15.958, 90.297], [71.003, 141.53], [74.229, 40.347]], "c": true}}, "o": {"a": 0, "k": 100}, "x": {"a": 0, "k": 0}, "nm": "Mask 1"}], "w": 80, "h": 136, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Letter Outlines", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [6.375, -64.018, 0]}, "a": {"a": 0, "k": [73.595, 104.297, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.12, 0], [0.154, 0.397], [0, 0], [-0.516, 0.198], [-0.2, -0.516], [0, 0], [0.516, -0.199]], "o": [[-0.401, 0], [0, 0], [-0.198, -0.515], [0.513, -0.197], [0, 0], [0.198, 0.515], [-0.118, 0.045]], "v": [[0.833, 3.227], [-0.101, 2.587], [-1.767, -1.738], [-1.192, -3.031], [0.101, -2.458], [1.767, 1.868], [1.192, 3.161]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [136.375, 67.565]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.182, 0], [0.191, 0.302], [-0.468, 0.294], [0, 0], [-0.295, -0.467], [0.467, -0.294], [0, 0]], "o": [[-0.332, 0], [-0.294, -0.467], [0, 0], [0.467, -0.294], [0.294, 0.468], [0, 0], [-0.165, 0.104]], "v": [[-1.493, 2.01], [-2.342, 1.542], [-2.027, 0.163], [0.963, -1.716], [2.342, -1.402], [2.028, -0.023], [-0.963, 1.857]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [136.353, 67.49]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.333, -7.338], [-9.333, -3.499], [9.333, 7.338], [9.333, 3.499]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [63.579, 25.428]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.616, 1.922], [-6.616, -5.761], [-6.606, -1.915], [6.616, 5.761]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.629, 13.846]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.083, -6.032], [-7.083, -2.192], [7.083, 6.032], [7.083, 2.193]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [89.995, 40.764]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-53.729, -36.58], [53.699, 25.785], [53.729, 36.58], [-53.698, -25.786]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [87.726, 39.447]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.757, -4.089], [1.725, -6.705], [1.757, 4.088], [-1.727, 6.705]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [143.181, 69.321]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-55.454, -29.874], [-51.972, -32.491], [55.454, 29.875], [51.973, 32.491]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [89.452, 32.741]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.02, 7.119], [0, 0], [-4.062, -2.36], [0, 0]], "o": [[0, 0], [0.02, 7.118], [0, 0], [-4.061, -2.36]], "v": [[50.431, 23.979], [-57.033, -38.47], [-50.43, -23.979], [57.033, 38.47]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.283, 168.32]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0.008], [0, 0], [0, 0], [0, 0], [0, 0], [9.757, -5.635], [3.22, 1.872], [0, 0], [-4.913, 2.835], [-0.13, 14.483], [0, 0]], "o": [[0, -0.009], [0, 0], [0, 0], [0, 0], [0, 0], [-0.01, 16.179], [-4.911, 2.835], [0, 0], [3.219, 1.871], [9.68, -5.591], [0, 0], [0, 0]], "v": [[68.846, 67.604], [68.846, 67.578], [68.846, 67.577], [68.57, -38.778], [-38.893, -101.229], [-38.617, 5.155], [-56.258, 35.685], [-68.846, 36.907], [38.617, 99.358], [51.205, 98.136], [68.833, 67.938], [68.846, 67.945]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [75.192, 107.115]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [22.003, -9.17], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.999, -66.305], [8.999, 33.676], [-11.001, 66.304], [5.516, -63.688]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [135.939, 139.714]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Small Code 2 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [377.301, 260.076, 0], "to": [0, 1.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [377.301, 270.076, 0], "to": [0, 0, 0], "ti": [0, 1.667, 0]}, {"t": 149, "s": [377.301, 260.076, 0]}]}, "a": {"a": 0, "k": [38.403, 44.147, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.119, 0], [0.154, 0.398], [0, 0], [-0.516, 0.199], [-0.199, -0.515], [0, 0], [0.516, -0.2]], "o": [[-0.401, 0], [0, 0], [-0.199, -0.515], [0.514, -0.195], [0, 0], [0.199, 0.515], [-0.118, 0.045]], "v": [[0.833, 3.227], [-0.101, 2.585], [-1.766, -1.738], [-1.192, -3.031], [0.101, -2.458], [1.766, 1.868], [1.192, 3.161]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [67.993, 35.316]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.182, 0], [0.191, 0.302], [-0.468, 0.294], [0, 0], [-0.293, -0.466], [0.467, -0.294], [0, 0]], "o": [[-0.332, 0], [-0.294, -0.468], [0, 0], [0.469, -0.292], [0.294, 0.468], [0, 0], [-0.165, 0.103]], "v": [[-1.493, 2.009], [-2.342, 1.541], [-2.027, 0.162], [0.963, -1.717], [2.342, -1.402], [2.028, -0.023], [-0.963, 1.856]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [67.971, 35.242]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.438, -4.497], [-4.438, -0.658], [4.438, 4.497], [4.438, 0.658]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.048, 22.02]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.616, 1.921], [-6.616, -5.76], [-6.606, -1.915], [6.616, 5.76]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.537, 13.596]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-26.083, -20.58], [26.053, 9.786], [26.083, 20.58], [-26.053, -9.786]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.989, 23.197]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.757, -4.089], [1.726, -6.706], [1.757, 4.089], [-1.727, 6.706]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [74.799, 37.071]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-27.81, -13.875], [-24.327, -16.492], [27.81, 13.874], [24.327, 16.492]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.715, 16.491]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.984, 0.221], [1.984, 2.541], [1.984, -0.221], [-1.984, -2.541]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.49, 44.021]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.419, -0.619], [3.419, 3.381], [3.419, 0.619], [-3.419, -3.381]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.054, 47.065]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.419, -0.62], [3.419, 3.382], [3.419, 0.618], [-3.419, -3.382]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.054, 50.95]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.337, 0.043], [0.266, -0.238], [0.094, -0.6], [0, -1.648], [0.051, -0.372], [0.15, -0.271], [0.207, -0.033], [0.416, 0.212], [0, 0], [0, 0], [-0.25, -0.443], [-0.129, -0.753], [0, -1.563], [-0.099, -0.679], [-0.272, -0.586], [-0.402, -0.423], [-0.753, -0.44], [0, 0], [0, 0], [0.181, 0.213], [0.063, 0.244], [0.007, 0.638], [0.057, 0.634], [0.26, 0.719], [0.417, 0.66], [-0.207, 0.258], [-0.121, 0.467], [-0.028, 0.868], [0, 0.526], [-0.085, 0.167], [-0.185, 0.012], [-0.601, -0.352], [0, 0], [0, 0]], "o": [[-0.509, -0.069], [-0.265, 0.239], [-0.099, 0.61], [0, 1.014], [-0.056, 0.515], [-0.144, 0.274], [-0.208, 0.032], [0, 0], [0, 0], [0.515, 0.346], [0.251, 0.44], [0.086, 0.531], [0, 1.352], [0.102, 0.693], [0.271, 0.584], [0.406, 0.411], [0, 0], [0, 0], [-0.637, -0.372], [-0.187, -0.231], [-0.071, -0.263], [-0.028, -2.056], [-0.094, -0.915], [-0.192, -0.506], [0.323, -0.083], [0.208, -0.239], [0.123, -0.461], [0.044, -1.449], [0.006, -0.518], [0.087, -0.167], [0.187, 0.001], [0, 0], [0, 0], [-0.733, -0.428]], "v": [[0.818, -12.762], [-0.351, -12.507], [-0.889, -11.24], [-1.039, -7.855], [-1.111, -5.77], [-1.426, -4.589], [-1.956, -4.124], [-2.881, -4.383], [-2.874, -4.38], [-2.874, -1.31], [-1.727, -0.125], [-1.161, 1.668], [-1.033, 4.8], [-0.889, 7.84], [-0.329, 9.762], [0.69, 11.276], [2.431, 12.567], [2.881, 12.831], [2.881, 9.774], [1.656, 8.883], [1.277, 8.17], [1.161, 6.823], [1.025, 2.793], [0.487, 0.329], [-0.423, -1.405], [0.366, -1.924], [0.852, -2.996], [1.082, -4.988], [1.155, -7.938], [1.291, -8.97], [1.698, -9.235], [2.881, -8.705], [2.881, -11.784], [2.431, -12.048]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.324, 44.29]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.252, 0.452], [0.122, 0.759], [0, 1.572], [0.102, 0.682], [0.278, 0.588], [0.402, 0.42], [0.761, 0.444], [0, 0], [0, 0], [-0.179, -0.226], [-0.092, -0.283], [0, -0.559], [-0.052, -1.447], [-0.109, -0.619], [-0.216, -0.52], [-0.33, -0.486], [0.186, -0.241], [0.114, -0.459], [0.028, -0.833], [0, -0.544], [0.08, -0.171], [0.18, -0.004], [0.624, 0.364], [0, 0], [0, 0], [-0.343, -0.049], [-0.263, 0.237], [-0.101, 0.598], [0, 1.66], [-0.05, 0.388], [-0.151, 0.283], [-0.207, 0.041], [-0.409, -0.194], [0, 0]], "o": [[-0.251, -0.443], [-0.087, -0.52], [0, -1.355], [-0.094, -0.686], [-0.28, -0.59], [-0.407, -0.425], [0, 0], [0, 0], [0.601, 0.352], [0.179, 0.225], [0.087, 0.268], [0, 0.391], [0.027, 0.955], [0.113, 0.622], [0.208, 0.505], [-0.372, 0.155], [-0.181, 0.231], [-0.116, 0.465], [-0.051, 1.465], [0, 0.522], [-0.086, 0.17], [-0.171, 0.01], [0, 0], [0, 0], [0.74, 0.433], [0.503, 0.062], [0.267, -0.249], [0.1, -0.594], [0, -1.014], [0.057, -0.511], [0.15, -0.273], [0.207, -0.034], [0, 0], [-0.515, -0.343]], "v": [[1.731, 0.135], [1.171, -1.657], [1.037, -4.79], [0.893, -7.844], [0.334, -9.754], [-0.686, -11.266], [-2.434, -12.574], [-2.877, -12.833], [-2.877, -9.754], [-1.702, -8.893], [-1.295, -8.141], [-1.165, -6.896], [-1.093, -4.137], [-0.877, -1.773], [-0.39, -0.07], [0.419, 1.419], [-0.419, 1.999], [-0.864, 3.027], [-1.085, 4.96], [-1.165, 7.971], [-1.287, 9.01], [-1.682, 9.271], [-2.877, 8.738], [-2.877, 11.794], [-2.434, 12.053], [-0.814, 12.771], [0.339, 12.508], [0.886, 11.242], [1.037, 7.871], [1.107, 5.772], [1.416, 4.589], [1.954, 4.13], [2.877, 4.385], [2.877, 1.318]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.775, 49.827]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-26.224, -34.051], [-25.948, 3.6], [26.224, 34.051], [25.948, -3.6]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.875, 35.31]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.02, 7.118], [0, 0], [-4.062, -2.36], [0, 0]], "o": [[0, 0], [0.02, 7.119], [0, 0], [-4.061, -2.36]], "v": [[22.786, 7.98], [-29.388, -22.471], [-22.785, -7.979], [29.388, 22.471]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.638, 64.02]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.222, 1.872], [0, 0], [0, 16.465], [0, 0]], "o": [[0, 0], [3.22, 1.87], [0, 0], [0, 16.416]], "v": [[-34.378, -7.57], [17.796, 22.88], [34.378, 5.701], [-17.794, -24.751]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.722, 63.293]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [13.5, -0.676], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.001, -22.768], [7.001, 6.069], [-7.001, 22.768], [4.887, -21.18]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [69.555, 63.928]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Screen Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [21.767, -29.468, 0]}, "a": {"a": 0, "k": [80.083, 116.236, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-71.447, 17.379], [71.447, 98.946], [71.447, -17.376], [-71.447, -98.946]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.932703354779, 0.955448584463, 0.963032382142, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [77.403, 112.474]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.829, 1.615], [0, 0], [0.01, -3.24], [0, 0], [-2.853, -1.628], [0, 0], [-0.01, 3.241], [0, 0]], "o": [[0, 0], [-2.852, -1.628], [0, 0], [-0.009, 3.242], [0, 0], [2.829, 1.615], [0, 0], [0.01, -3.241]], "v": [[72.132, -27.848], [-71.837, -110.033], [-77.025, -107.135], [-77.264, 19.035], [-72.109, 27.863], [71.86, 110.047], [77.025, 107.111], [77.262, -19.06]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.717538631664, 0.814217840456, 0.836017922794, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [77.523, 114.126]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.829, 1.615], [0, 0], [0.942, -0.541], [0, 0], [-1.441, -0.822], [0, 0], [0.01, -3.24], [0, 0], [0.928, -0.535], [0, 0], [-0.005, 1.612], [0, 0]], "o": [[0, 0], [-1.441, -0.823], [0, 0], [0.941, -0.541], [0, 0], [2.829, 1.615], [0, 0], [-0.004, 1.614], [0, 0], [0.929, -0.533], [0, 0], [0.01, -3.241]], "v": [[73.819, -29.05], [-70.15, -111.233], [-73.84, -111.594], [-78.96, -108.651], [-75.271, -108.292], [68.698, -26.107], [73.829, -17.32], [73.591, 108.851], [72.079, 112.134], [77.199, 109.191], [78.712, 105.91], [78.949, -20.261]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [80.956, 112.385]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-20.174, -9.905], [20.128, 13.324], [20.174, 9.906], [-20.128, -13.324]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.914689307119, 0.936495852003, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.348, 210.792]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [6.852, -4.449], [0, 0], [0, 0], [0, 0], [0, 8.105], [0, 0], [0, 0]], "o": [[0, 8.175], [0, 0], [0, 0], [0, 0], [6.798, -4.417], [0, 0], [0, 0], [0, 0]], "v": [[-9.9, -22.32], [-20.887, -2.077], [-30.246, 3.96], [10.074, 27.195], [19.348, 21.219], [30.246, 1.139], [30.246, -3.757], [-9.9, -27.195]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.895734480316, 0.932703354779, 0.940282066196, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [59.42, 193.543]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-41.394, -3.896], [-1.083, 19.334], [41.394, -5.622], [17.438, -19.335]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5, 0.5, 0.5, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [70.568, 204.782]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.836, 2.717], [0, 0], [2.168, -1.226], [0, 0], [-4.835, -2.717], [0, 0], [-2.169, 1.226], [0, 0]], "o": [[0, 0], [-2.172, -1.221], [0, 0], [-4.828, 2.729], [0, 0], [2.172, 1.221], [0, 0], [4.829, -2.73]], "v": [[44.79, -5.379], [1.888, -29.486], [-5.106, -29.477], [-44.807, -7.027], [-44.79, 5.377], [-1.889, 29.485], [5.104, 29.476], [44.807, 7.027]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [69.523, 201.515]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Hand With Remte Outlines", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-31]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 108, "s": [13]}, {"t": 134, "s": [-31]}]}, "p": {"a": 0, "k": [74.989, 10.8, 0]}, "a": {"a": 0, "k": [3.292, 30.639, 0]}, "s": {"a": 0, "k": [344.828, 344.828, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.82, 2.309], [4.777, -2.487], [-0.921, -1.9], [-0.015, -0.031], [-2.046, 1.199], [-4.839, 5.662]], "o": [[-4.521, 5.05], [-1.815, 1.072], [0.016, 0.031], [0.92, 1.896], [5.35, -2.775], [-1.828, -0.891]], "v": [[7.49, -11.109], [-10.119, 2.542], [-11.754, 7.805], [-11.479, 8.301], [-5.64, 9.91], [12.675, -6.536]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.925, 23.919]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [1.383, -0.325], [0.013, 0.114], [0, 0], [-0.243, -0.013], [0.057, 0.606], [0, 0], [0.899, -0.633], [0.903, -0.763], [0, 0], [0, 0], [-0.818, 0.134], [-1.498, 1.532], [-0.72, 2.222]], "o": [[0, 0], [-0.815, 0.198], [-0.047, -0.465], [0, 0], [0.713, 0.035], [0, 0], [0, 0], [-0.888, 0.645], [-2.137, 1.801], [0, 0], [3.316, -3.357], [0.464, -0.075], [1.488, -1.529], [0.733, -2.222]], "v": [[8.832, -7.568], [4.678, -5.985], [2.448, -6.322], [3.036, -7.026], [5.512, -7.151], [6.892, -8.407], [3.195, -8.73], [1.133, -8.132], [-0.517, -5.175], [-10.76, 3.935], [-6.834, 8.765], [1.381, -0.32], [8.214, -1.976], [10.027, -6.231]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78104577158, 0.709956389782, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.522, 15.872]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.116, 0.323], [0, 0], [0, 0], [0.594, 0.38]], "o": [[0, 0], [0, 0], [0, 0], [-0.368, -0.236]], "v": [[-1.03, -0.962], [-1.449, 0.962], [1.449, 0.406], [-0.312, -0.128]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.757, 7.121]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.348, 0.207], [-0.579, 0.091], [0, 0]], "o": [[0.163, -0.254], [0.487, -0.302], [0, 0], [0, 0]], "v": [[-0.985, 0.892], [-0.22, 0.175], [1.402, -0.405], [-1.402, -0.892]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.92, 4.014]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.163, -0.116], [0.024, -0.022], [0.626, -0.093], [0, 0], [0, 0]], "o": [[-0.022, 0.024], [-0.532, 0.327], [0, 0], [0, 0], [-0.138, 0.14]], "v": [[0.591, -0.499], [0.498, -0.43], [-1.263, 0.198], [1.263, 0.87], [1.031, -0.87]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [35.397, 7.236]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.394, -0.255], [-0.116, -0.232], [-0.024, -0.069], [0, 0], [0, 0]], "o": [[0.302, 0.187], [0.024, 0.071], [0, 0], [0, 0], [0.534, 0.068]], "v": [[0.14, 0.034], [0.788, 0.706], [0.882, 0.915], [1.275, -0.915], [-1.275, -0.428]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.011, 3.968]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.218, 0.117], [0.515, 0], [0.496, -0.269], [-0.81, -0.434], [-0.914, 0.494], [-0.025, 0.014], [0.235, 0.398]], "o": [[-0.371, -0.198], [-0.611, 0], [-0.912, 0.496], [0.812, 0.434], [0.026, -0.014], [0.609, -0.359], [-0.09, -0.152]], "v": [[1.714, -0.967], [0.332, -1.263], [-1.415, -0.857], [-1.6, 0.829], [1.528, 0.72], [1.604, 0.677], [2.174, -0.558]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.741, 5.578]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.391, 0.217], [0, 0], [0.207, 0], [0.297, -0.174], [0, 0], [-0.003, -0.408], [-0.39, -0.217], [0, 0], [-0.207, 0], [-0.298, 0.174], [0, 0], [0.004, 0.406]], "o": [[0, 0], [-0.302, -0.169], [-0.212, 0], [0, 0], [-0.376, 0.218], [0.004, 0.409], [0, 0], [0.302, 0.169], [0.211, 0], [0, 0], [0.377, -0.219], [-0.003, -0.41]], "v": [[10.495, -3.369], [4.721, -6.586], [3.9, -6.789], [3.076, -6.579], [-10.531, 1.37], [-11.126, 2.368], [-10.495, 3.369], [-4.721, 6.586], [-3.9, 6.789], [-3.077, 6.579], [10.529, -1.37], [11.124, -2.368]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.206, 7.039]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.21, -0.124], [0, 0], [0.21, 0], [0.302, 0.168], [0, 0], [0, 0], [-0.001, -0.18], [-0.391, -0.217], [0, 0], [-0.207, 0], [-0.297, 0.174], [0, 0], [0.005, 0.406], [0, 0]], "o": [[0, 0], [-0.297, 0.175], [-0.207, 0], [0, 0], [-0.235, -0.13], [0, 0], [0.005, 0.41], [0, 0], [0.302, 0.168], [0.21, 0], [0, 0], [0.377, -0.221], [-0.001, -0.164], [0, 0]], "v": [[10.528, -4.312], [-3.079, 3.635], [-3.901, 3.846], [-4.722, 3.643], [-10.495, 0.425], [-11.122, -0.178], [-11.127, 0.386], [-10.495, 1.387], [-4.722, 4.605], [-3.901, 4.807], [-3.079, 4.597], [10.528, -3.35], [11.123, -4.35], [11.123, -4.807]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.207, 9.478]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Chair Back Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [189.753, 239.664, 0]}, "a": {"a": 0, "k": [34.672, 40.66, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.101, -1.043], [0, 0], [0, 0], [0, 0], [0, 0], [2.961, 1.583], [0, 0], [1.071, -2.761], [-2.211, -1.165], [0, 0], [-0.35, -3.615], [0, 0], [0, 0], [-2.742, 1.461], [0, 0], [0, 1.163], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.368, -3.55], [0, 0], [-2.485, -1.331], [0.945, -1.452], [0, 0], [3.042, 1.605], [0, 0], [0, 0], [0.027, 3.107], [0, 0], [1.027, -0.55], [0, 0], [0, -2.345]], "v": [[29.5, 19.205], [7.406, 30.629], [7.395, 30.61], [7.463, 30.559], [3.672, -16.592], [-2.32, -25.853], [-22.865, -38.77], [-34.066, -36.024], [-29.046, -36.622], [-7.883, -23.827], [-1.774, -14.414], [1.493, 34.956], [1.493, 35.025], [7.577, 38.64], [32.396, 24.892], [34.066, 22.107], [34.066, 22.035]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.914689307119, 0.936495852003, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [35.029, 40.351]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.042, 1.605], [0, 0], [0.947, -1.453], [0, 0], [-0.085, -1.277], [0, 0], [-2.058, -1.196], [0, 0], [0, 0]], "o": [[0, 0], [-2.211, -1.164], [0, 0], [-0.473, 0.733], [0, 0], [0.146, 2.375], [0, 0], [0, 0], [-0.35, -3.615]], "v": [[8.761, -22.412], [-12.403, -35.207], [-17.423, -34.608], [-17.423, -34.602], [-18.051, -31.561], [-16.269, 12.713], [-12.741, 18.43], [18.136, 36.371], [14.87, -12.999]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.385, 38.936]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-4.324, -2.528], [0, 0], [-3.487, 1.861], [0, 0], [0, 0]], "o": [[0.001, 5.011], [0, 0], [3.411, 1.994], [0, 0], [0, 0], [0, 0]], "v": [[-21.255, -12.646], [-14.271, -0.474], [6.799, 11.848], [17.901, 12.064], [21.255, 10.274], [-21.255, -13.925]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.293, 67.146]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 3, "nm": "Hand Optios", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [52]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0]}, {"t": 149, "s": [52]}]}, "p": {"a": 0, "k": [191.5, 217.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [29, 29, 100]}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Hand Uppe Outlines", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [32.075, 4.897, 0]}, "a": {"a": 0, "k": [16.81, 7.869, 0]}, "s": {"a": 0, "k": [344.828, 344.828, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.448, -0.095], [6.362, 2.331], [0.991, -2.98], [-0.025, -0.775], [-2.493, -0.846], [-7.878, 0.478], [0.164, 2.337], [0.001, 0.039]], "o": [[-7.16, 0.636], [-2.236, -0.686], [-0.162, 0.488], [0.112, 3.462], [7.091, 2.405], [2.732, -0.165], [-0.003, -0.039], [-0.164, -2.34]], "v": [[11.612, -2.249], [-10.612, -6.934], [-16.378, -2.493], [-16.535, -0.255], [-12, 5.215], [11.915, 6.742], [16.396, 1.838], [16.387, 1.684]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.809, 7.87]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Chair Bottom Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [185.569, 296.502, 0]}, "a": {"a": 0, "k": [29.365, 21.754, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-2.25, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.606, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.107, 9.458], [-0.107, 10.113], [3.107, 9.458], [3.107, -10.113], [-3.107, -10.113]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.044, 10.362]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.142, 0], [0, -1.619], [-4.142, 0], [0, 1.617]], "o": [[-4.142, 0], [0, 1.617], [4.142, 0], [0, -1.619]], "v": [[-0.001, -2.93], [-7.5, 0.001], [-0.001, 2.93], [7.5, 0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30, 19.165]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.142, 0], [0, -1.619], [-4.142, 0], [0, 1.617]], "o": [[-4.142, 0], [0, 1.617], [4.142, 0], [0, -1.619]], "v": [[-0.001, -2.93], [-7.5, 0.001], [-0.001, 2.93], [7.5, 0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30, 19.935]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.813, 0.417], [2.092, 0.156], [-2.279, -5.573], [-0.76, 0.23], [-0.027, 0.009], [0.397, 0.832]], "o": [[-1.217, 0.649], [-0.123, -0.005], [0.3, 0.736], [0.026, -0.009], [0.881, -0.266], [-2.613, -5.484]], "v": [[-4.447, -9.047], [-9.456, -8.281], [6.365, 7.891], [8.197, 8.816], [8.279, 8.79], [9.182, 6.706]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.495, 29.934]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.835, 0], [0, -1.836], [-1.837, 0], [0, 1.836]], "o": [[-1.837, 0], [0, 1.836], [1.835, 0], [0, -1.836]], "v": [[0.001, -3.324], [-3.325, 0.001], [0.001, 3.324], [3.325, 0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.409, 39.934]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[6.983, 3.105], [0.132, -0.052], [0, 0], [0.007, -0.017], [0.054, -0.434], [0, -0.14], [-1.829, 0.008], [0.003, 1.675], [0.268, 0.571]], "o": [[-0.132, 0.048], [0, 0], [9.244, 5.909], [-0.239, 0.359], [-0.018, 0.13], [0.006, 1.674], [1.844, -0.003], [-0.002, -0.656], [-1.067, -2.283]], "v": [[-5.223, -10.729], [-10.22, -10.471], [-10.22, -8.378], [4.066, 6.101], [3.603, 7.297], [3.576, 7.699], [6.902, 10.721], [10.217, 7.684], [9.706, 5.898]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.535, 32.38]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[17.063, -1.564], [0, 0], [0.479, -0.768], [0.45, -0.247], [-3.051, -1.443]], "o": [[0, 0], [1.074, 0.619], [-0.202, 0.314], [13.284, -1.984], [0, 0]], "v": [[-12.07, -2.338], [-12.116, -2.264], [-11.07, -0.088], [-12.064, 0.751], [12.116, 3.902]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.085, 20.136]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.528, 0], [0, -1.529], [-1.529, 0], [0, 1.528]], "o": [[-1.529, 0], [0, 1.528], [1.528, 0], [0, -1.529]], "v": [[0.001, -2.768], [-2.768, -0.001], [0.001, 2.768], [2.768, -0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.712, 25.496]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.736, 0.419], [7.624, -2.73], [-0.902, -0.638], [0, 0], [0.036, -0.032], [0, -0.669], [-1.595, 0.003], [0.004, 1.453]], "o": [[-5.152, -2.914], [0.905, 0.464], [2.942, -0.647], [-0.036, 0.033], [-0.451, 0.458], [0.005, 1.456], [1.582, -0.006], [-0.003, -0.852]], "v": [[11.4, 0.29], [-12.136, -2.292], [-9.428, -0.627], [7.598, 0.54], [7.494, 0.646], [7.913, 2.396], [9.663, 5.018], [11.393, 2.383]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.157, 23.178]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.217, 0.649], [2.615, -5.484], [-0.881, -0.266], [-0.027, -0.009], [-0.3, 0.736], [0.123, -0.005]], "o": [[-0.814, 0.417], [-0.395, 0.832], [0.028, 0.009], [0.76, 0.23], [2.279, -5.573], [-2.092, 0.156]], "v": [[4.449, -9.047], [-9.183, 6.706], [-8.279, 8.79], [-8.197, 8.816], [-6.364, 7.891], [9.454, -8.281]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.251, 29.934]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.835, 0], [0, -1.836], [-1.836, 0], [0, 1.836]], "o": [[-1.836, 0], [0, 1.836], [1.835, 0], [0, -1.836]], "v": [[0.001, -3.324], [-3.325, 0.001], [0.001, 3.324], [3.325, 0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.388, 39.934]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.067, -2.283], [0.001, -0.656], [-1.842, -0.003], [-0.004, 1.674], [0.017, 0.13], [0.239, 0.359], [-9.244, 5.909], [0, 0], [0.132, 0.048]], "o": [[-0.268, 0.571], [-0.005, 1.675], [1.829, 0.008], [0.002, -0.14], [-0.053, -0.434], [-0.006, -0.016], [0, 0], [-0.133, -0.052], [-6.984, 3.105]], "v": [[-9.706, 5.897], [-10.215, 7.684], [-6.903, 10.721], [-3.577, 7.699], [-3.602, 7.296], [-4.066, 6.1], [10.221, -8.378], [10.221, -10.472], [5.225, -10.729]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.209, 32.381]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-1.076, 0.619], [0, 0], [0, 0], [-13.284, -1.984], [0.201, 0.314]], "o": [[0, 0], [-17.065, -1.564], [3.049, -1.443], [-0.449, -0.246], [-0.48, -0.768]], "v": [[12.116, -2.265], [12.069, -2.339], [-12.116, 3.902], [12.062, 0.751], [11.069, -0.089]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.662, 20.137]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.528, 0], [0, -1.529], [-1.53, 0], [0, 1.528]], "o": [[-1.53, 0], [0, 1.528], [1.528, 0], [0, -1.529]], "v": [[0.001, -2.768], [-2.768, -0.001], [0.001, 2.768], [2.768, -0.001]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.018, 25.496]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.152, -2.914], [0.003, -0.852], [-1.582, -0.006], [-0.003, 1.456], [0.451, 0.458], [0.036, 0.033], [-2.943, -0.647], [-0.904, 0.464]], "o": [[-0.736, 0.419], [-0.004, 1.453], [1.595, 0.003], [0, -0.669], [-0.036, -0.032], [0, 0], [0.901, -0.638], [-7.624, -2.73]], "v": [[-11.4, 0.29], [-11.393, 2.383], [-9.663, 5.018], [-7.913, 2.396], [-7.495, 0.646], [-7.599, 0.54], [9.429, -0.627], [12.137, -2.292]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.588, 23.178]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Aunty Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [199.218, 240.147, 0]}, "a": {"a": 0, "k": [32.392, 63.444, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-2.362, 1.602], [0, 4.613], [6.619, 0], [2.217, -7.234], [0, -8.315], [0, 3.198], [-3.748, 0], [-1.648, -7.799], [2.937, 5.183]], "o": [[2.092, -1.416], [0, -2.139], [-3.982, 0], [-3.308, 10.803], [0, 11.862], [0, -16.684], [3.159, 0], [0.465, -7.791], [-3.766, -6.647]], "v": [[7.35, -17.783], [13.68, -24.332], [1.75, -31.995], [-11.484, -21.834], [-12.754, 20.133], [-2.706, 28.07], [4.239, 3.321], [14.327, 15.636], [8.375, -0.764]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.241727133358, 0.241727133358, 0.249287668864, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.65, 32.245]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.281, 1.734], [5.017, 0], [0, -5.389], [0, -0.055], [0, 0], [0, 0], [-1.249, 0.048], [-0.507, 0.405], [0.619, 3.901]], "o": [[-0.783, -4.836], [-5.389, 0], [0, 0.056], [0, 0], [0, 0], [0, 4.066], [0.649, -0.022], [3.356, -2.681], [-0.366, -2.298]], "v": [[9.395, -5.668], [-0.455, -12.724], [-10.213, -2.965], [-10.205, -2.801], [-10.213, -2.801], [-10.213, 2.63], [1.967, 12.676], [3.75, 12.026], [9.594, -0.02]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.684361237171, 0.539330994849, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.048, 18.176]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.392, 1.865], [0, 0], [2.072, 0.193], [1.416, -1.273], [0, -6.976], [-8.265, 7.779], [0, 0], [0, 4.097]], "o": [[-5.137, -2.825], [0, 0], [-1.613, -0.16], [-8.046, 7.227], [0, 0], [0, 0], [0, 0], [0, -4.146]], "v": [[8.535, -16.072], [0.318, -20.964], [-3.465, -21.977], [-8.336, -20.647], [-7.745, 11.294], [11.378, 14.357], [12.762, 2.487], [16.382, -2.577]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.632, 51.138]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[9.448, 6.542], [0, 0], [0, 0], [0.089, 0.08], [0, 0], [-10.169, -9.109], [-12.247, 10.276]], "o": [[-2.855, -1.979], [0, 0], [-8.221, 7.736], [0, 0], [0, 0], [9.977, 8.936], [0, 0]], "v": [[13.831, -10.709], [4.096, -18.891], [2.71, -7.022], [-16.404, -10.084], [-16.409, -10.083], [-13.109, 9.954], [20.904, 3.891]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.297, 72.514]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-11.892, 15.318], [14.805, 1.609], [-14.805, -15.318]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.062, 80.472]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.342, -1.354], [0, 0], [0, 0], [0.225, -10.366], [1.838, -2.128], [-2.29, -2.576], [-2.699, 2.218], [0.939, 0.427], [0.346, 2.942], [0, 9.238]], "o": [[0, 0], [0, 0], [0, 0], [-0.225, 10.365], [0, 0], [1.625, 1.823], [0.798, -0.652], [-2.08, -0.944], [-0.44, -3.712], [0, -12.782]], "v": [[-12.026, -25.06], [-2.77, -10.225], [-0.017, -12], [-3.661, 6.12], [-5.854, 24.255], [-0.451, 29.138], [8.494, 30.681], [8.141, 28.311], [2.766, 22.584], [12.026, -20.117]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.684361237171, 0.539330994849, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.884, 86.523]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.487, -0.044], [0, 0], [4.679, 2.175], [1.227, 0], [-2.934, -5.147], [0, 0], [-0.78, 0.91], [0, 0], [0, 0], [-1.266, 0.635], [-0.054, 0.358]], "o": [[-3.907, 0.356], [0, 0], [-3.264, -1.513], [-0.028, 0.02], [2.944, 5.165], [0, 0], [0, 0], [0, 0], [1.328, 0.495], [5.333, -2.69], [0.074, -0.503]], "v": [[11.462, -6.332], [3.628, -4.536], [-0.553, -1.266], [-9.069, -7.93], [-9.501, -0.125], [-6.158, 7.303], [-4.462, 7.02], [-6.185, 0.458], [1.434, 3.947], [5.346, 3.288], [12.361, -5.554]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [52.099, 118.708]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.343, -1.355], [0, 0], [0, 0], [0.225, -10.363], [1.839, -2.125], [-2.291, -2.574], [-2.701, 2.216], [0.939, 0.425], [0.347, 2.941], [0, 9.235]], "o": [[0, 0], [0, 0], [0, 0], [-0.225, 10.364], [0, 0], [1.623, 1.826], [0.797, -0.654], [-2.083, -0.947], [-0.439, -3.712], [0, -9.236]], "v": [[-10.314, -24.205], [-4.48, -11.996], [-1.727, -13.775], [-5.371, 4.347], [-7.565, 22.48], [-2.159, 27.363], [6.785, 28.909], [6.433, 26.54], [1.056, 20.812], [10.314, -21.889]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.684361237171, 0.539330994849, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.858, 75.815]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.484, -0.043], [0, 0], [4.677, 2.172], [1.231, 0], [-2.935, -5.146], [0, 0], [-0.78, 0.91], [0, 0], [0, 0], [-1.267, 0.638], [-0.052, 0.358]], "o": [[-3.909, 0.353], [0, 0], [-3.266, -1.516], [-0.024, 0.023], [2.942, 5.162], [0, 0], [0, 0], [0, 0], [1.33, 0.496], [5.332, -2.69], [0.075, -0.504]], "v": [[11.463, -6.329], [3.626, -4.533], [-0.551, -1.263], [-9.072, -7.93], [-9.499, -0.125], [-6.158, 7.307], [-4.464, 7.02], [-6.186, 0.458], [1.43, 3.946], [5.346, 3.288], [12.359, -5.554]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.364, 106.225]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Small Code Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [170.331, 138.726, 0], "to": [0, -1.574, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [170.331, 123.726, 0], "to": [0, 0, 0], "ti": [0, -2.5, 0]}, {"t": 148, "s": [170.331, 138.726, 0]}]}, "a": {"a": 0, "k": [34.347, 41.142, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.568, 12.708], [1.389, 12.809], [-3.568, -12.708], [-1.39, -12.809]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.57, 44.583]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.029, 0.457], [-0.233, 0.188]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.194, -0.458], [0.031, -0.452], [0, 0]], "v": [[1.961, -7.574], [3.13, -4.84], [-0.63, -1.814], [2.74, 6.148], [0.966, 7.574], [-2.847, -1.409], [-3.101, -2.828], [-2.686, -3.835]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.798, 40.752]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.489, -0.394], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0.401, 0.945], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.783, -5.918], [-1.01, -7.345], [2.637, 1.249], [2.478, 3.677], [-2.083, 7.345], [-3.038, 4.919], [0.635, 1.964]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.028, 48.867]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.12, 0], [0.153, 0.398], [0, 0], [-0.516, 0.199], [-0.199, -0.515], [0, 0], [0.516, -0.2]], "o": [[-0.401, 0], [0, 0], [-0.198, -0.515], [0.514, -0.195], [0, 0], [0.198, 0.515], [-0.118, 0.045]], "v": [[0.833, 3.227], [-0.101, 2.586], [-1.767, -1.739], [-1.191, -3.032], [0.101, -2.458], [1.767, 1.868], [1.192, 3.161]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [59.882, 30.905]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.182, 0], [0.19, 0.302], [-0.468, 0.294], [0, 0], [-0.293, -0.466], [0.467, -0.294], [0, 0]], "o": [[-0.333, 0], [-0.294, -0.468], [0, 0], [0.469, -0.292], [0.294, 0.468], [0, 0], [-0.166, 0.103]], "v": [[-1.493, 2.009], [-2.341, 1.541], [-2.026, 0.162], [0.962, -1.717], [2.341, -1.402], [2.027, -0.023], [-0.962, 1.856]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [59.86, 30.83]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.617, 1.921], [-6.617, -5.76], [-6.607, -1.916], [6.617, 5.76]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.287, 13.596]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-22.153, -18.374], [22.122, 7.581], [22.153, 18.375], [-22.123, -7.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.809, 20.991]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.756, -4.089], [1.726, -6.706], [1.757, 4.089], [-1.726, 6.706]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [66.688, 32.659]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-23.879, -11.669], [-20.396, -14.286], [23.879, 11.669], [20.396, 14.286]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.535, 14.286]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-22.294, -31.845], [-22.017, 5.805], [22.294, 31.845], [22.018, -5.805]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.695, 33.104]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.021, 7.118], [0, 0], [-4.062, -2.36], [0, 0]], "o": [[0, 0], [0.02, 7.119], [0, 0], [-4.062, -2.36]], "v": [[18.855, 5.774], [-25.458, -20.265], [-18.854, -5.773], [25.458, 20.265]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65023354923, 0.791470336914, 0.814217840456, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.458, 61.814]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.222, 1.872], [0, 0], [0, 16.465], [0, 0]], "o": [[0, 0], [3.218, 1.87], [0, 0], [0, 16.415]], "v": [[-30.447, -4.692], [13.865, 21.348], [30.447, 4.168], [-13.863, -21.871]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.542, 60.413]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [13.5, -0.677], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.001, -22.768], [7.001, 6.068], [-7.001, 22.768], [4.887, -21.18]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.443, 59.028]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Tool Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [0]}, {"t": 149, "s": [-6]}]}, "p": {"a": 0, "k": [195.459, 156.814, 0]}, "a": {"a": 0, "k": [6.261, 129.02, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [1.322, -6.386], [2.336, -1.637], [-1.151, 5.533], [3.001, 5.132]], "o": [[3.001, 5.133], [-0.691, 3.317], [4.032, -0.903], [1.322, -6.387], [-0.009, 0.111]], "v": [[-3.552, -12.031], [-0.619, 6.786], [-5.606, 14.204], [4.284, 4.613], [1.351, -14.204]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.666, 24.363]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.324, -1.549], [0.685, 0.002], [0.254, 0.188], [-0.322, 1.548], [-0.687, -0.004], [-0.253, -0.188]], "o": [[-0.236, 1.137], [-0.25, -0.002], [-0.946, -0.708], [0.235, -1.135], [0.25, 0], [0.946, 0.708]], "v": [[-13.808, 60.307], [-15.344, 62.107], [-16.106, 61.827], [-17.24, 57.741], [-15.702, 55.942], [-14.94, 56.221]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.021, -0.109], [0, 0], [0.529, 0.003], [0.204, 0.152], [0, 0], [-0.247, 1.183], [0, 0], [0.129, 0.099], [1.31, -6.295], [-5.204, -5.253], [0, 0], [-2.455, -1.838], [-0.674, -0.003], [-0.6, 2.882], [0, 0], [-1.308, 6.29], [2.993, 5.135]], "o": [[0, 0], [-0.178, 0.849], [-0.209, -0.001], [0, 0], [-0.724, -0.543], [0, 0], [0.023, -0.109], [-3.968, -0.019], [-1.307, 6.29], [0, 0], [-0.832, 3.997], [0.684, 0.512], [1.738, 0.009], [0, 0], [4.289, -0.336], [1.325, -6.384], [-0.013, 0.112]], "v": [[16.326, -56.394], [13.022, -40.493], [11.828, -39.177], [11.202, -39.404], [0.131, -47.305], [-0.747, -50.412], [2.562, -66.333], [2.457, -66.662], [-6.245, -56.662], [-1.168, -33.439], [-19.729, 55.702], [-17.011, 65.91], [-14.952, 66.672], [-11.061, 62.185], [7.743, -28.122], [19.236, -37.96], [16.307, -56.776]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.811, 69.109]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.001, 0.001]], "o": [[0, 0], [0, 0]], "v": [[-0.001, 0.001], [0.001, 0]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.415, 135.322]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.021, -0.086], [0.045, -0.156], [0.007, -0.021], [0.042, -0.12], [0.013, -0.037], [0.043, -0.11], [0.006, -0.015], [0.055, -0.113], [0.016, -0.031], [0.043, -0.076], [0.019, -0.032], [0.054, -0.082], [0.01, -0.017], [0.07, -0.092], [0.015, -0.02], [0.087, -0.096], [0.001, -0.001], [0.043, -0.042], [0.019, -0.017], [0.044, -0.038], [0.018, -0.015], [0.131, -0.084], [0, 0], [-0.064, 0.048], [-0.016, 0.012], [-0.048, 0.039], [-0.027, 0.023], [-0.036, 0.034], [-0.028, 0.029], [-0.031, 0.034], [-0.001, 0], [-0.084, 0.107], [-0.01, 0.013], [-0.006, 0.009], [-0.065, 0.101], [-0.002, 0.003], [-0.008, 0.013], [-0.05, 0.088], [-0.009, 0.015], [-0.009, 0.018], [-0.04, 0.08], [-0.01, 0.021], [-0.005, 0.011], [-0.049, 0.12], [-0.002, 0.003], [-0.005, 0.013], [-0.041, 0.115], [-0.007, 0.018], [-0.008, 0.02], [-0.037, 0.127], [-0.004, 0.016], [-0.001, 0.005], [-0.038, 0.164], [-0.004, 0.019], [-0.014, 0.068], [0, 0], [0, 0]], "o": [[-0.019, 0.087], [-0.038, 0.163], [-0.005, 0.022], [-0.037, 0.128], [-0.013, 0.038], [-0.04, 0.115], [-0.006, 0.015], [-0.05, 0.12], [-0.015, 0.03], [-0.041, 0.081], [-0.017, 0.033], [-0.05, 0.087], [-0.01, 0.015], [-0.065, 0.099], [-0.015, 0.021], [-0.084, 0.107], [-0.001, 0], [-0.041, 0.044], [-0.017, 0.019], [-0.043, 0.042], [-0.019, 0.016], [-0.125, 0.106], [0, 0], [0.067, -0.042], [0.016, -0.012], [0.048, -0.036], [0.026, -0.023], [0.035, -0.031], [0.029, -0.028], [0.031, -0.031], [0, 0], [0.087, -0.095], [0.009, -0.013], [0.007, -0.008], [0.07, -0.091], [0.003, -0.003], [0.007, -0.013], [0.053, -0.081], [0.009, -0.013], [0.009, -0.018], [0.042, -0.077], [0.01, -0.022], [0.005, -0.011], [0.055, -0.111], [0.002, -0.002], [0.003, -0.012], [0.044, -0.109], [0.005, -0.017], [0.007, -0.02], [0.041, -0.121], [0.006, -0.017], [0.002, -0.005], [0.045, -0.155], [0.004, -0.018], [0.015, -0.067], [0, 0], [0, 0], [0, 0]], "v": [[-10.86, 44.79], [-10.919, 45.049], [-11.043, 45.529], [-11.063, 45.592], [-11.18, 45.962], [-11.219, 46.074], [-11.347, 46.411], [-11.366, 46.456], [-11.522, 46.803], [-11.568, 46.895], [-11.693, 47.131], [-11.746, 47.229], [-11.9, 47.482], [-11.93, 47.53], [-12.133, 47.816], [-12.18, 47.878], [-12.438, 48.184], [-12.44, 48.186], [-12.568, 48.312], [-12.621, 48.368], [-12.753, 48.486], [-12.807, 48.535], [-13.194, 48.818], [-7.947, 45.519], [-7.75, 45.385], [-7.701, 45.347], [-7.557, 45.234], [-7.478, 45.163], [-7.371, 45.068], [-7.286, 44.982], [-7.19, 44.885], [-7.188, 44.883], [-6.931, 44.577], [-6.902, 44.543], [-6.883, 44.516], [-6.68, 44.229], [-6.673, 44.221], [-6.65, 44.181], [-6.497, 43.928], [-6.471, 43.887], [-6.443, 43.831], [-6.319, 43.596], [-6.286, 43.533], [-6.272, 43.501], [-6.117, 43.155], [-6.112, 43.148], [-6.098, 43.109], [-5.969, 42.773], [-5.95, 42.723], [-5.93, 42.661], [-5.813, 42.292], [-5.797, 42.243], [-5.793, 42.228], [-5.669, 41.747], [-5.655, 41.695], [-5.611, 41.491], [13.194, -48.818], [7.944, -45.518]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.61, 86.504]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [], "o": [], "v": [], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.5, 0.5]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.032, -0.011], [0.008, -0.002], [0.046, -0.007], [0.007, -0.001], [0.05, 0.001], [0.061, 0.011], [0.005, 0.001], [0.061, 0.023], [0, 0], [0.062, 0.034], [0.003, 0.002], [0.061, 0.046], [0.047, 0.044], [0.018, 0.017], [0.032, 0.036], [0.017, 0.02], [0.025, 0.032], [0.026, 0.037], [0.023, 0.034], [0.023, 0.04], [0.02, 0.038], [0.02, 0.042], [0.017, 0.039], [0.018, 0.046], [0.012, 0.035], [0.025, 0.077], [0.01, 0.033], [0.023, 0.102], [0.002, 0.008], [0.02, 0.161], [0.003, 0.025], [0.004, 0.128], [0, 0.033], [-0.001, 0.079], [-0.004, 0.044], [-0.005, 0.056], [-0.006, 0.049], [-0.008, 0.044], [-0.016, 0.077], [-0.023, 0.082], [-0.003, 0.012], [-0.027, 0.076], [-0.008, 0.018], [-0.027, 0.058], [-0.006, 0.013], [-0.036, 0.06], [-0.007, 0.014], [-0.032, 0.045], [-0.009, 0.012], [-0.041, 0.044], [-0.003, 0.005], [-0.034, 0.03], [-0.017, 0.013], [-0.054, 0.033], [0, 0], [0.051, -0.043], [0.018, -0.016], [0.03, -0.033], [0, 0], [0.004, -0.004], [0.033, -0.044], [0.004, -0.006], [0.009, -0.014], [0.017, -0.025], [0.013, -0.023], [0.008, -0.014], [0.005, -0.01], [0.024, -0.05], [0.003, -0.007], [0.006, -0.014], [0.015, -0.033], [0.009, -0.027], [0.007, -0.019], [0.004, -0.012], [0.018, -0.065], [0, -0.003], [0.004, -0.013], [0.014, -0.059], [0.005, -0.027], [0.009, -0.055], [0.004, -0.023], [0.006, -0.044], [0.003, -0.027], [0.001, -0.023], [0.004, -0.058], [0.001, -0.026], [0.001, -0.019], [-0.001, -0.08], [-0.001, -0.026], [-0.001, -0.008], [-0.013, -0.128], [-0.001, -0.009], [-0.003, -0.014], [-0.033, -0.156], [0, -0.001], [-0.001, -0.009], [-0.028, -0.1], [-0.01, -0.033], [-0.029, -0.073], [-0.008, -0.019], [-0.005, -0.015], [-0.019, -0.044], [-0.019, -0.038], [-0.023, -0.042], [-0.021, -0.036], [-0.024, -0.038], [-0.022, -0.034], [-0.026, -0.035], [-0.018, -0.023], [-0.007, -0.008], [-0.017, -0.018], [-0.033, -0.034], [-0.019, -0.016], [-0.013, -0.011], [-0.037, -0.027], [-0.019, -0.014], [-0.042, -0.023], [-0.002, -0.001], [-0.002, -0.003], [-0.03, -0.014], [-0.03, -0.011], [0, 0], [-0.001, -0.002], [-0.026, -0.009], [-0.026, -0.005], [-0.009, -0.003], [-0.004, 0], [-0.011, -0.002], [-0.006, 0.002], [-0.013, 0.003], [-0.029, 0.009], [-0.003, 0.001], [-0.007, 0.002], [-0.026, 0.009], [-0.007, 0.003], [-0.058, 0.037], [0, 0], [0.061, -0.025]], "o": [[-0.007, 0.002], [-0.045, 0.014], [-0.005, 0.001], [-0.048, 0.007], [-0.06, 0], [-0.004, 0], [-0.061, -0.011], [-0.001, 0], [-0.062, -0.022], [-0.001, -0.002], [-0.062, -0.035], [-0.051, -0.038], [-0.019, -0.017], [-0.034, -0.034], [-0.017, -0.018], [-0.024, -0.03], [-0.027, -0.035], [-0.023, -0.033], [-0.024, -0.039], [-0.021, -0.036], [-0.022, -0.041], [-0.02, -0.039], [-0.019, -0.045], [-0.013, -0.034], [-0.028, -0.074], [-0.01, -0.033], [-0.028, -0.099], [-0.002, -0.009], [-0.033, -0.156], [-0.002, -0.024], [-0.013, -0.125], [-0.002, -0.033], [-0.001, -0.079], [0.003, -0.044], [0.003, -0.057], [0.004, -0.05], [0.006, -0.044], [0.011, -0.078], [0.018, -0.087], [0.003, -0.013], [0.023, -0.082], [0.007, -0.018], [0.022, -0.062], [0.006, -0.013], [0.033, -0.067], [0.008, -0.014], [0.031, -0.049], [0.009, -0.013], [0.038, -0.051], [0.004, -0.005], [0.031, -0.034], [0.016, -0.016], [0.051, -0.043], [0, 0], [-0.053, 0.033], [-0.018, 0.015], [-0.031, 0.028], [0, 0], [-0.003, 0.005], [-0.035, 0.039], [-0.006, 0.006], [-0.009, 0.013], [-0.017, 0.023], [-0.015, 0.022], [-0.008, 0.013], [-0.005, 0.009], [-0.026, 0.046], [-0.003, 0.006], [-0.007, 0.013], [-0.015, 0.031], [-0.01, 0.027], [-0.007, 0.018], [-0.004, 0.011], [-0.021, 0.062], [-0.001, 0.003], [-0.004, 0.013], [-0.015, 0.056], [-0.006, 0.026], [-0.012, 0.056], [-0.003, 0.022], [-0.007, 0.043], [-0.003, 0.027], [-0.003, 0.023], [-0.006, 0.057], [-0.001, 0.026], [-0.001, 0.019], [-0.002, 0.078], [0.001, 0.025], [0, 0.008], [0.004, 0.128], [0.002, 0.008], [0.001, 0.014], [0.018, 0.162], [0.001, 0.001], [0.002, 0.008], [0.023, 0.102], [0.01, 0.033], [0.025, 0.077], [0.006, 0.02], [0.005, 0.016], [0.019, 0.046], [0.017, 0.04], [0.02, 0.043], [0.02, 0.036], [0.023, 0.04], [0.022, 0.034], [0.026, 0.036], [0.018, 0.024], [0.007, 0.008], [0.017, 0.019], [0.032, 0.036], [0.018, 0.017], [0.012, 0.012], [0.035, 0.031], [0.019, 0.014], [0.042, 0.028], [0.002, 0.001], [0.003, 0.002], [0.031, 0.016], [0.031, 0.014], [0.001, 0], [0.002, 0], [0.027, 0.009], [0.026, 0.006], [0.009, 0.002], [0.005, 0], [0.011, 0.002], [0.023, 0.004], [0.013, -0.001], [0.029, -0.005], [0.003, 0], [0.007, -0.001], [0.025, -0.009], [0.007, -0.003], [0.061, -0.026], [0, 0], [-0.057, 0.035], [-0.031, 0.014]], "v": [[3.645, 1.282], [3.623, 1.289], [3.486, 1.32], [3.469, 1.322], [3.321, 1.332], [3.14, 1.315], [3.126, 1.313], [2.944, 1.262], [2.942, 1.262], [2.756, 1.178], [2.748, 1.174], [2.561, 1.052], [2.414, 0.929], [2.359, 0.877], [2.26, 0.771], [2.21, 0.716], [2.136, 0.621], [2.057, 0.515], [1.991, 0.414], [1.919, 0.297], [1.858, 0.186], [1.794, 0.061], [1.74, -0.057], [1.682, -0.192], [1.643, -0.296], [1.565, -0.523], [1.534, -0.621], [1.456, -0.924], [1.449, -0.951], [1.37, -1.428], [1.361, -1.499], [1.334, -1.879], [1.332, -1.978], [1.332, -2.216], [1.34, -2.349], [1.352, -2.519], [1.367, -2.668], [1.386, -2.8], [1.426, -3.034], [1.488, -3.286], [1.498, -3.325], [1.575, -3.561], [1.597, -3.616], [1.671, -3.795], [1.688, -3.834], [1.789, -4.024], [1.813, -4.065], [1.906, -4.205], [1.934, -4.244], [2.052, -4.387], [2.063, -4.401], [2.161, -4.494], [2.21, -4.541], [2.366, -4.655], [-2.884, -1.355], [-3.04, -1.241], [-3.092, -1.191], [-3.186, -1.102], [-3.187, -1.101], [-3.198, -1.087], [-3.302, -0.963], [-3.316, -0.944], [-3.344, -0.904], [-3.395, -0.833], [-3.436, -0.764], [-3.461, -0.724], [-3.477, -0.697], [-3.552, -0.553], [-3.561, -0.534], [-3.579, -0.494], [-3.623, -0.398], [-3.653, -0.316], [-3.675, -0.26], [-3.688, -0.226], [-3.749, -0.034], [-3.751, -0.025], [-3.762, 0.014], [-3.806, 0.185], [-3.823, 0.266], [-3.855, 0.433], [-3.864, 0.501], [-3.882, 0.632], [-3.893, 0.712], [-3.897, 0.781], [-3.91, 0.952], [-3.916, 1.03], [-3.916, 1.085], [-3.917, 1.322], [-3.917, 1.398], [-3.915, 1.421], [-3.889, 1.803], [-3.886, 1.83], [-3.878, 1.872], [-3.801, 2.349], [-3.8, 2.352], [-3.794, 2.376], [-3.716, 2.679], [-3.685, 2.778], [-3.606, 3.004], [-3.586, 3.064], [-3.568, 3.109], [-3.51, 3.242], [-3.456, 3.361], [-3.391, 3.488], [-3.331, 3.597], [-3.259, 3.714], [-3.193, 3.816], [-3.114, 3.922], [-3.061, 3.993], [-3.04, 4.017], [-2.99, 4.072], [-2.891, 4.178], [-2.835, 4.229], [-2.797, 4.265], [-2.689, 4.352], [-2.632, 4.394], [-2.506, 4.472], [-2.501, 4.474], [-2.493, 4.479], [-2.402, 4.525], [-2.31, 4.562], [-2.309, 4.562], [-2.306, 4.564], [-2.228, 4.59], [-2.151, 4.608], [-2.124, 4.614], [-2.11, 4.615], [-2.078, 4.621], [-1.877, 4.653], [-1.723, 4.619], [-1.636, 4.595], [-1.627, 4.591], [-1.604, 4.583], [-1.529, 4.557], [-1.509, 4.546], [-1.331, 4.455], [3.918, 1.156], [3.741, 1.245]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.394319392186, 0.498574320475, 0.512799012427, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.395, 126.583]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.247, 1.177], [0, 0], [0.127, 0.094], [1.321, -6.267], [-3.215, -0.017], [0.025, -0.102], [0, 0], [-0.725, -0.546], [0, 0], [-0.204, 0], [-0.179, 0.845], [0, 0], [0, 0]], "o": [[0, 0], [0.025, -0.102], [-3.957, -0.026], [1.671, -4.28], [0.128, 0.094], [0, 0], [-0.247, 1.177], [0, 0], [0.204, 0.145], [0.529, 0.009], [0, 0], [0, 0], [-0.725, -0.546]], "v": [[-0.014, 1.432], [3.296, -14.494], [3.193, -14.818], [-9.452, -5.943], [-1.71, -12.645], [-1.607, -12.321], [-4.916, 3.606], [-4.038, 6.71], [7.038, 14.614], [7.661, 14.835], [8.854, 13.523], [9.452, 10.658], [0.866, 4.536]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456868459664, 0.468252383961, 0.468252383961, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.977, 15.095]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Gear Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [417.274, 167.542, 0], "to": [0, -1.667, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [417.274, 157.542, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 76, "s": [417.274, 167.542, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114, "s": [417.274, 157.542, 0], "to": [0, 0, 0], "ti": [0, -1.667, 0]}, {"t": 149, "s": [417.274, 167.542, 0]}]}, "a": {"a": 0, "k": [41.361, 53.847, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.465, 0.542], [12.965, -8.48], [2.466, -0.542], [-12.965, 8.48]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.973, 98.964]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.715, -5.388], [7.715, -14.41], [7.715, 5.388], [-7.715, 14.41]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [74.755, 73.127]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.18, 2.414], [0, 5.572], [-4.18, -2.412], [0, -5.573]], "o": [[-4.18, -2.412], [0, -5.572], [4.18, 2.414], [0, 5.573]], "v": [[0, 10.089], [-7.568, -4.369], [0, -10.09], [7.567, 4.369]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[9.072, 5.236], [0, -12.096], [-9.073, -5.238], [0, 12.096]], "o": [[-9.073, -5.239], [0, 12.096], [9.072, 5.238], [0, -12.097]], "v": [[0, -21.9], [-16.427, -9.483], [0, 21.901], [16.427, 9.484]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.645, 58.358]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -5.573], [4.179, 2.414], [0, 5.571], [-4.18, -2.413]], "o": [[0, 5.573], [-4.18, -2.412], [0, -5.573], [4.179, 2.414]], "v": [[7.568, 4.368], [0, 10.089], [-7.568, -4.37], [0, -10.091]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.936, 3.675], [0.079, 0.288], [4.811, 5.547], [0.186, 0.202], [0, 0], [2.686, 1.55], [2.351, 0.11], [0.18, 0], [0, 0], [2.199, -3.971], [0.28, -0.701], [0, 0], [0.073, -0.202], [0.244, -1.466], [0, 0], [0, -1.875], [-0.936, -3.676], [-0.081, -0.288], [0, 0], [-4.809, -5.543], [0, 0], [-0.188, -0.205], [0, 0], [-2.685, -1.55], [-2.352, -0.11], [0, 0], [-0.178, 0], [0, 0], [-2.077, 5.217], [0, 0], [-0.073, 0.202], [0, 3.578]], "o": [[-0.073, -0.286], [-2.075, -7.611], [-0.178, -0.207], [0, 0], [-2.35, -2.607], [-2.687, -1.55], [-0.183, -0.01], [0, 0], [-4.166, -0.007], [-0.343, 0.616], [0, 0], [-0.079, 0.197], [-0.447, 1.235], [0, 0], [-0.27, 1.614], [0, 3.58], [0.073, 0.288], [0, 0], [2.075, 7.609], [0, 0], [0.18, 0.212], [0, 0], [2.35, 2.604], [2.684, 1.55], [0, 0], [0.181, 0.01], [0, 0], [4.813, 0.008], [0, 0], [0.079, -0.195], [0.936, -2.595], [0, -3.58]], "v": [[19.121, 0.905], [18.893, 0.044], [8.147, -20.489], [7.602, -21.106], [7.599, -21.108], [0, -27.432], [-7.603, -29.885], [-8.144, -29.899], [-8.146, -29.899], [-17.964, -23.748], [-18.896, -21.773], [-18.896, -21.771], [-19.122, -21.173], [-20.161, -17.12], [-20.161, -17.118], [-20.572, -11.879], [-19.122, -0.907], [-18.892, -0.043], [-18.892, -0.041], [-8.152, 20.48], [-8.148, 20.484], [-7.598, 21.107], [-7.598, 21.109], [0, 27.429], [7.602, 29.883], [7.602, 29.883], [8.143, 29.898], [8.145, 29.898], [18.895, 21.771], [18.895, 21.769], [19.121, 21.172], [20.572, 11.878]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992416082644, 0.996211152918, 0.988625799441, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.645, 58.359]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.798, -6.053], [-0.299, -13.99], [10.798, 4.533], [-1.711, 13.99]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.581, 23.262]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.136, 4.831], [-7.136, -14.967], [7.136, -8.621], [7.136, 14.967]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.387, 44.146]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.299, 14.805], [-10.799, -5.256], [-1.712, -14.805], [10.799, 9.096]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.581, 71.434]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.425, 14.212], [-7.424, 5.64], [-8.846, -14.212], [8.846, -3.998]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.644, 92.957]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[10.799, 6.053], [0.299, 13.99], [-10.799, -4.533], [1.71, -13.99]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.709, 93.453]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.137, -4.832], [7.137, 14.967], [-7.137, 8.621], [-7.137, -14.967]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [59.902, 72.57]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.299, -14.806], [10.799, 5.255], [1.711, 14.806], [-10.799, -9.096]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.709, 45.282]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.424, -14.212], [7.425, -5.639], [8.846, 14.212], [-8.846, 3.998]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.645, 23.758]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -5.573], [4.179, 2.414], [0, 5.571], [-4.18, -2.413]], "o": [[0, 5.573], [-4.18, -2.412], [0, -5.573], [4.179, 2.414]], "v": [[7.568, 4.37], [0.001, 10.09], [-7.567, -4.368], [0.001, -10.089]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.862, 3.963], [0, 0], [0.566, 1.83], [0, 0], [5.042, 6.294], [0, 0], [1.215, 1.236], [0, 0], [2.888, 1.668], [2.453, 0.406], [0.168, 0.022], [1.124, -0.106], [0, 0], [0, 0], [1.835, -1.237], [1.395, -2.894], [0.402, -1.385], [0, -3.85], [-0.017, -0.533], [-0.744, -3.42], [0, 0], [-0.566, -1.83], [0, 0], [-5.042, -6.292], [-0.325, -0.382], [-0.561, -0.602], [-0.149, -0.157], [-0.004, -0.002], [-0.151, -0.155], [0, 0], [-2.889, -1.667], [-0.256, -0.139], [-2.366, -0.319], [-1.127, 0.104], [-1.609, 0.912], [-1.576, 3.267], [0, 0], [-0.402, 1.386], [0, 0], [0, 0], [0, 3.851]], "o": [[0, 0], [-0.402, -1.849], [0, 0], [-2.534, -8.187], [0, 0], [-1.125, -1.407], [0, 0], [-2.599, -2.649], [-2.701, -1.56], [-0.171, -0.029], [-1.211, -0.165], [0, 0], [0, 0], [-2.27, 0.211], [-2.241, 1.508], [-0.566, 1.176], [-0.862, 2.969], [0, 0.527], [0.11, 3.349], [0, 0], [0.402, 1.847], [0, 0], [2.534, 8.187], [0.315, 0.394], [0.539, 0.639], [0.147, 0.157], [0, 0.002], [0.151, 0.157], [0, 0], [2.599, 2.65], [0.26, 0.151], [2.61, 1.405], [1.213, 0.165], [1.911, -0.178], [2.636, -1.487], [0, 0], [0.566, -1.174], [0, 0], [0, 0], [0.86, -2.968], [0, -3.851]], "v": [[24.93, 3.372], [24.93, 3.37], [23.474, -2.155], [23.472, -2.155], [11.783, -24.494], [11.781, -24.494], [8.268, -28.464], [8.268, -28.466], [0.001, -35.009], [-7.759, -37.935], [-8.267, -38.011], [-11.774, -38.097], [-11.779, -38.097], [-11.78, -38.097], [-17.963, -35.904], [-23.473, -29.26], [-24.929, -25.415], [-26.258, -15.159], [-26.232, -13.568], [-24.929, -3.37], [-24.929, -3.368], [-23.473, 2.158], [-23.471, 2.158], [-11.78, 24.496], [-10.818, 25.662], [-9.168, 27.522], [-8.725, 27.992], [-8.72, 27.998], [-8.267, 28.464], [-8.267, 28.466], [0.001, 35.011], [0.776, 35.444], [8.268, 38.013], [11.781, 38.099], [17.078, 36.453], [23.474, 29.262], [23.474, 29.26], [24.93, 25.417], [24.932, 25.417], [24.932, 25.415], [26.258, 15.161]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.644, 58.357]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.965, -5.519], [2.465, -14.541], [12.965, 5.52], [-2.465, 14.541]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.974, 35.996]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-11.007, 6.204], [-4.424, 2.818], [11.007, -6.204], [4.423, -2.817]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [56.433, 27.659]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.127, -1.406]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [1.213, 1.236], [0, 0]], "v": [[-5.379, 6.184], [-6.588, 6.807], [-10.051, 2.798], [-8.893, 2.215], [6.538, -6.807], [10.051, -2.837]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.805, 27.679]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-15.14, 0.224], [0.292, -8.798], [15.14, -0.225], [-0.291, 8.798]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.361, 9.322]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-11.008, -0.984], [4.423, -10.006], [11.008, 0.984], [-4.424, 10.006]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.289, 10.256]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.567, -1.175], [0, 0], [0.067, 0.009], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.402, 1.384], [0, 0], [-0.067, -0.007], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.337, 2.951], [-6.527, 2.59], [8.902, -6.433], [7.447, -2.591], [-7.557, 6.183], [-7.759, 6.162], [-7.77, 6.308], [-7.984, 6.433], [-8.902, 5.166]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [65.102, 81.184]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.02, 10.456], [10.411, 1.435], [5.02, -10.456], [-10.411, -1.432]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [67.528, 89.05]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.401, -1.849], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.567, 1.83], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.887, -7.533], [9.343, -2.008], [-6.088, 7.014], [-9.343, 7.533], [-9.343, 2.469], [-7.544, 1.489]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851184740254, 0.918480308383, 0.646445839078, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.661, 54.714]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 21", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.336, 0.563], [2.336, -2.167], [1.45, 1.757], [-1.375, 2.167]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.201, 19.698]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 22", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.291, 8.797], [15.14, -0.225], [0.292, -8.797], [-15.14, 0.225]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.36, 98.372]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.716, -5.388], [7.716, -14.41], [7.716, 5.388], [-7.716, 14.41]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.967, 34.567]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.18, 2.414], [0, 5.572], [-4.18, -2.412], [0, -5.573]], "o": [[-4.18, -2.412], [0, -5.572], [4.18, 2.414], [0, 5.573]], "v": [[-7.715, 14.6], [-15.284, 0.142], [-7.715, -5.58], [-0.148, 8.88]], "c": true}}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.566, 1.828], [0, 0], [0, 0], [0, 0], [1.213, 1.237], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.566, -1.83], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.323, -0.385], [-0.56, -0.601], [-0.149, -0.156], [-0.004, -0.002], [-0.15, -0.155], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.401, -1.85], [0, 0], [0, 0], [0, 0], [-1.127, -1.406], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.402, 1.848], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.318, 0.395], [0.539, 0.638], [0.147, 0.157], [0, 0.002], [0.151, 0.157], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[40.439, 4.395], [40.165, 4.201], [32.643, -1.142], [31.188, -6.666], [36.578, -12.333], [26.078, -32.393], [19.495, -29.006], [15.983, -32.977], [15.141, -44.752], [0.29, -53.322], [-7.689, -48.657], [-10.647, -53.597], [-26.078, -44.575], [-36.577, -36.639], [-33.237, -29.271], [-41.111, -24.668], [-41.111, -4.87], [-32.646, 1.139], [-32.646, 1.142], [-32.643, 1.144], [-31.188, 6.665], [-31.188, 6.668], [-31.19, 6.668], [-36.577, 12.333], [-26.078, 32.392], [-19.496, 29.007], [-19.495, 29.005], [-18.534, 30.173], [-16.884, 32.032], [-16.441, 32.502], [-16.435, 32.508], [-15.983, 32.975], [-15.983, 32.976], [-15.141, 44.749], [-0.292, 53.323], [7.689, 48.657], [10.647, 53.597], [26.078, 44.575], [35.513, 37.44], [35.516, 37.44], [36.578, 36.637], [36.168, 35.734], [33.238, 29.271], [33.238, 29.269], [41.111, 24.668], [41.111, 4.871]], "c": true}}, "nm": "Path 2", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5469255036, 0.62085278081, 0.33839934106, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.36, 53.847]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 25", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Background Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [294.863, 202.474, 0]}, "a": {"a": 0, "k": [193.315, 155.779, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 75, "s": [88, 88, 100]}, {"t": 149, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[5.338, -3.635], [-8.176, 2.92], [-4.509, 1.052], [-0.001, 1.843], [6.246, -0.814]], "o": [[-6.474, 4.409], [4.406, -1.573], [1.458, -0.341], [0.002, -4.884], [-5.035, 0.656]], "v": [[65.253, 144.937], [73.503, 150.36], [86.642, 146.181], [96.913, 142.224], [80.738, 139.307]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[5.338, -3.635], [-8.176, 2.92], [-4.509, 1.052], [-0.001, 1.843], [6.246, -0.814]], "o": [[-6.474, 4.409], [4.406, -1.573], [1.458, -0.341], [0.002, -4.884], [-5.035, 0.656]], "v": [[65.253, 144.937], [73.503, 150.36], [88.142, 147.681], [96.913, 142.224], [80.738, 139.307]], "c": true}]}, {"t": 149, "s": [{"i": [[5.338, -3.635], [-8.176, 2.92], [-4.509, 1.052], [-0.001, 1.843], [6.246, -0.814]], "o": [[-6.474, 4.409], [4.406, -1.573], [1.458, -0.341], [0.002, -4.884], [-5.035, 0.656]], "v": [[65.253, 144.937], [73.503, 150.36], [86.642, 146.181], [96.913, 142.224], [80.738, 139.307]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ind": 1, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[7.179, -1.34], [0.15, -0.968], [-0.7, -0.486], [-7.18, -0.093], [-0.772, 0.303], [0.716, 1.39], [0.435, 0.394]], "o": [[-1.039, 0.194], [-0.124, 0.799], [5.735, 3.983], [0.839, 0.011], [1.535, -0.602], [-0.26, -0.508], [-5.769, -5.227]], "v": [[-77.991, 147.632], [-80.422, 149.213], [-79.122, 151.181], [-58.488, 155.512], [-56.01, 155.226], [-54.453, 151.351], [-55.56, 150.022]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[7.179, -1.34], [0.15, -0.968], [-0.7, -0.486], [-7.18, -0.093], [-0.772, 0.303], [0.716, 1.39], [0.435, 0.394]], "o": [[-1.039, 0.194], [-0.124, 0.799], [5.735, 3.983], [0.839, 0.011], [1.535, -0.602], [-0.26, -0.508], [-5.769, -5.227]], "v": [[-72.491, 135.632], [-80.422, 149.213], [-79.122, 151.181], [-58.488, 155.512], [-54.51, 155.726], [-54.453, 151.351], [-52.06, 140.522]], "c": true}]}, {"t": 149, "s": [{"i": [[7.179, -1.34], [0.15, -0.968], [-0.7, -0.486], [-7.18, -0.093], [-0.772, 0.303], [0.716, 1.39], [0.435, 0.394]], "o": [[-1.039, 0.194], [-0.124, 0.799], [5.735, 3.983], [0.839, 0.011], [1.535, -0.602], [-0.26, -0.508], [-5.769, -5.227]], "v": [[-77.991, 147.632], [-80.422, 149.213], [-79.122, 151.181], [-58.488, 155.512], [-56.01, 155.226], [-54.453, 151.351], [-55.56, 150.022]], "c": true}]}]}, "nm": "Path 2", "hd": false}, {"ind": 2, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[1.757, -1.441], [-0.519, -2.783], [-0.567, -0.667], [-1.206, -0.326], [-1.633, 1.399], [0.002, 1.525], [0.158, 1.385], [0.382, 0.568], [0.853, 0.336]], "o": [[-1.758, 1.441], [0.156, 0.835], [0.775, 0.911], [2.139, 0.577], [1.213, -1.038], [-0.002, -1.393], [-0.077, -0.664], [-0.49, -0.727], [-3.061, -1.206]], "v": [[-185.403, 41.981], [-185.057, 50.423], [-184.127, 52.788], [-180.861, 54.399], [-174.495, 54.067], [-173.114, 49.813], [-173.355, 45.642], [-173.917, 43.723], [-176.13, 42.236]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[1.757, -1.441], [-0.519, -2.783], [-0.567, -0.667], [-1.206, -0.326], [-1.633, 1.399], [0.002, 1.525], [0.158, 1.385], [0.382, 0.568], [0.853, 0.336]], "o": [[-1.758, 1.441], [0.156, 0.835], [0.775, 0.911], [2.139, 0.577], [1.213, -1.038], [-0.002, -1.393], [-0.077, -0.664], [-0.49, -0.727], [-3.061, -1.206]], "v": [[-185.403, 41.981], [-185.057, 50.423], [-184.127, 52.788], [-180.861, 54.399], [-174.495, 54.067], [-173.114, 49.813], [-173.355, 45.642], [-173.917, 43.723], [-173.13, 34.236]], "c": true}]}, {"t": 149, "s": [{"i": [[1.757, -1.441], [-0.519, -2.783], [-0.567, -0.667], [-1.206, -0.326], [-1.633, 1.399], [0.002, 1.525], [0.158, 1.385], [0.382, 0.568], [0.853, 0.336]], "o": [[-1.758, 1.441], [0.156, 0.835], [0.775, 0.911], [2.139, 0.577], [1.213, -1.038], [-0.002, -1.393], [-0.077, -0.664], [-0.49, -0.727], [-3.061, -1.206]], "v": [[-185.403, 41.981], [-185.057, 50.423], [-184.127, 52.788], [-180.861, 54.399], [-174.495, 54.067], [-173.114, 49.813], [-173.355, 45.642], [-173.917, 43.723], [-176.13, 42.236]], "c": true}]}]}, "nm": "Path 3", "hd": false}, {"ind": 3, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-0.347, -2.819], [-0.853, -1.083], [-2.171, -1.629], [-1.184, -0.038], [-0.874, 0.583], [-0.294, 2.409], [1.92, 2.815], [1.99, 0.934], [0.933, -2.75]], "o": [[0.164, 1.33], [1.645, 2.088], [0.919, 0.689], [1.078, 0.035], [2.129, -1.421], [0.404, -3.304], [-1.189, -1.743], [-3.337, -1.567], [-0.858, 2.532]], "v": [[169.67, -34.954], [171.568, -31.393], [177.055, -25.562], [180.225, -24.218], [183.211, -25.221], [187.071, -31.321], [183.904, -40.657], [179.336, -45.072], [172.862, -42.521]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[-0.347, -2.819], [-0.853, -1.083], [-2.171, -1.629], [-1.184, -0.038], [-0.874, 0.583], [-0.294, 2.409], [1.92, 2.815], [1.99, 0.934], [0.933, -2.75]], "o": [[0.164, 1.33], [1.645, 2.088], [0.919, 0.689], [1.078, 0.035], [2.129, -1.421], [0.404, -3.304], [-1.189, -1.743], [-3.337, -1.567], [-0.858, 2.532]], "v": [[169.67, -34.954], [171.568, -31.393], [177.055, -25.562], [180.225, -24.218], [183.211, -25.221], [187.071, -31.321], [183.904, -40.657], [179.336, -45.072], [160.362, -41.021]], "c": true}]}, {"t": 149, "s": [{"i": [[-0.347, -2.819], [-0.853, -1.083], [-2.171, -1.629], [-1.184, -0.038], [-0.874, 0.583], [-0.294, 2.409], [1.92, 2.815], [1.99, 0.934], [0.933, -2.75]], "o": [[0.164, 1.33], [1.645, 2.088], [0.919, 0.689], [1.078, 0.035], [2.129, -1.421], [0.404, -3.304], [-1.189, -1.743], [-3.337, -1.567], [-0.858, 2.532]], "v": [[169.67, -34.954], [171.568, -31.393], [177.055, -25.562], [180.225, -24.218], [183.211, -25.221], [187.071, -31.321], [183.904, -40.657], [179.336, -45.072], [172.862, -42.521]], "c": true}]}]}, "nm": "Path 4", "hd": false}, {"ind": 4, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-0.948, -0.305], [-0.954, 0.288], [0.165, 0.574], [0.273, 0.135], [1.262, -0.8]], "o": [[0.947, 0.304], [0.611, -0.185], [-0.079, -0.276], [-1.255, -0.625], [-1.261, 0.799]], "v": [[125.894, -115.46], [128.834, -115.725], [130.031, -116.891], [129.399, -117.486], [125.255, -117.772]], "c": true}]}, {"t": 149, "s": [{"i": [[-0.948, -0.305], [-0.954, 0.288], [0.165, 0.574], [0.273, 0.135], [1.262, -0.8]], "o": [[0.947, 0.304], [0.611, -0.185], [-0.079, -0.276], [-1.255, -0.625], [-1.261, 0.799]], "v": [[125.894, -115.46], [128.834, -115.725], [130.031, -116.891], [129.399, -117.486], [125.255, -117.772]], "c": true}]}]}, "nm": "Path 5", "hd": false}, {"ind": 5, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-3.787, -2.911], [-3.743, -0.178], [-0.771, 0.589], [0.845, 0.835], [0.721, 0.272], [0.697, 1.044], [0.335, 0.175]], "o": [[2.879, 2.213], [0.999, 0.048], [0.953, -0.73], [-0.533, -0.526], [-1.23, -0.465], [-0.202, -0.303], [-2.083, -1.091]], "v": [[107.56, -119.237], [117.849, -115.521], [120.706, -116.121], [120.917, -119.175], [118.887, -120.209], [115.754, -122.385], [115.043, -123.211]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[-3.787, -2.911], [-3.743, -0.178], [-0.771, 0.589], [0.845, 0.835], [0.721, 0.272], [0.697, 1.044], [0.335, 0.175]], "o": [[2.879, 2.213], [0.999, 0.048], [0.953, -0.73], [-0.533, -0.526], [-1.23, -0.465], [-0.202, -0.303], [-2.083, -1.091]], "v": [[107.56, -119.237], [117.849, -111.521], [120.706, -116.121], [120.917, -119.175], [118.887, -120.209], [115.754, -122.385], [115.043, -123.211]], "c": true}]}, {"t": 149, "s": [{"i": [[-3.787, -2.911], [-3.743, -0.178], [-0.771, 0.589], [0.845, 0.835], [0.721, 0.272], [0.697, 1.044], [0.335, 0.175]], "o": [[2.879, 2.213], [0.999, 0.048], [0.953, -0.73], [-0.533, -0.526], [-1.23, -0.465], [-0.202, -0.303], [-2.083, -1.091]], "v": [[107.56, -119.237], [117.849, -115.521], [120.706, -116.121], [120.917, -119.175], [118.887, -120.209], [115.754, -122.385], [115.043, -123.211]], "c": true}]}]}, "nm": "Path 6", "hd": false}, {"ind": 6, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[3.31, 0], [0, -2.36], [-3.303, -0.203], [0, 2.361]], "o": [[-3.31, 0], [0, 2.361], [10.764, 0.662], [0, -3.771]], "v": [[-1.754, -137.463], [-7.748, -133.189], [-1.754, -128.915], [9.01, -134.664]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[3.31, 0], [0, -2.36], [-3.303, -0.203], [0, 2.361]], "o": [[-3.31, 0], [0, 2.361], [10.764, 0.662], [0, -3.771]], "v": [[-10.754, -146.963], [-12.248, -138.189], [-2.754, -135.415], [2.51, -141.164]], "c": true}]}, {"t": 149, "s": [{"i": [[3.31, 0], [0, -2.36], [-3.303, -0.203], [0, 2.361]], "o": [[-3.31, 0], [0, 2.361], [10.764, 0.662], [0, -3.771]], "v": [[-1.754, -137.463], [-7.748, -133.189], [-1.754, -128.915], [9.01, -134.664]], "c": true}]}]}, "nm": "Path 7", "hd": false}, {"ind": 7, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-68.255, -4.252], [-5.918, 10.281], [-12.102, -17.332], [-14.953, 4.805], [-19.973, -5.651], [-15.902, 1.096], [6.283, -13.905], [-3.334, -9.338], [-6.779, -8.841], [3.371, -10.372], [5.34, -7.848], [22.098, -20.316], [22.133, -2.775], [18.373, -2.827], [37.72, 0.355], [12.869, 3.05], [5.957, 15.396], [9.286, 20.868], [-1.164, 18.344], [-5.765, 10.129]], "o": [[12.594, 0.785], [16.2, -28.147], [8.572, 12.276], [14.717, -4.729], [17.445, 4.936], [34.691, -2.389], [-4.101, 9.074], [3.672, 10.285], [6.78, 8.842], [-2.883, 8.876], [-16.584, 24.373], [-15.817, 14.541], [-18.457, 2.315], [-37.208, 5.725], [-13.287, -0.126], [-17.17, -4.069], [-8.22, -21.244], [-7.565, -16.999], [0.725, -11.419], [8.567, -15.055]], "v": [[-111.553, -109.887], [-87.771, -127.382], [-23.392, -135.484], [18.619, -130.538], [78.385, -134.159], [118.89, -112.512], [164.787, -69.116], [161.984, -40.552], [180.824, -13.284], [189.694, 16.982], [172.867, 39.847], [150.59, 121.889], [86.851, 135.849], [32.652, 148.206], [-79.852, 140.966], [-119.543, 139.168], [-157.282, 107.406], [-167, 40.709], [-191.902, -8.464], [-177.031, -39.566]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[-68.255, -4.252], [-5.918, 10.281], [-12.102, -17.332], [-14.953, 4.805], [-19.973, -5.651], [-15.902, 1.096], [6.283, -13.905], [-3.334, -9.338], [-6.779, -8.841], [3.371, -10.372], [5.34, -7.848], [22.098, -20.316], [22.133, -2.775], [18.373, -2.827], [37.72, 0.355], [12.869, 3.05], [5.957, 15.396], [9.286, 20.868], [-1.164, 18.344], [-5.765, 10.129]], "o": [[12.594, 0.785], [16.2, -28.147], [8.572, 12.276], [14.717, -4.729], [17.445, 4.936], [34.691, -2.389], [-4.101, 9.074], [3.672, 10.285], [6.78, 8.842], [-2.883, 8.876], [-16.584, 24.373], [-15.817, 14.541], [-18.457, 2.315], [-37.208, 5.725], [-13.287, -0.126], [-17.17, -4.069], [-8.22, -21.244], [-7.565, -16.999], [0.725, -11.419], [8.567, -15.055]], "v": [[-131.053, -91.887], [-103.272, -132.882], [-35.392, -123.984], [19.12, -140.538], [78.385, -134.159], [124.39, -101.012], [182.787, -65.116], [151.984, -39.052], [180.824, -13.284], [169.694, 17.482], [190.867, 65.347], [144.09, 114.889], [89.851, 123.349], [43.153, 139.206], [-37.352, 127.466], [-113.543, 124.168], [-151.782, 94.906], [-153, 28.709], [-191.902, -8.464], [-152.531, -34.566]], "c": true}]}, {"t": 149, "s": [{"i": [[-68.255, -4.252], [-5.918, 10.281], [-12.102, -17.332], [-14.953, 4.805], [-19.973, -5.651], [-15.902, 1.096], [6.283, -13.905], [-3.334, -9.338], [-6.779, -8.841], [3.371, -10.372], [5.34, -7.848], [22.098, -20.316], [22.133, -2.775], [18.373, -2.827], [37.72, 0.355], [12.869, 3.05], [5.957, 15.396], [9.286, 20.868], [-1.164, 18.344], [-5.765, 10.129]], "o": [[12.594, 0.785], [16.2, -28.147], [8.572, 12.276], [14.717, -4.729], [17.445, 4.936], [34.691, -2.389], [-4.101, 9.074], [3.672, 10.285], [6.78, 8.842], [-2.883, 8.876], [-16.584, 24.373], [-15.817, 14.541], [-18.457, 2.315], [-37.208, 5.725], [-13.287, -0.126], [-17.17, -4.069], [-8.22, -21.244], [-7.565, -16.999], [0.725, -11.419], [8.567, -15.055]], "v": [[-111.553, -109.887], [-87.771, -127.382], [-23.392, -135.484], [18.619, -130.538], [78.385, -134.159], [118.89, -112.512], [164.787, -69.116], [161.984, -40.552], [180.824, -13.284], [189.694, 16.982], [172.867, 39.847], [150.59, 121.889], [86.851, 135.849], [32.652, 148.206], [-79.852, 140.966], [-119.543, 139.168], [-157.282, 107.406], [-167, 40.709], [-191.902, -8.464], [-177.031, -39.566]], "c": true}]}]}, "nm": "Path 8", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936495852003, 0.959242996515, 0.963032382142, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [193.315, 155.779]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Code/Customization Graphics Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [40, 68, 0], "to": [0, -22.667, 0], "ti": [0, 22.667, 0]}, {"t": 149, "s": [40, -68, 0]}]}, "a": {"a": 0, "k": [31, 68, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -3.396], [4.079, 1.597], [4.079, 3.397], [-4.079, -1.595]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 128.712]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.379, -6.027], [8.379, 4.227], [8.379, 6.027], [-8.379, -4.227]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.105, 129.385]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.905, -2.677], [2.905, 0.879], [2.905, 2.677], [-2.905, -0.877]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.399, 131.856]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.776, -3.823], [4.776, 2.023], [4.776, 3.823], [-4.776, -2.024]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.209, 125.622]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.388, -3.585], [4.388, 1.785], [4.388, 3.585], [-4.388, -1.785]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.83, 120.038]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.428, -4.833], [6.428, 3.033], [6.428, 4.833], [-6.428, -3.033]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.869, 116.847]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -4.42], [5.752, 2.62], [5.752, 4.42], [-5.752, -2.62]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 111.276]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-13.165, -8.956], [13.165, 7.154], [13.165, 8.956], [-13.165, -7.156]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.598, 121.866]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.864, -2.653], [2.864, 0.853], [2.864, 2.653], [-2.864, -0.853]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.859, 109.788]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.673, -4.372], [5.673, 2.57], [5.673, 4.372], [-5.673, -2.572]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.415, 102.787]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -4.258], [5.487, 2.458], [5.487, 4.258], [-5.487, -2.458]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 120.941]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.553], [5.642, 4.353], [-5.642, -2.551]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 111.793]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.352], [5.642, 2.552], [5.642, 4.352], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 107.355]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.123], [-6.901, -3.323], [6.901, 5.123], [6.901, 3.323]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 101.557]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.876], [-3.228, -1.076], [3.228, 2.876], [3.228, 1.075]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 94.871]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.401, -2.37], [2.401, 0.568], [2.401, 2.37], [-2.401, -0.57]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.267, 105.808]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.646, -4.356], [5.646, 2.556], [5.646, 4.356], [-5.646, -2.556]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.141, 112.461]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.134, -2.819], [3.134, 1.018], [3.134, 2.819], [-3.134, -1.018]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.104, 111.661]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.534, -2.451], [2.534, 0.651], [2.534, 2.451], [-2.534, -0.65]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.264, 106.863]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.081, -4.01], [5.081, 2.208], [5.081, 4.01], [-5.081, -2.21]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.523, 99.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.289, -6.584], [9.289, 4.784], [9.289, 6.584], [-9.289, -4.784]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.73, 97.97]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 21", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.552], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 91.178]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 22", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.552], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 86.728]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.594, -5.547], [7.594, 3.745], [7.594, 5.547], [-7.594, -3.746]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.218, 98.559]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -3.396], [4.079, 1.596], [4.079, 3.396], [-4.079, -1.596]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 90.309]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 25", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.379, -6.033], [8.379, 4.221], [8.379, 6.033], [-8.379, -4.221]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.105, 90.986]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 26", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.776, -3.823], [4.776, 2.022], [4.776, 3.823], [-4.776, -2.023]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.209, 87.218]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 27", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.388, -3.591], [4.388, 1.78], [4.388, 3.591], [-4.388, -1.778]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.83, 81.639]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 28", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.428, -4.834], [6.428, 3.034], [6.428, 4.834], [-6.428, -3.034]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.869, 78.444]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 29", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -4.42], [5.752, 2.619], [5.752, 4.42], [-5.752, -2.62]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 72.873]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 30", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.112, -6.476], [9.112, 4.676], [9.112, 6.476], [-9.112, -4.676]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.545, 80.982]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 31", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.864, -2.653], [2.864, 0.853], [2.864, 2.653], [-2.864, -0.853]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.859, 71.384]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 32", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.673, -4.372], [5.673, 2.57], [5.673, 4.372], [-5.673, -2.57]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.415, 64.382]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 33", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -4.258], [5.487, 2.458], [5.487, 4.258], [-5.487, -2.458]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 82.536]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 34", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.358], [5.642, 2.548], [5.642, 4.358], [-5.642, -2.546]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 73.395]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 35", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.553], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 68.951]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 36", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.123], [-6.901, -3.322], [6.901, 5.123], [6.901, 3.323]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 63.152]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 37", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.875], [-3.228, -1.075], [3.228, 2.875], [3.228, 1.077]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 56.466]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 38", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.401, -0.569], [2.401, 2.37], [2.401, 0.57], [-2.401, -2.37]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.267, 56.916]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 39", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.801, -2.649], [5.801, 4.45], [5.801, 2.649], [-5.801, -4.45]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.438, 74.476]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 40", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.289, -1.114], [3.289, 2.913], [3.289, 1.113], [-3.289, -2.913]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.498, 69.451]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 41", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.269, -2.936], [6.269, 4.736], [6.269, 2.936], [-6.269, -4.736]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.432, 62.069]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 42", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.344, -5.428], [10.344, 7.229], [10.344, 5.43], [-10.344, -7.229]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.785, 63.062]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 43", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.114], [3.293, 2.915], [3.293, 1.115], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 54.299]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 44", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 51.174]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 45", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.352], [5.642, 2.553], [-5.642, -4.352]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 46.724]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 46", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.646, -2.556], [5.646, 4.355], [5.646, 2.553], [-5.646, -4.355]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.141, 63.571]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 47", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.134, -1.018], [3.134, 2.818], [3.134, 1.018], [-3.134, -2.818]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.104, 62.77]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 48", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.534, -0.65], [2.534, 2.451], [2.534, 0.651], [-2.534, -2.451]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.264, 57.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 49", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.821, -2.05], [4.821, 3.85], [4.821, 2.05], [-4.821, -3.85]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [32.984, 52.294]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 50", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.081, -2.209], [5.081, 4.009], [5.081, 2.209], [-5.081, -4.009]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.523, 50.955]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 51", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.116], [3.293, 2.915], [3.293, 1.116], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 45.411]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 52", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 42.286]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 53", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.551], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 37.837]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 54", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -1.597], [4.079, 3.396], [4.079, 1.596], [-4.079, -3.396]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 41.417]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 55", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.125, -3.46], [7.125, 5.26], [7.125, 3.46], [-7.125, -5.26]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.359, 42.857]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 56", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.289, -1.113], [3.289, 2.913], [3.289, 1.112], [-3.289, -2.913]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.498, 42.786]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 57", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.269, -2.936], [6.269, 4.736], [6.269, 2.935], [-6.269, -4.736]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.432, 35.404]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 58", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.083, 33.511]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 59", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.114], [3.293, 2.915], [3.293, 1.115], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 27.634]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 60", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -2.621], [5.752, 4.419], [5.752, 2.62], [-5.752, -4.419]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 23.982]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 61", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.883, -2.088], [4.883, 3.888], [4.883, 2.088], [-4.883, -3.888]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.988, 29.303]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 62", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.224, -5.357], [10.224, 7.156], [10.224, 5.357], [-10.224, -7.156]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.967, 18.276]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 63", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -2.458], [5.487, 4.258], [5.487, 2.457], [-5.487, -4.258]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 33.646]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 64", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 24.498]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 65", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 20.06]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 66", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.124], [-6.901, -3.323], [6.901, 5.124], [6.901, 3.322]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 14.262]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 67", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.875], [-3.228, -1.075], [3.228, 2.875], [3.228, 1.075]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 7.576]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 68", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-15.943, -10.656], [-15.943, -8.855], [15.943, 10.656], [15.943, 8.855]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.193, 10.906]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 69", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Code/Customization Graphics Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [40, 203.938, 0], "to": [0, -22.667, 0], "ti": [0, 22.667, 0]}, {"t": 149, "s": [40, 67.938, 0]}]}, "a": {"a": 0, "k": [31, 68, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -3.396], [4.079, 1.597], [4.079, 3.397], [-4.079, -1.595]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 128.712]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.379, -6.027], [8.379, 4.227], [8.379, 6.027], [-8.379, -4.227]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.105, 129.385]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.905, -2.677], [2.905, 0.879], [2.905, 2.677], [-2.905, -0.877]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.399, 131.856]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.776, -3.823], [4.776, 2.023], [4.776, 3.823], [-4.776, -2.024]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.209, 125.622]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.388, -3.585], [4.388, 1.785], [4.388, 3.585], [-4.388, -1.785]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.83, 120.038]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.428, -4.833], [6.428, 3.033], [6.428, 4.833], [-6.428, -3.033]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.869, 116.847]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -4.42], [5.752, 2.62], [5.752, 4.42], [-5.752, -2.62]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 111.276]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-13.165, -8.956], [13.165, 7.154], [13.165, 8.956], [-13.165, -7.156]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.598, 121.866]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.864, -2.653], [2.864, 0.853], [2.864, 2.653], [-2.864, -0.853]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.859, 109.788]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.673, -4.372], [5.673, 2.57], [5.673, 4.372], [-5.673, -2.572]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.415, 102.787]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -4.258], [5.487, 2.458], [5.487, 4.258], [-5.487, -2.458]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 120.941]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.553], [5.642, 4.353], [-5.642, -2.551]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 111.793]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.352], [5.642, 2.552], [5.642, 4.352], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 107.355]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.123], [-6.901, -3.323], [6.901, 5.123], [6.901, 3.323]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 101.557]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.876], [-3.228, -1.076], [3.228, 2.876], [3.228, 1.075]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 94.871]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.401, -2.37], [2.401, 0.568], [2.401, 2.37], [-2.401, -0.57]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.267, 105.808]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.646, -4.356], [5.646, 2.556], [5.646, 4.356], [-5.646, -2.556]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.141, 112.461]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.134, -2.819], [3.134, 1.018], [3.134, 2.819], [-3.134, -1.018]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.104, 111.661]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.534, -2.451], [2.534, 0.651], [2.534, 2.451], [-2.534, -0.65]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.264, 106.863]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.081, -4.01], [5.081, 2.208], [5.081, 4.01], [-5.081, -2.21]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.523, 99.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.289, -6.584], [9.289, 4.784], [9.289, 6.584], [-9.289, -4.784]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.73, 97.97]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 21", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.552], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 91.178]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 22", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.552], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 86.728]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.594, -5.547], [7.594, 3.745], [7.594, 5.547], [-7.594, -3.746]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.218, 98.559]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -3.396], [4.079, 1.596], [4.079, 3.396], [-4.079, -1.596]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 90.309]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 25", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.379, -6.033], [8.379, 4.221], [8.379, 6.033], [-8.379, -4.221]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.105, 90.986]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 26", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.776, -3.823], [4.776, 2.022], [4.776, 3.823], [-4.776, -2.023]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.209, 87.218]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 27", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.388, -3.591], [4.388, 1.78], [4.388, 3.591], [-4.388, -1.778]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.83, 81.639]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 28", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.428, -4.834], [6.428, 3.034], [6.428, 4.834], [-6.428, -3.034]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.869, 78.444]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 29", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -4.42], [5.752, 2.619], [5.752, 4.42], [-5.752, -2.62]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 72.873]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 30", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.112, -6.476], [9.112, 4.676], [9.112, 6.476], [-9.112, -4.676]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.545, 80.982]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 31", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.864, -2.653], [2.864, 0.853], [2.864, 2.653], [-2.864, -0.853]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.859, 71.384]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 32", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.673, -4.372], [5.673, 2.57], [5.673, 4.372], [-5.673, -2.57]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.415, 64.382]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 33", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -4.258], [5.487, 2.458], [5.487, 4.258], [-5.487, -2.458]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 82.536]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 34", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.358], [5.642, 2.548], [5.642, 4.358], [-5.642, -2.546]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 73.395]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 35", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -4.353], [5.642, 2.553], [5.642, 4.353], [-5.642, -2.553]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 68.951]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 36", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.123], [-6.901, -3.322], [6.901, 5.123], [6.901, 3.323]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 63.152]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 37", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.875], [-3.228, -1.075], [3.228, 2.875], [3.228, 1.077]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 56.466]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 38", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.401, -0.569], [2.401, 2.37], [2.401, 0.57], [-2.401, -2.37]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.267, 56.916]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 39", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.801, -2.649], [5.801, 4.45], [5.801, 2.649], [-5.801, -4.45]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.438, 74.476]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 40", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.289, -1.114], [3.289, 2.913], [3.289, 1.113], [-3.289, -2.913]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.498, 69.451]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 41", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.269, -2.936], [6.269, 4.736], [6.269, 2.936], [-6.269, -4.736]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.432, 62.069]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 42", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.344, -5.428], [10.344, 7.229], [10.344, 5.43], [-10.344, -7.229]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.785, 63.062]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 43", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.114], [3.293, 2.915], [3.293, 1.115], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 54.299]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 44", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 51.174]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 45", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.352], [5.642, 2.553], [-5.642, -4.352]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 46.724]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 46", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.646, -2.556], [5.646, 4.355], [5.646, 2.553], [-5.646, -4.355]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.141, 63.571]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 47", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.134, -1.018], [3.134, 2.818], [3.134, 1.018], [-3.134, -2.818]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.104, 62.77]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 48", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.534, -0.65], [2.534, 2.451], [2.534, 0.651], [-2.534, -2.451]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.264, 57.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 49", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.821, -2.05], [4.821, 3.85], [4.821, 2.05], [-4.821, -3.85]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [32.984, 52.294]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 50", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.081, -2.209], [5.081, 4.009], [5.081, 2.209], [-5.081, -4.009]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.523, 50.955]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 51", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.116], [3.293, 2.915], [3.293, 1.116], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 45.411]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 52", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 42.286]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 53", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.551], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 37.837]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 54", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.079, -1.597], [4.079, 3.396], [4.079, 1.596], [-4.079, -3.396]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.735, 41.417]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 55", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.125, -3.46], [7.125, 5.26], [7.125, 3.46], [-7.125, -5.26]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.359, 42.857]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 56", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.289, -1.113], [3.289, 2.913], [3.289, 1.112], [-3.289, -2.913]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.498, 42.786]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 57", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.269, -2.936], [6.269, 4.736], [6.269, 2.935], [-6.269, -4.736]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.432, 35.404]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 58", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.083, 33.511]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 59", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.293, -1.114], [3.293, 2.915], [3.293, 1.115], [-3.293, -2.915]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.735, 27.634]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 60", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.752, -2.621], [5.752, 4.419], [5.752, 2.62], [-5.752, -4.419]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.038, 23.982]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 61", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.883, -2.088], [4.883, 3.888], [4.883, 2.088], [-4.883, -3.888]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.988, 29.303]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 62", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.224, -5.357], [10.224, 7.156], [10.224, 5.357], [-10.224, -7.156]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.967, 18.276]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 63", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.487, -2.458], [5.487, 4.258], [5.487, 2.457], [-5.487, -4.258]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.033, 33.646]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 64", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.553], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 24.498]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 65", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.642, -2.552], [5.642, 4.353], [5.642, 2.552], [-5.642, -4.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.356, 20.06]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 66", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.901, -5.124], [-6.901, -3.323], [6.901, 5.124], [6.901, 3.322]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.151, 14.262]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 67", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.228, -2.875], [-3.228, -1.075], [3.228, 2.875], [3.228, 1.075]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.688149007161, 0.806637154373, 0.416115405513, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.478, 7.576]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 68", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-15.943, -10.656], [-15.943, -8.855], [15.943, 10.656], [15.943, 8.855]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.576305494121, 0.698581531001, 0.721325324563, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.193, 10.906]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 69", "bm": 0, "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Customization Graphics", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [290, 190, 0]}, "a": {"a": 0, "k": [288.5, 181.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 577, "h": 363, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}