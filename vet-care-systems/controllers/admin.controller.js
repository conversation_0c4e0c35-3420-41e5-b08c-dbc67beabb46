import { sendResponse } from '../utils/responseHandler.js';
import crypto from 'crypto';

// In-memory storage for demo purposes - in production, use a database
let apiKeys = [];
let systemSettings = {
  aiSettings: {
    defaultProvider: 'openai',
    enableAiNotes: true,
    maxTokens: 1000,
    temperature: 0.7
  },
  notificationSettings: {
    emailNotifications: true,
    smsNotifications: false,
    appointmentReminders: true
  },
  systemSettings: {
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    currency: 'KES',
    language: 'en'
  }
};

/**
 * Get all API keys
 */
export const getApiKeys = async (req, res) => {
  try {
    // Return API keys with masked keys for security
    const maskedKeys = apiKeys.map(key => ({
      ...key,
      key: maskApiKey(key.key)
    }));

    return sendResponse(res, 200, true, "API keys retrieved successfully", maskedKeys);
  } catch (error) {
    console.error("Get API keys error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Add new API key
 */
export const addApiKey = async (req, res) => {
  try {
    const { name, provider, key, description, isActive = true } = req.body;

    if (!name || !provider || !key) {
      return sendResponse(res, 400, false, "Name, provider, and key are required");
    }

    const newApiKey = {
      id: crypto.randomUUID(),
      name,
      provider,
      key,
      description,
      isActive,
      createdAt: new Date().toISOString(),
      lastUsed: null
    };

    apiKeys.push(newApiKey);

    // Return the new key with masked key
    const responseKey = {
      ...newApiKey,
      key: maskApiKey(newApiKey.key)
    };

    return sendResponse(res, 201, true, "API key added successfully", responseKey);
  } catch (error) {
    console.error("Add API key error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Update API key
 */
export const updateApiKey = async (req, res) => {
  try {
    const { keyId } = req.params;
    const { name, provider, key, description, isActive } = req.body;

    const keyIndex = apiKeys.findIndex(k => k.id === keyId);
    if (keyIndex === -1) {
      return sendResponse(res, 404, false, "API key not found");
    }

    // Update the key
    apiKeys[keyIndex] = {
      ...apiKeys[keyIndex],
      ...(name && { name }),
      ...(provider && { provider }),
      ...(key && { key }),
      ...(description !== undefined && { description }),
      ...(isActive !== undefined && { isActive }),
      updatedAt: new Date().toISOString()
    };

    // Return updated key with masked key
    const responseKey = {
      ...apiKeys[keyIndex],
      key: maskApiKey(apiKeys[keyIndex].key)
    };

    return sendResponse(res, 200, true, "API key updated successfully", responseKey);
  } catch (error) {
    console.error("Update API key error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Delete API key
 */
export const deleteApiKey = async (req, res) => {
  try {
    const { keyId } = req.params;

    const keyIndex = apiKeys.findIndex(k => k.id === keyId);
    if (keyIndex === -1) {
      return sendResponse(res, 404, false, "API key not found");
    }

    apiKeys.splice(keyIndex, 1);

    return sendResponse(res, 200, true, "API key deleted successfully");
  } catch (error) {
    console.error("Delete API key error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Get system settings
 */
export const getSettings = async (req, res) => {
  try {
    return sendResponse(res, 200, true, "Settings retrieved successfully", systemSettings);
  } catch (error) {
    console.error("Get settings error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Update system settings
 */
export const updateSettings = async (req, res) => {
  try {
    const { aiSettings, notificationSettings, systemSettings: sysSettings } = req.body;

    // Update settings
    if (aiSettings) {
      systemSettings.aiSettings = { ...systemSettings.aiSettings, ...aiSettings };
    }
    if (notificationSettings) {
      systemSettings.notificationSettings = { ...systemSettings.notificationSettings, ...notificationSettings };
    }
    if (sysSettings) {
      systemSettings.systemSettings = { ...systemSettings.systemSettings, ...sysSettings };
    }

    systemSettings.updatedAt = new Date().toISOString();

    return sendResponse(res, 200, true, "Settings updated successfully", systemSettings);
  } catch (error) {
    console.error("Update settings error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Generate AI notes for service
 */
export const generateServiceNotes = async (req, res) => {
  try {
    const { service, appointment, context } = req.body;

    if (!service || !service.serviceName) {
      return sendResponse(res, 400, false, "Service information is required");
    }

    // Find active AI API key
    const activeAiKey = apiKeys.find(key => 
      key.isActive && 
      (key.provider === systemSettings.aiSettings.defaultProvider || key.provider === 'openai')
    );

    if (!activeAiKey) {
      return sendResponse(res, 400, false, "No active AI API key found. Please configure an AI API key in admin settings.");
    }

    // Generate AI notes based on service type and context
    const generatedNotes = await generateAIContent(service, appointment, context, activeAiKey);

    // Update last used timestamp for the API key
    const keyIndex = apiKeys.findIndex(k => k.id === activeAiKey.id);
    if (keyIndex !== -1) {
      apiKeys[keyIndex].lastUsed = new Date().toISOString();
    }

    return sendResponse(res, 200, true, "AI notes generated successfully", {
      generatedNotes,
      provider: activeAiKey.provider,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error("Generate AI notes error:", error);
    return sendResponse(res, 500, false, error.message);
  }
};

/**
 * Helper function to mask API keys
 */
function maskApiKey(key) {
  if (!key || key.length <= 8) return '*'.repeat(key?.length || 8);
  return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
}

/**
 * Helper function to generate AI content
 */
async function generateAIContent(service, appointment, context, apiKey) {
  // This is a mock implementation - in production, integrate with actual AI APIs
  const templates = {
    consultation: `Consultation performed for ${service.serviceName}. Patient examined thoroughly. Vital signs checked and recorded. Owner concerns addressed. Treatment plan discussed.`,
    vaccination: `Vaccination administered: ${service.serviceName}. Pre-vaccination examination completed. No adverse reactions observed. Next vaccination due date provided to owner.`,
    surgery: `Surgical procedure: ${service.serviceName}. Pre-operative examination and preparation completed. Procedure performed successfully. Post-operative care instructions provided.`,
    laboratory: `Laboratory test: ${service.serviceName}. Sample collected and processed. Results reviewed and interpreted. Findings discussed with owner.`,
    dental: `Dental procedure: ${service.serviceName}. Oral examination completed. Dental cleaning/treatment performed. Dental care recommendations provided.`,
    grooming: `Grooming service: ${service.serviceName}. Full grooming session completed. Coat condition assessed. Nail trimming and ear cleaning performed.`,
    emergency: `Emergency treatment: ${service.serviceName}. Immediate assessment and stabilization performed. Treatment administered. Follow-up care plan established.`,
    default: `Service performed: ${service.serviceName}. Procedure completed successfully. Patient monitored throughout. Owner informed of results and next steps.`
  };

  const template = templates[service.category] || templates.default;
  
  // Add timestamp and additional context
  const timestamp = new Date().toLocaleString();
  const additionalContext = appointment ? `Appointment ID: ${appointment.appointmentId}. ` : '';
  
  return `${additionalContext}${template}\n\nService completed on: ${timestamp}\nDuration: ${service.estimatedDuration || 30} minutes\nStatus: ${service.status || 'completed'}`;
}

/**
 * Get API key for external use (internal function)
 */
export const getActiveApiKey = (provider = null) => {
  const targetProvider = provider || systemSettings.aiSettings.defaultProvider;
  return apiKeys.find(key => key.isActive && key.provider === targetProvider);
};
