
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { getStaffById, updateStaff } from "@/services/staff";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import LoadingPage from "@/components/common/LoadingPage";
import { Staff } from "@/store/types";
import { useEffect } from "react";

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  roleId: z.string().min(1, "Role is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  clinicId: z.string().min(1, "Clinic is required")
});

type FormValues = z.infer<typeof formSchema>;

const EditStaff = () => {
  const { _id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: staffData, isLoading, error } = useQuery({
    queryKey: ["staff", _id],
    queryFn: () => getStaffById(_id!),
    enabled: !!_id,
  });

  // Make sure we handle the data correctly
  const staff = staffData?.data;

  // Extract first and last name from full name
  const nameParts = staff?.userId?.firstName && staff.userId?.lastName
    ? [staff.userId.firstName, staff.userId.lastName]
    : ["", ""];
  const firstName = nameParts[0] || "";
  const lastName = nameParts.slice(1).join(" ") || "";

  // Use useEffect to handle error toast
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to load staff information",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      roleId: "",
      jobTitle: "",
      clinicId: ""
    },
    values: staff ? {
      firstName,
      lastName,
      email: staff.userId?.email || "",
      phoneNumber: staff.userId?.phoneNumber || "",
      roleId: typeof staff.roleId === 'object' ? (staff.roleId.roleId || staff.roleId._id) : (staff.roleId || ""),
      jobTitle: staff.jobTitle || "",
      clinicId: typeof staff.clinicId === 'object' ? (staff.clinicId.clinicId || staff.clinicId._id) : (staff.clinicId || "")
    } : undefined
  });

  const onSubmit = async (values: FormValues) => {
    try {
      // Create a properly formatted staff object for updating
      const staffData = {
        userId: {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phoneNumber: values.phoneNumber
        },
        roleId: parseInt(values.roleId) || values.roleId, // Try to convert to number first
        jobTitle: values.jobTitle,
        clinicId: parseInt(values.clinicId) || values.clinicId, // Try to convert to number first
        status: 1
      };

      if (_id) {
        const result = await updateStaff(_id, staffData);

        if (!result.success) {
          throw new Error(result.message);
        }

        await queryClient.invalidateQueries({ queryKey: ["staff"] });
        await queryClient.invalidateQueries({ queryKey: ["staff", _id] });

        toast({
          title: "Success",
          description: "Staff member updated successfully",
        });

        navigate("/admin/staff");
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update staff member",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  if (!staff) {
    return null;
  }

  return (
    <div className="p-8">
      <Card>
        <CardHeader>
          <CardTitle>Edit Staff Member</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="roleId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter role ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter job title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="clinicId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Clinic</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter clinic ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => navigate("/admin/staff")}
                  type="button"
                >
                  Cancel
                </Button>
                <Button type="submit">Update Staff Member</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditStaff;
