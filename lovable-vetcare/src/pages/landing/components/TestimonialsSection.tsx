import { motion } from "framer-motion";
import { MessageSquareQuo<PERSON>, <PERSON> } from "lucide-react";
import FlipCarousel from "./FlipCarousel";
import LottieAnimation from "./LottieAnimation";
import TriangleDecoration from "./TriangleDecoration";

const testimonials = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    role: "Veterinarian",
    content: "VetCare has completely transformed how we operate. Patient records are now accessible instantly, and the scheduling system has reduced our no-shows by 40%.",
    avatar: "/pets/cat.jpg"
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Clinic Manager",
    content: "The analytics dashboard gives me insights we never had before. We've been able to optimize staffing and improve our service offerings based on real data.",
    avatar: "/pets/dog.jpg"
  },
  {
    id: 3,
    name: "Dr. <PERSON>",
    role: "Veterinary Surgeon",
    content: "The integrated medical records system makes it so easy to track patient history and treatment plans. It's improved our quality of care significantly.",
    avatar: "/pets/parrot.jpg"
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Pet Owner",
    content: "As a client, I love being able to access my pets' records and book appointments online. The reminders ensure I never miss a vaccination or checkup.",
    avatar: "/pets/horse.jpg"
  }
];

const TestimonialsSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="testimonials" className="py-20 bg-gradient-to-b from-background to-background/90 scroll-mt-20 relative overflow-hidden">
      {/* Background decorations */}
      <TriangleDecoration top="5%" left="2%" size={80} color="bg-primary/10" rotate={15} delay={0.2} />
      <TriangleDecoration top="10%" right="5%" size={60} color="bg-secondary/10" rotate={45} delay={0.3} />
      <TriangleDecoration bottom="15%" left="10%" size={70} color="bg-accent/10" rotate={-30} delay={0.4} />

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            What Our Clients Say
          </h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            Hear from veterinarians and clinic managers who have transformed their practices with VetCare
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <div className="absolute -top-10 -left-10 text-primary/20">
              <MessageSquareQuote size={80} />
            </div>
            
            <div className="h-[400px] relative perspective-1000 z-10 rounded-2xl overflow-hidden shadow-xl">
              <FlipCarousel testimonials={testimonials} autoPlayInterval={6000} />
            </div>
            
            <div className="absolute -bottom-6 -right-6">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((_, i) => (
                  <Star key={i} className="h-6 w-6 text-yellow-400 fill-yellow-400" />
                ))}
              </div>
            </div>
          </motion.div>
          
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            className="relative"
          >
            <div className="absolute top-0 right-0 w-full h-full opacity-5 pointer-events-none">
              <LottieAnimation
                animationPath="/animation/lottie/stethoscope.json"
                className="w-full h-full"
                speed={0.3}
              />
            </div>
            
            <motion.div variants={itemVariants} className="mb-8">
              <h3 className="text-3xl font-bold mb-4">Trusted by Veterinary Professionals</h3>
              <p className="text-foreground/70">
                VetCare is the preferred choice for veterinary clinics across the country, 
                with a 98% satisfaction rate among our users.
              </p>
            </motion.div>
            
            <motion.div variants={itemVariants} className="grid grid-cols-2 gap-4 mb-8">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-primary/10">
                <div className="text-3xl font-bold text-primary">98%</div>
                <div className="text-sm text-foreground/70">Satisfaction Rate</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-primary/10">
                <div className="text-3xl font-bold text-secondary">500+</div>
                <div className="text-sm text-foreground/70">Clinics Using VetCare</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-primary/10">
                <div className="text-3xl font-bold text-accent">40%</div>
                <div className="text-sm text-foreground/70">Reduction in No-Shows</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-primary/10">
                <div className="text-3xl font-bold text-primary">25%</div>
                <div className="text-sm text-foreground/70">Increase in Efficiency</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
