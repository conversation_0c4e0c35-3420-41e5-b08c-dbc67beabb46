import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createPetClinicRelationship,
    getPetRelationships,
    getClinicPetRelationships,
    getRelationship,
    updateRelationship
} from '../controllers/petClinicRelationship.controller.js';

const router = Router();

// Base route: /api/v1/pet-clinic-relationships

// Protected routes
router.use(verifyToken);

// Create a new pet-clinic relationship
router.post('/', createPetClinicRelationship);

// Get all relationships for a specific pet
router.get('/pet/:petId', getPetRelationships);

// Get all pet relationships for a specific clinic
router.get('/clinic/:clinicId', getClinicPetRelationships);

// Get a specific relationship
router.get('/:relationshipId', getRelationship);

// Update a relationship
router.put('/:relationshipId', updateRelationship);

export default router;
