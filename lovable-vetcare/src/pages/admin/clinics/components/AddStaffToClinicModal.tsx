import React, { useState, forwardRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getStaff, updateStaff, createStaff } from "@/services/staff";
import { getRoles } from "@/services/roles";
import { Staff } from "@/store/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { UserPlus, Plus } from "lucide-react";

interface AddStaffToClinicModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  clinicId: string;
  clinicName: string;
  clinicData?: any; // Clinic data passed from parent component
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

// Form schema for creating a new staff member
const newStaffFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
  roleId: z.string().min(1, "Role is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  employmentDate: z.string().min(1, "Employment date is required"),
  salary: z.string().min(1, "Salary is required"),
  isClinicOwner: z.boolean().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type NewStaffFormValues = z.infer<typeof newStaffFormSchema>;

const AddStaffToClinicModal = ({
  open,
  onOpenChange,
  clinicId,
  clinicName,
  clinicData,
  onSuccess,
  trigger
}: AddStaffToClinicModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [isClinicOwner, setIsClinicOwner] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState("existing");
  const [isOpen, setIsOpen] = useState(open);

  // Form for creating a new staff member
  const newStaffForm = useForm<NewStaffFormValues>({
    resolver: zodResolver(newStaffFormSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      roleId: "",
      jobTitle: "",
      employmentDate: new Date().toISOString().split('T')[0],
      salary: "",
      isClinicOwner: false
    },
  });

  // Handle open state for both controlled and uncontrolled usage
  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setIsOpen(newOpen);
    }
  };

  // Determine the effective open state
  const effectiveOpen = open !== undefined ? open : isOpen;

  // Fetch staff not already in this clinic
  const { data: staffData, isLoading: isStaffLoading } = useQuery({
    queryKey: ["staff", "not-in-clinic", clinicId, searchQuery],
    queryFn: () => getStaff({
      search: searchQuery,
      // We can't directly filter by "not in clinic" on the backend,
      // so we'll filter the results on the frontend
      limit: 100
    }),
    enabled: effectiveOpen
  });

  // Fetch roles for dropdown
  const { data: rolesData, isLoading: isRolesLoading } = useQuery({
    queryKey: ["roles"],
    queryFn: () => getRoles(),
    enabled: effectiveOpen
  });

  // Filter staff not already in this clinic
  const availableStaff = staffData?.data?.data.filter(staff => {
    // Check if staff is not already in this clinic
    const staffClinicId = typeof staff.clinicId === 'string'
      ? staff.clinicId
      : staff.clinicId?._id;

    return staffClinicId !== clinicId;
  }) || [];

  const roles = rolesData?.data?.data || [];

  // Update staff mutation (for existing staff)
  const updateStaffMutation = useMutation({
    mutationFn: (data: { staffId: string, updateData: any }) =>
      updateStaff(data.staffId, data.updateData),
    onSuccess: () => {
      toast({
        title: "Success",
        description: `Staff added to ${clinicName} successfully`,
      });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      queryClient.invalidateQueries({ queryKey: ["clinic", clinicId, "staff"] });
      onSuccess?.();
      handleOpenChange(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add staff to clinic",
        variant: "destructive",
      });
    },
  });

  // Create staff mutation (for new staff)
  const createStaffMutation = useMutation({
    mutationFn: (staffData: any) => createStaff(staffData),
    onSuccess: () => {
      toast({
        title: "Success",
        description: `New staff member created and added to ${clinicName} successfully`,
      });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      queryClient.invalidateQueries({ queryKey: ["clinic", clinicId, "staff"] });
      onSuccess?.();
      handleOpenChange(false);
      resetForm();
      newStaffForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create new staff member",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setSelectedStaff(null);
    setSelectedRole("");
    setIsClinicOwner(false);
    setSearchQuery("");
    setActiveTab("existing");
  };

  // Handle submission for existing staff
  const handleExistingStaffSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedStaff) {
      toast({
        title: "Error",
        description: "Please select a staff member",
        variant: "destructive",
      });
      return;
    }

    if (!selectedRole) {
      toast({
        title: "Error",
        description: "Please select a role",
        variant: "destructive",
      });
      return;
    }

    // Update staff with new clinic
    updateStaffMutation.mutate({
      staffId: selectedStaff._id || "",
      updateData: {
        clinicId: clinicData?.clinicId || parseInt(clinicId) || clinicId, // Use clinicData if available
        roleId: selectedRole,
        isClinicOwner: isClinicOwner
      }
    });
  };

  // Handle submission for new staff
  const onNewStaffSubmit = (values: NewStaffFormValues) => {
    // Create staff data object
    const staffData = {
      firstName: values.firstName,
      middleName: values.middleName || "",
      lastName: values.lastName,
      email: values.email,
      phoneNumber: values.phoneNumber,
      password: values.password,
      roleId: parseInt(values.roleId) || values.roleId, // Try to convert to number first
      jobTitle: values.jobTitle,
      employmentDate: new Date(values.employmentDate),
      salary: Number(values.salary),
      clinicId: clinicData?.clinicId || parseInt(clinicId) || clinicId, // Use clinicData if available
      isClinicOwner: values.isClinicOwner || false
    };

    // Call the API to create the staff member
    createStaffMutation.mutate(staffData);
  };

  const getStaffDisplayName = (staff: Staff): string => {
    if (staff.userId && typeof staff.userId !== 'string') {
      return `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
    } else if (staff.firstName || staff.lastName) {
      return `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
    } else {
      return staff.name || `Staff ID: ${staff._id}`;
    }
  };

  // Render the dialog content
  const renderDialogContent = () => (
    <DialogContent className="max-w-md sm:max-w-2xl">
      <DialogHeader>
        <DialogTitle>Add Staff to {clinicName}</DialogTitle>
        <DialogDescription>
          Add existing staff members to this clinic or create new staff members.
        </DialogDescription>
      </DialogHeader>

      <Tabs defaultValue="existing" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="existing">Existing Staff</TabsTrigger>
          <TabsTrigger value="new">Create New Staff</TabsTrigger>
        </TabsList>

        {/* Tab for adding existing staff */}
        <TabsContent value="existing">
          <form onSubmit={handleExistingStaffSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search Staff</Label>
                <Input
                  id="search"
                  placeholder="Search by name or email"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="staff">Select Staff</Label>
                <Select
                  value={selectedStaff?._id}
                  onValueChange={(value) => {
                    const staff = availableStaff.find(s => s._id === value);
                    if (staff) setSelectedStaff(staff);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {isStaffLoading ? (
                      <SelectItem value="loading" disabled>Loading staff...</SelectItem>
                    ) : availableStaff.length === 0 ? (
                      <SelectItem value="none" disabled>No available staff found</SelectItem>
                    ) : (
                      availableStaff.map((staff) => (
                        <SelectItem key={staff._id} value={staff._id || ""}>
                          {getStaffDisplayName(staff)}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={selectedRole}
                  onValueChange={setSelectedRole}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    {isRolesLoading ? (
                      <SelectItem value="loading" disabled>Loading roles...</SelectItem>
                    ) : roles.length === 0 ? (
                      <SelectItem value="none" disabled>No roles found</SelectItem>
                    ) : (
                      roles.map((role) => (
                        <SelectItem key={role._id} value={role._id || ""}>
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isClinicOwner"
                  checked={isClinicOwner}
                  onChange={(e) => setIsClinicOwner(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="isClinicOwner" className="text-sm font-medium">
                  Is Clinic Owner
                </Label>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => handleOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={updateStaffMutation.isPending}>
                {updateStaffMutation.isPending ? "Adding..." : "Add to Clinic"}
              </Button>
            </DialogFooter>
          </form>
        </TabsContent>

        {/* Tab for creating new staff */}
        <TabsContent value="new">
          <Form {...newStaffForm}>
            <form onSubmit={newStaffForm.handleSubmit(onNewStaffSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={newStaffForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="First name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="middleName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Middle Name (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Middle name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Email address" type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Job title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="roleId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isRolesLoading ? (
                            <SelectItem value="loading" disabled>Loading roles...</SelectItem>
                          ) : roles.length === 0 ? (
                            <SelectItem value="none" disabled>No roles found</SelectItem>
                          ) : (
                            roles.map((role) => (
                              <SelectItem key={role._id} value={role._id || ""}>
                                {role.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="employmentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employment Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salary</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Salary" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={newStaffForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Confirm password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={newStaffForm.control}
                name="isClinicOwner"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Is Clinic Owner</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => handleOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createStaffMutation.isPending}>
                  {createStaffMutation.isPending ? "Creating..." : "Create Staff"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </DialogContent>
  );

  // Create a forwardRef wrapper for the trigger
  const TriggerWrapper = forwardRef<HTMLDivElement, { children: React.ReactNode }>((props, ref) => (
    <div ref={ref} {...props}>
      {props.children}
    </div>
  ));
  TriggerWrapper.displayName = "TriggerWrapper";

  // If a trigger is provided, use it with DialogTrigger
  if (trigger) {
    return (
      <Dialog open={effectiveOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          <TriggerWrapper>{trigger}</TriggerWrapper>
        </DialogTrigger>
        {renderDialogContent()}
      </Dialog>
    );
  }

  // Otherwise, use the controlled dialog
  return (
    <Dialog open={effectiveOpen} onOpenChange={handleOpenChange}>
      {renderDialogContent()}
    </Dialog>
  );
};

export default AddStaffToClinicModal;
