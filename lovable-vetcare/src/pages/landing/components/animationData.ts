// Animation data for various sections
// These are simplified versions of <PERSON>tie animations for demonstration purposes

export const petAnimations = {
  cat: {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 60,
    w: 300,
    h: 300,
    nm: "Cat Animation",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Cat Shape",
        sr: 1,
        ks: {
          o: { a: 0, k: 100, ix: 11 },
          r: { 
            a: 1, 
            k: [
              { t: 0, s: [0], e: [5] },
              { t: 30, s: [5], e: [0] },
              { t: 60, s: [0] }
            ], 
            ix: 10 
          },
          p: { 
            a: 1, 
            k: [
              { t: 0, s: [150, 150, 0], e: [150, 140, 0] },
              { t: 30, s: [150, 140, 0], e: [150, 150, 0] },
              { t: 60, s: [150, 150, 0] }
            ], 
            ix: 2, 
            l: 2 
          },
          a: { a: 0, k: [0, 0, 0], ix: 1, l: 2 },
          s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "el",
                p: { a: 0, k: [0, 0], ix: 3 },
                s: { a: 0, k: [100, 100], ix: 2 },
                d: 1,
                nm: "Ellipse Path 1",
                mn: "ADBE Vector Shape - Ellipse",
                hd: false
              },
              {
                ty: "fl",
                c: { a: 0, k: [0.8, 0.4, 0.2, 1], ix: 4 },
                o: { a: 0, k: 100, ix: 5 },
                r: 1,
                bm: 0,
                nm: "Fill 1",
                mn: "ADBE Vector Graphic - Fill",
                hd: false
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0], ix: 2 },
                a: { a: 0, k: [0, 0], ix: 1 },
                s: { a: 0, k: [100, 100], ix: 3 },
                r: { a: 0, k: 0, ix: 6 },
                o: { a: 0, k: 100, ix: 7 },
                sk: { a: 0, k: 0, ix: 4 },
                sa: { a: 0, k: 0, ix: 5 },
                nm: "Transform"
              }
            ],
            nm: "Group 1",
            np: 2,
            cix: 2,
            bm: 0,
            ix: 1,
            mn: "ADBE Vector Group",
            hd: false
          }
        ],
        ip: 0,
        op: 60,
        st: 0,
        bm: 0
      }
    ]
  },
  dog: {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 60,
    w: 300,
    h: 300,
    nm: "Dog Animation",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Dog Shape",
        sr: 1,
        ks: {
          o: { a: 0, k: 100, ix: 11 },
          r: { 
            a: 1, 
            k: [
              { t: 0, s: [0], e: [-5] },
              { t: 30, s: [-5], e: [0] },
              { t: 60, s: [0] }
            ], 
            ix: 10 
          },
          p: { 
            a: 1, 
            k: [
              { t: 0, s: [150, 150, 0], e: [160, 150, 0] },
              { t: 30, s: [160, 150, 0], e: [150, 150, 0] },
              { t: 60, s: [150, 150, 0] }
            ], 
            ix: 2, 
            l: 2 
          },
          a: { a: 0, k: [0, 0, 0], ix: 1, l: 2 },
          s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "rc",
                d: 1,
                s: { a: 0, k: [100, 80], ix: 2 },
                p: { a: 0, k: [0, 0], ix: 3 },
                r: { a: 0, k: 20, ix: 4 },
                nm: "Rectangle Path 1",
                mn: "ADBE Vector Shape - Rect",
                hd: false
              },
              {
                ty: "fl",
                c: { a: 0, k: [0.4, 0.3, 0.2, 1], ix: 4 },
                o: { a: 0, k: 100, ix: 5 },
                r: 1,
                bm: 0,
                nm: "Fill 1",
                mn: "ADBE Vector Graphic - Fill",
                hd: false
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0], ix: 2 },
                a: { a: 0, k: [0, 0], ix: 1 },
                s: { a: 0, k: [100, 100], ix: 3 },
                r: { a: 0, k: 0, ix: 6 },
                o: { a: 0, k: 100, ix: 7 },
                sk: { a: 0, k: 0, ix: 4 },
                sa: { a: 0, k: 0, ix: 5 },
                nm: "Transform"
              }
            ],
            nm: "Group 1",
            np: 2,
            cix: 2,
            bm: 0,
            ix: 1,
            mn: "ADBE Vector Group",
            hd: false
          }
        ],
        ip: 0,
        op: 60,
        st: 0,
        bm: 0
      }
    ]
  },
  bird: {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 60,
    w: 300,
    h: 300,
    nm: "Bird Animation",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Bird Shape",
        sr: 1,
        ks: {
          o: { a: 0, k: 100, ix: 11 },
          r: { 
            a: 1, 
            k: [
              { t: 0, s: [0], e: [10] },
              { t: 15, s: [10], e: [-10] },
              { t: 45, s: [-10], e: [0] },
              { t: 60, s: [0] }
            ], 
            ix: 10 
          },
          p: { 
            a: 1, 
            k: [
              { t: 0, s: [150, 150, 0], e: [150, 140, 0] },
              { t: 30, s: [150, 140, 0], e: [150, 150, 0] },
              { t: 60, s: [150, 150, 0] }
            ], 
            ix: 2, 
            l: 2 
          },
          a: { a: 0, k: [0, 0, 0], ix: 1, l: 2 },
          s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "sr",
                sy: 1,
                d: 1,
                pt: { a: 0, k: 5, ix: 3 },
                p: { a: 0, k: [0, 0], ix: 4 },
                r: { a: 0, k: 0, ix: 5 },
                ir: { a: 0, k: 20, ix: 6 },
                is: { a: 0, k: 0, ix: 8 },
                or: { a: 0, k: 40, ix: 7 },
                os: { a: 0, k: 0, ix: 9 },
                ix: 1,
                nm: "Polystar Path 1",
                mn: "ADBE Vector Shape - Star",
                hd: false
              },
              {
                ty: "fl",
                c: { a: 0, k: [0.2, 0.6, 0.8, 1], ix: 4 },
                o: { a: 0, k: 100, ix: 5 },
                r: 1,
                bm: 0,
                nm: "Fill 1",
                mn: "ADBE Vector Graphic - Fill",
                hd: false
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0], ix: 2 },
                a: { a: 0, k: [0, 0], ix: 1 },
                s: { a: 0, k: [100, 100], ix: 3 },
                r: { a: 0, k: 0, ix: 6 },
                o: { a: 0, k: 100, ix: 7 },
                sk: { a: 0, k: 0, ix: 4 },
                sa: { a: 0, k: 0, ix: 5 },
                nm: "Transform"
              }
            ],
            nm: "Group 1",
            np: 2,
            cix: 2,
            bm: 0,
            ix: 1,
            mn: "ADBE Vector Group",
            hd: false
          }
        ],
        ip: 0,
        op: 60,
        st: 0,
        bm: 0
      }
    ]
  }
};

export const serviceAnimations = {
  diagnostics: {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 60,
    w: 300,
    h: 300,
    nm: "Diagnostics Animation",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Diagnostics Shape",
        sr: 1,
        ks: {
          o: { a: 0, k: 20, ix: 11 },
          r: { 
            a: 1, 
            k: [
              { t: 0, s: [0], e: [360] },
              { t: 60, s: [360] }
            ], 
            ix: 10 
          },
          p: { a: 0, k: [150, 150, 0], ix: 2, l: 2 },
          a: { a: 0, k: [0, 0, 0], ix: 1, l: 2 },
          s: { a: 0, k: [100, 100, 100], ix: 6, l: 2 }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                ty: "sr",
                sy: 1,
                d: 1,
                pt: { a: 0, k: 6, ix: 3 },
                p: { a: 0, k: [0, 0], ix: 4 },
                r: { a: 0, k: 0, ix: 5 },
                ir: { a: 0, k: 50, ix: 6 },
                is: { a: 0, k: 0, ix: 8 },
                or: { a: 0, k: 100, ix: 7 },
                os: { a: 0, k: 0, ix: 9 },
                ix: 1,
                nm: "Polystar Path 1",
                mn: "ADBE Vector Shape - Star",
                hd: false
              },
              {
                ty: "st",
                c: { a: 0, k: [0.2, 0.6, 1, 1], ix: 3 },
                o: { a: 0, k: 100, ix: 4 },
                w: { a: 0, k: 5, ix: 5 },
                lc: 1,
                lj: 1,
                ml: 4,
                bm: 0,
                nm: "Stroke 1",
                mn: "ADBE Vector Graphic - Stroke",
                hd: false
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0], ix: 2 },
                a: { a: 0, k: [0, 0], ix: 1 },
                s: { a: 0, k: [100, 100], ix: 3 },
                r: { a: 0, k: 0, ix: 6 },
                o: { a: 0, k: 100, ix: 7 },
                sk: { a: 0, k: 0, ix: 4 },
                sa: { a: 0, k: 0, ix: 5 },
                nm: "Transform"
              }
            ],
            nm: "Group 1",
            np: 2,
            cix: 2,
            bm: 0,
            ix: 1,
            mn: "ADBE Vector Group",
            hd: false
          }
        ],
        ip: 0,
        op: 60,
        st: 0,
        bm: 0
      }
    ]
  }
};
