import mongoose from 'mongoose'
import {DB_URI, NODE_ENV, PORT} from "../config/env.js";

if(!DB_URI) {
    throw new Error("MongoDB URI doesn't exist: check .env<development/production>.local");
}

const connectToDatabase = async () => {
    try {
        console.log(`MongoDB connection started on port ${PORT}`);
        await mongoose.connect(DB_URI);
        console.log(`MongoDB Connected: IN ${NODE_ENV} MODE`);
    }catch(err){
        console.log("Error connecting to database: " + err);
        process.exit(1);
    }
}

export default connectToDatabase;