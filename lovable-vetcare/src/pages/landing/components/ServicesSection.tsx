
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Stethoscope,
  Pill,
  Activity,
  Clipboard,
  Calendar,
  Users,
  ArrowRight
} from "lucide-react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import FancyCard3D from "./FancyCard3D";
import TriangleDecoration from "./TriangleDecoration";
import LottieAnimation from "./LottieAnimation";


const services = [
  {
    icon: Stethoscope,
    title: "Diagnostics",
    description: "Complete diagnostic suite for accurate pet health assessment",
    color: "bg-primary",
    accent: "top-right",
    animation: "/animation/lottie/stethoscope.json"
  },
  {
    icon: Pill,
    title: "Medication",
    description: "Integrated pharmacy and medication management system",
    color: "bg-secondary",
    accent: "bottom-left",
    animation: "/animation/lottie/pill.json"
  },
  {
    icon: Activity,
    title: "Treatment",
    description: "Full treatment planning and monitoring tools",
    color: "bg-accent",
    accent: "top-left",
    animation: "/animation/lottie/treatment.json"
  },
  {
    icon: Clipboard,
    title: "Records",
    description: "Comprehensive medical records and history tracking",
    color: "bg-primary",
    accent: "bottom-right",
    animation: "/animation/lottie/records.json"
  },
  {
    icon: Calendar,
    title: "Scheduling",
    description: "Smart appointment scheduling and reminders",
    color: "bg-secondary",
    accent: "top-right",
    animation: "/animation/lottie/dog.json"
  },
  {
    icon: Users,
    title: "Client Portal",
    description: "Secure client access to pet health information",
    color: "bg-accent",
    accent: "bottom-left",
    animation: "/animation/lottie/bird.json"
  }
];

const ServicesSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="services" className="py-20 scroll-mt-20 relative overflow-hidden">
      {/* Background decorations */}
      <TriangleDecoration top="5%" left="2%" size={80} color="bg-primary/10" rotate={15} delay={0.2} />
      <TriangleDecoration top="10%" right="5%" size={60} color="bg-secondary/10" rotate={45} delay={0.3} />
      <TriangleDecoration bottom="15%" left="10%" size={70} color="bg-accent/10" rotate={-30} delay={0.4} />

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary relative inline-block">
            Our Complete Solution
            <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary to-secondary rounded-full"></span>
          </h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto mt-6">
            VetCare provides a comprehensive suite of tools designed specifically for modern veterinary practices
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {services.map((service, index) => (
            <motion.div key={index} variants={itemVariants}>
              <FancyCard3D
                cornerAccent={service.accent as any}
                accentColor={service.color}
                depth={15}
                className="h-full"
              >
                <div className="p-6 flex flex-col h-full relative overflow-hidden">
                  {/* Lottie animation in background */}
                  <div className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none">
                    <LottieAnimation
                      animationPath={service.animation}
                      className="w-full h-full"
                      speed={0.7}
                    />
                  </div>

                  <div className={`${service.color}/20 p-4 rounded-2xl mb-4 w-fit relative z-10`}>
                    <service.icon className={`h-8 w-8 ${service.color}`} />
                  </div>
                  <h3 className="text-2xl font-bold mb-3 relative z-10">{service.title}</h3>
                  <p className="text-foreground/70 mb-6 flex-grow relative z-10">{service.description}</p>
                  <Button variant="ghost" asChild className="w-fit group relative z-10">
                    <Link to="/register/owner" className="flex items-center gap-2">
                      Learn More
                      <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </FancyCard3D>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
