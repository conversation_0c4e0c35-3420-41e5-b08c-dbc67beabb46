/**
 * CSRF Token Routes
 * 
 * This file contains routes for CSRF token management.
 */

import { Router } from "express";
import { csrfProtection } from "../../middlewares/csrf.middleware.js";

const csrfRouter = Router();

/**
 * @route GET /api/v1/csrf-token
 * @desc Get a CSRF token
 * @access Public
 */
csrfRouter.get('/csrf-token', csrfProtection, (req, res) => {
  return res.json({
    success: true,
    status: 200,
    message: 'CSRF token generated',
    data: {
      csrfToken: req.csrfToken()
    }
  });
});

export default csrfRouter;
