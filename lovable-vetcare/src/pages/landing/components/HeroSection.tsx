
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Steth<PERSON><PERSON>, <PERSON>, Shield<PERSON>he<PERSON>, <PERSON><PERSON><PERSON>, PawP<PERSON>t, Syringe } from "lucide-react";
import { motion } from "framer-motion";
import FancyCard3D from "./FancyCard3D";
import AnimatedPet3D from "./AnimatedPet3D";
import TriangleDecoration from "./TriangleDecoration";

interface HeroSectionProps {
  backgroundImages: string[];
  currentBgIndex: number;
  staticTitle: string;
}

const HeroSection = ({ backgroundImages, currentBgIndex, staticTitle }: HeroSectionProps) => {
  const titleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const subtitleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 0.2,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 0.4,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section className="relative min-h-screen flex items-center pt-20 overflow-hidden bg-gradient-to-b from-primary/10 via-background to-background">
      {/* Background decorations */}
      <TriangleDecoration top="10%" left="5%" size={60} color="bg-primary/30" rotate={45} delay={0.5} />
      <TriangleDecoration top="15%" right="10%" size={40} color="bg-secondary/30" rotate={180} delay={0.7} />
      <TriangleDecoration bottom="20%" left="15%" size={50} color="bg-accent/30" rotate={-30} delay={0.9} />
      <TriangleDecoration bottom="10%" right="5%" size={70} color="bg-primary/30" rotate={120} delay={1.1} />

      {/* Background images with blur effect */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        {backgroundImages.map((img, index) => (
          <div
            key={img}
            className={`absolute inset-0 transition-opacity duration-1000 bg-cover bg-center ${
              index === currentBgIndex ? 'opacity-10' : 'opacity-0'
            }`}
            style={{ backgroundImage: `url(${img})` }}
          />
        ))}
        <div className="absolute inset-0 backdrop-blur-3xl" />
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="text-center md:text-left space-y-8">
            <motion.h1
              className="text-5xl md:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary mb-4"
              variants={titleVariants}
              initial="hidden"
              animate="visible"
            >
              {staticTitle || "PROFESSIONAL PET CARE"}
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-foreground/90 max-w-xl"
              variants={subtitleVariants}
              initial="hidden"
              animate="visible"
            >
              A complete solution for veterinary practices. Streamline operations, enhance patient care, and grow your business.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row items-center gap-4 pt-4 justify-center md:justify-start"
              variants={buttonVariants}
              initial="hidden"
              animate="visible"
            >
              <Button size="lg" className="w-full sm:w-auto rounded-xl bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all duration-300 shadow-lg">
                Get Started <ArrowRight className="ml-2" />
              </Button>
              <Button size="lg" variant="outline" className="w-full sm:w-auto backdrop-blur-md border-primary/20 hover:bg-primary/10 rounded-xl">
                Schedule Demo
              </Button>
            </motion.div>
          </div>

          <div className="relative hidden md:block">
            {/* 3D Pet Image */}
            <div className="absolute -top-20 -right-10 w-48 h-48 z-20">
              <AnimatedPet3D
                imageSrc="/pets/cat.jpg"
                alt="Cat"
                delay={0.3}
              />
            </div>

            <div className="absolute -bottom-10 -left-10 w-40 h-40 z-20">
              <AnimatedPet3D
                imageSrc="/pets/dog.jpg"
                alt="Dog"
                delay={0.6}
              />
            </div>

            {/* Main 3D Card */}
            <FancyCard3D
              className="max-w-md mx-auto"
              cornerAccent="top-right"
              accentColor="bg-primary"
            >
              <h3 className="text-2xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                VetCare Platform Features
              </h3>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-primary/20 p-3 rounded-xl text-primary">
                    <Stethoscope className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Advanced Diagnostics</h3>
                    <p className="text-foreground/70">Complete patient history and diagnostic tools at your fingertips</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="bg-secondary/20 p-3 rounded-xl text-secondary">
                    <Calendar className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Smart Scheduling</h3>
                    <p className="text-foreground/70">Efficient appointment booking that reduces no-shows</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="bg-accent/20 p-3 rounded-xl text-accent">
                    <ShieldCheck className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Secure Records</h3>
                    <p className="text-foreground/70">HIPAA-compliant digital storage for all patient data</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="bg-primary/20 p-3 rounded-xl text-primary">
                    <PawPrint className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Pet Profiles</h3>
                    <p className="text-foreground/70">Comprehensive pet profiles with medical history</p>
                  </div>
                </div>
              </div>
            </FancyCard3D>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
