# Troubleshooting Guide

This document outlines issues encountered during the implementation of architectural improvements and how they were resolved.

## Environment Configuration Issues

### Issue 1: Syntax Error in Environment Variables Export

**Error Message:**
```
SyntaxError: Unexpected token ':'
```

**Problem:**
The ES module export syntax in `config/env.js` was incorrect. We were trying to use object property shorthand and direct assignment in the same export statement, which is not valid JavaScript.

**Solution:**
1. Extract all environment variables to constants first
2. Then export them using the proper ES module export syntax

```javascript
// Before (incorrect)
export {
    // Database configuration
    DB_URI: process.env.DB_URI,
    // ...other variables
};

// After (correct)
const DB_URI = process.env.DB_URI;
// ...other variables

export {
    DB_URI,
    // ...other variables
};
```

### Issue 2: Environment Variable Name Mismatch

**Error Message:**
```
Error: Missing required environment variables: JWT_EXPIRE_IN
```

**Problem:**
Our code was looking for `JWT_EXPIRE_IN` but the actual environment variable in `.env.development.local` was named `JWT_EXPIRES_IN` (with an 's').

**Solution:**
Updated all references in the codebase to use the consistent name `JWT_EXPIRES_IN`:

1. Updated `config/env.js` to check for `JWT_EXPIRES_IN`
2. Updated controller imports to use `JWT_EXPIRES_IN`
3. Updated template files to use the consistent naming

Files that needed updating:
- `config/env.js`
- `controllers/auth.controller.js`
- `controllers/user.controller.js`
- `controllers/staff.controller.js`
- `.env.development.template`
- `.env.production.template`
- `.github/workflows/ci.yml`

## MongoDB Warnings

**Warning Messages:**
```
[MONGOOSE] Warning: Duplicate schema index on {"roleId":1} found.
[MONGOOSE] Warning: Duplicate schema index on {"name":1} found.
```

**Problem:**
Some MongoDB schemas have duplicate index definitions, which can cause performance issues.

**Potential Solution:**
Review schema definitions and remove duplicate index declarations. This typically happens when both of these are used:
- `{ index: true }` in the schema field definition
- `schema.index({ field: 1 })` as a separate call

These warnings don't prevent the application from running but should be addressed in future updates.

## Running the Application

To run the application:

1. Ensure all environment variables are properly set in `.env.development.local`
2. Run the backend server:
   ```
   cd vet-care-systems
   npm run dev
   ```
3. Run the frontend (in a separate terminal):
   ```
   cd lovable-vetcare
   npm run dev
   ```

## Common Issues and Solutions

1. **Missing Environment Variables**
   - Check that all required variables are defined in your `.env.development.local` file
   - Ensure variable names match exactly what the code is looking for

2. **Module Import Errors**
   - Ensure all import paths are correct
   - Check that exported names match imported names

3. **Database Connection Issues**
   - Verify MongoDB connection string is correct
   - Ensure MongoDB server is running

4. **CSRF Protection Issues**
   - For API testing, you may need to disable CSRF protection temporarily
   - Ensure your frontend is properly handling CSRF tokens
