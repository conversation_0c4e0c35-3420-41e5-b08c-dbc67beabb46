import AppointmentNote from '../models/appointmentNote.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Create a new appointment note
 */
export const createAppointmentNote = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            noteType,
            category,
            title,
            content,
            priority = 'normal',
            isPrivate = false,
            tags = [],
            visibility = 'all_staff',
            visibleToRoles = []
        } = req.body;

        const note = await AppointmentNote.create({
            appointmentId: parseInt(appointmentId),
            staffId: req.user?.staffId || req.user?.userId || 1001,
            noteType,
            category,
            title,
            content,
            priority,
            isPrivate,
            tags,
            visibility,
            visibleToRoles
        });

        // Get populated note for response
        const populatedNote = await AppointmentNote.findOne({ noteId: note.noteId }).lean();
        
        // Add staff data
        const staffData = await AppointmentNote.model('Staff').findOne({ 
            staffId: note.staffId 
        }).lean();
        
        if (staffData) {
            populatedNote.staffData = staffData;
            populatedNote.staffName = `${staffData.firstName} ${staffData.lastName}`;
        }

        return sendResponse(res, 201, true, "Appointment note created successfully", populatedNote);
    } catch (error) {
        console.error("Create appointment note error:", error);
        return sendResponse(res, 400, false, `Failed to create appointment note: ${error.message}`);
    }
};

/**
 * Get all notes for an appointment
 */
export const getAppointmentNotes = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { 
            noteType, 
            category, 
            priority, 
            status = 'published',
            page = 1, 
            limit = 20 
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        
        let query = { 
            appointmentId: parseInt(appointmentId),
            status
        };
        
        if (noteType) query.noteType = noteType;
        if (category) query.category = category;
        if (priority) query.priority = priority;

        // Check visibility permissions
        const userStaffId = req.user?.staffId || req.user?.userId;
        if (!req.user?.isAdmin) {
            query.$or = [
                { visibility: 'all_staff' },
                { staffId: userStaffId },
                { visibility: 'specific_roles', visibleToRoles: { $in: [req.user?.roleId] } }
            ];
        }

        const [notes, totalCount] = await Promise.all([
            AppointmentNote.find(query)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            AppointmentNote.countDocuments(query)
        ]);

        // Populate staff data for each note
        const populatedNotes = await Promise.all(notes.map(async (note) => {
            const staffData = await AppointmentNote.model('Staff').findOne({ 
                staffId: note.staffId 
            }).lean();
            
            if (staffData) {
                note.staffData = staffData;
                note.staffName = `${staffData.firstName} ${staffData.lastName}`;
            }
            
            return note;
        }));

        const totalPages = Math.ceil(totalCount / parseInt(limit));

        return sendResponse(res, 200, true, "Appointment notes retrieved successfully", {
            data: populatedNotes,
            pagination: {
                totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages
            }
        });
    } catch (error) {
        console.error("Get appointment notes error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve appointment notes: ${error.message}`);
    }
};

/**
 * Update an appointment note
 */
export const updateAppointmentNote = async (req, res) => {
    try {
        const { noteId } = req.params;
        const updateData = { ...req.body };
        
        // Add edit tracking
        updateData.editedBy = req.user?.staffId || req.user?.userId;

        const note = await AppointmentNote.findOneAndUpdate(
            { noteId: parseInt(noteId) },
            updateData,
            { new: true, runValidators: true, context: { userId: updateData.editedBy } }
        ).lean();

        if (!note) {
            return sendResponse(res, 404, false, "Appointment note not found");
        }

        // Add staff data
        const staffData = await AppointmentNote.model('Staff').findOne({ 
            staffId: note.staffId 
        }).lean();
        
        if (staffData) {
            note.staffData = staffData;
            note.staffName = `${staffData.firstName} ${staffData.lastName}`;
        }

        return sendResponse(res, 200, true, "Appointment note updated successfully", note);
    } catch (error) {
        console.error("Update appointment note error:", error);
        return sendResponse(res, 400, false, `Failed to update appointment note: ${error.message}`);
    }
};

/**
 * Delete an appointment note
 */
export const deleteAppointmentNote = async (req, res) => {
    try {
        const { noteId } = req.params;

        const note = await AppointmentNote.findOneAndDelete({ noteId: parseInt(noteId) });

        if (!note) {
            return sendResponse(res, 404, false, "Appointment note not found");
        }

        return sendResponse(res, 200, true, "Appointment note deleted successfully");
    } catch (error) {
        console.error("Delete appointment note error:", error);
        return sendResponse(res, 500, false, `Failed to delete appointment note: ${error.message}`);
    }
};

/**
 * Get notes by category for an appointment
 */
export const getNotesByCategory = async (req, res) => {
    try {
        const { appointmentId, category } = req.params;

        const notes = await AppointmentNote.find({
            appointmentId: parseInt(appointmentId),
            category,
            status: 'published'
        }).sort({ createdAt: -1 }).lean();

        // Populate staff data for each note
        const populatedNotes = await Promise.all(notes.map(async (note) => {
            const staffData = await AppointmentNote.model('Staff').findOne({ 
                staffId: note.staffId 
            }).lean();
            
            if (staffData) {
                note.staffData = staffData;
                note.staffName = `${staffData.firstName} ${staffData.lastName}`;
            }
            
            return note;
        }));

        return sendResponse(res, 200, true, `${category} notes retrieved successfully`, populatedNotes);
    } catch (error) {
        console.error("Get notes by category error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve ${category} notes: ${error.message}`);
    }
};

/**
 * Archive an appointment note
 */
export const archiveAppointmentNote = async (req, res) => {
    try {
        const { noteId } = req.params;

        const note = await AppointmentNote.findOneAndUpdate(
            { noteId: parseInt(noteId) },
            { status: 'archived' },
            { new: true }
        );

        if (!note) {
            return sendResponse(res, 404, false, "Appointment note not found");
        }

        return sendResponse(res, 200, true, "Appointment note archived successfully", note);
    } catch (error) {
        console.error("Archive appointment note error:", error);
        return sendResponse(res, 500, false, `Failed to archive appointment note: ${error.message}`);
    }
};
