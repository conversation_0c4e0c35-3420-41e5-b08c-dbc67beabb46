# Veterinary SaaS Application Update Guide

## Overview

This guide provides instructions for updating the Veterinary SaaS application to work with the new data model where staff and user tables are completely separate. It also includes best practices for state management and recommendations for improving the application architecture.

## Table of Contents

1. [Database Schema Changes](#database-schema-changes)
2. [Backend API Updates](#backend-api-updates)
3. [Frontend State Management](#frontend-state-management)
4. [Authentication Flow](#authentication-flow)
5. [Component Updates](#component-updates)
6. [Testing Strategy](#testing-strategy)

## Database Schema Changes

### Key Changes

- **Separate User Tables**: Staff, Client, and Admin are now completely separate tables with no linking
- **Removed User Type**: The `userType` field has been removed from the User model
- **Authentication in Each Model**: Each model now contains its own authentication fields
- **Clinic Ownership**: Clinics are tied directly to staff members who are clinic owners

### Migration Steps

1. **Enable Schema Reset**: Uncomment the `resetCollectionIfSchemaChanged` function in each model file
2. **Backup Existing Data**: Before running the application, export all existing data
3. **Data Migration**: Create a migration script to move data from the old schema to the new schema
4. **Verify Migration**: After migration, verify that all data has been correctly transferred

## Backend API Updates

### Authentication Controller

The authentication controller has been updated to:

- Handle different user types (staff, client, admin)
- Create users in the appropriate collection
- Include user type in the JWT token
- Return appropriate data based on user type

### Middleware Updates

The middleware has been updated to:

- Verify tokens based on user type
- Check permissions based on user type
- Validate clinic access based on user type

### Staff Controller

The staff controller has been updated to:

- Work directly with the Staff model
- Handle staff-specific operations
- Check for clinic ownership when needed

### Required Updates

1. **Create Client Controller**: Implement CRUD operations for clients
2. **Create Admin Controller**: Implement CRUD operations for admins
3. **Update Clinic Controller**: Ensure it works with the new staff model
4. **Update Pet Controller**: Ensure it works with the new client model

## Frontend State Management

### Current State Management

The current application uses Zustand for state management, which is a good choice for its simplicity and flexibility. However, the state management can be improved for better organization and performance.

### Recommended Improvements

#### 1. Modular Store Structure

Organize the store into modules for better separation of concerns:

```typescript
// src/store/index.ts
import { createAuthSlice } from './slices/authSlice';
import { createClinicSlice } from './slices/clinicSlice';
import { createStaffSlice } from './slices/staffSlice';
import { createClientSlice } from './slices/clientSlice';
import { createPetSlice } from './slices/petSlice';
import { createAppointmentSlice } from './slices/appointmentSlice';

export const useStore = create<
  AuthSlice & ClinicSlice & StaffSlice & ClientSlice & PetSlice & AppointmentSlice
>()((...a) => ({
  ...createAuthSlice(...a),
  ...createClinicSlice(...a),
  ...createStaffSlice(...a),
  ...createClientSlice(...a),
  ...createPetSlice(...a),
  ...createAppointmentSlice(...a),
}));
```

#### 2. Typed Slices

Create typed slices for better type safety:

```typescript
// src/store/slices/authSlice.ts
import { StateCreator } from 'zustand';
import { Staff, Client, Admin } from '../types';

export interface AuthState {
  isAuthenticated: boolean;
  userType: 'staff' | 'client' | 'admin' | null;
  staff: Staff | null;
  client: Client | null;
  admin: Admin | null;
  token: string | null;
  currentClinic: Clinic | null;
  availableClinics: Clinic[];
  login: (email: string, password: string, userType: string) => Promise<void>;
  logout: () => void;
  switchClinic: (clinicId: string) => Promise<void>;
}

export const createAuthSlice: StateCreator<AuthState> = (set, get) => ({
  isAuthenticated: false,
  userType: null,
  staff: null,
  client: null,
  admin: null,
  token: null,
  currentClinic: null,
  availableClinics: [],
  
  login: async (email, password, userType) => {
    // Implementation
  },
  
  logout: () => {
    // Implementation
  },
  
  switchClinic: async (clinicId) => {
    // Implementation
  }
});
```

#### 3. API Integration

Create a service layer for API calls:

```typescript
// src/services/api.ts
import axios from 'axios';
import { useStore } from '../store';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
});

// Add request interceptor
api.interceptors.request.use(
  (config) => {
    const token = useStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    // Handle errors (e.g., 401 Unauthorized)
    if (error.response && error.response.status === 401) {
      useStore.getState().logout();
    }
    return Promise.reject(error);
  }
);

export default api;
```

#### 4. React Query Integration

Consider using React Query for data fetching and caching:

```typescript
// src/hooks/useStaff.ts
import { useQuery, useMutation, useQueryClient } from 'react-query';
import api from '../services/api';

export const useStaff = (clinicId: string) => {
  return useQuery(['staff', clinicId], () => 
    api.get(`/staff?clinicId=${clinicId}`)
  );
};

export const useStaffMember = (staffId: string) => {
  return useQuery(['staff', staffId], () => 
    api.get(`/staff/${staffId}`)
  );
};

export const useCreateStaff = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    (newStaff) => api.post('/staff', newStaff),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('staff');
      },
    }
  );
};
```

## Authentication Flow

### Updated Authentication Flow

1. **Login**:
   - User enters email, password, and selects user type
   - Backend checks the appropriate collection based on user type
   - Backend returns user data, token, and available clinics

2. **Token Storage**:
   - Store token in secure storage (localStorage or sessionStorage)
   - Store user type in secure storage
   - Store current clinic in secure storage

3. **API Requests**:
   - Include token in Authorization header
   - Include user type in requests when needed

4. **Logout**:
   - Clear all stored data
   - Redirect to login page

## Component Updates

### Required Component Updates

1. **Login Component**:
   - Add user type selection
   - Update form submission to include user type

2. **User Profile Component**:
   - Update to display different fields based on user type
   - Add clinic switching functionality for staff

3. **Staff Management Components**:
   - Update to work with the new staff model
   - Add clinic assignment functionality

4. **Client Management Components**:
   - Update to work with the new client model
   - Add preferred clinic selection

5. **Clinic Management Components**:
   - Update to work with the new clinic-staff relationship
   - Add owner assignment functionality

## Testing Strategy

### Unit Tests

1. **Model Tests**:
   - Test validation rules for each model
   - Test relationships between models

2. **Controller Tests**:
   - Test authentication with different user types
   - Test CRUD operations for each model
   - Test permission checking

3. **Middleware Tests**:
   - Test token verification
   - Test permission checking
   - Test clinic access checking

### Integration Tests

1. **API Tests**:
   - Test authentication flow
   - Test clinic switching
   - Test staff-clinic relationships

2. **Frontend Tests**:
   - Test login with different user types
   - Test clinic switching
   - Test permission-based UI rendering

### End-to-End Tests

1. **User Flows**:
   - Test complete user flows for each user type
   - Test clinic management flows
   - Test staff management flows

## Conclusion

By following this guide, you will be able to update the Veterinary SaaS application to work with the new data model where staff and user tables are completely separate. The recommended state management improvements will also help make the application more maintainable and performant.

Remember to thoroughly test all changes before deploying to production, and consider implementing the changes incrementally to minimize disruption to users.
