/**
 * UI Store Slice
 * 
 * This slice handles UI state like sidebar visibility, theme, etc.
 */

import { StateCreator } from 'zustand';

// UI slice state interface
export interface UIState {
  sidebarOpen: boolean;
  isMobileView: boolean;
  activeTheme: 'light' | 'dark' | 'system';
}

// UI slice actions interface
export interface UIActions {
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setMobileView: (isMobile: boolean) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

// Combined UI slice type
export type UISlice = UIState & UIActions;

// UI slice creator function
export const createUISlice: StateCreator<
  UISlice,
  [],
  [],
  UISlice
> = (set) => ({
  // Initial state
  sidebarOpen: true,
  isMobileView: false,
  activeTheme: 'system',
  
  // Actions
  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  setMobileView: (isMobile) => set({ isMobileView: isMobile }),
  setTheme: (theme) => set({ activeTheme: theme }),
});
