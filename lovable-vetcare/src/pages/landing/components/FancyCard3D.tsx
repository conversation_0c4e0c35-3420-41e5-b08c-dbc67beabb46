import { ReactNode, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';

interface FancyCard3DProps {
  children: ReactNode;
  className?: string;
  cornerAccent?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'none';
  accentColor?: string;
  depth?: number;
}

const FancyCard3D = ({
  children,
  className = '',
  cornerAccent = 'top-right',
  accentColor = 'bg-primary',
  depth = 20
}: FancyCard3DProps) => {
  const ref = useRef<HTMLDivElement>(null);

  // Motion values for the card tilt effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // Smooth out the motion values
  const springConfig = { damping: 20, stiffness: 300 };
  const xSpring = useSpring(x, springConfig);
  const ySpring = useSpring(y, springConfig);

  // Transform the motion values to rotation values
  const rotateX = useTransform(ySpring, [-0.5, 0.5], [depth/2, -depth/2]);
  const rotateY = useTransform(xSpring, [-0.5, 0.5], [-depth/2, depth/2]);

  // Handle mouse move on the card
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    // Calculate the position of the mouse relative to the card (0 to 1)
    const xValue = (e.clientX - rect.left) / width - 0.5;
    const yValue = (e.clientY - rect.top) / height - 0.5;

    // Update the motion values
    x.set(xValue);
    y.set(yValue);
  };

  // Reset the card position when mouse leaves
  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  // Determine the triangle position based on the cornerAccent prop
  const getTriangleClasses = () => {
    switch (cornerAccent) {
      case 'top-left':
        return 'top-0 left-0 border-t-[50px] border-l-[50px] border-r-transparent border-b-transparent';
      case 'top-right':
        return 'top-0 right-0 border-t-[50px] border-r-[50px] border-l-transparent border-b-transparent';
      case 'bottom-left':
        return 'bottom-0 left-0 border-b-[50px] border-l-[50px] border-r-transparent border-t-transparent';
      case 'bottom-right':
        return 'bottom-0 right-0 border-b-[50px] border-r-[50px] border-l-transparent border-t-transparent';
      default:
        return 'hidden';
    }
  };

  return (
    <motion.div
      ref={ref}
      className={`relative overflow-hidden rounded-[2rem] bg-white/10 backdrop-blur-md border border-white/20 shadow-xl ${className}`}
      style={{
        perspective: '1200px',
        transformStyle: 'preserve-3d',
        borderRadius: '24px',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      whileHover={{ boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)', scale: 1.02 }}
    >
      {/* Triangle accent in corner */}
      {cornerAccent !== 'none' && (
        <div
          className={`absolute w-0 h-0 border-solid ${accentColor} ${getTriangleClasses()}`}
        />
      )}

      {/* 3D card content */}
      <motion.div
        className="relative z-10 p-6"
        style={{
          rotateX,
          rotateY,
          transformStyle: 'preserve-3d',
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

export default FancyCard3D;
