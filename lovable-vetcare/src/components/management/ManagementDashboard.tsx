import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Users,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2,
  UserPlus,
  Settings,
  Shield,
  Clock,
  Mail,
  Phone
} from "lucide-react";
import { motion } from "framer-motion";
import { ClinicCreationWizard } from "@/components/clinic/ClinicCreationWizard";
import { StaffCreationWizard } from "@/components/staff/StaffCreationWizard";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/services/api";
import { getClinics } from "@/services/clinics";
import { useAuth } from "@/store";

interface ManagementDashboardProps {
  userRole?: string;
  isAdmin?: boolean;
}

export const ManagementDashboard = ({ userRole = "admin", isAdmin = true }: ManagementDashboardProps) => {
  const { staff } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [showClinicWizard, setShowClinicWizard] = useState(false);
  const [showStaffWizard, setShowStaffWizard] = useState(false);
  const [selectedClinicId, setSelectedClinicId] = useState<number | undefined>();
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch clinics - filter by ownerId if user is clinic owner
  const { data: clinicsData, refetch: refetchClinics } = useQuery({
    queryKey: ["clinics", staff?.staffId],
    queryFn: async () => {
      const params = staff?.isClinicOwner && !isAdmin ? { ownerId: staff.staffId } : {};
      const response = await getClinics(params);
      return response.data?.data || [];
    }
  });

  // Fetch staff
  const { data: staffData, refetch: refetchStaff } = useQuery({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await api.get('/staff');
      return response.data?.data?.data || [];
    }
  });

  const handleClinicCreated = (clinic: any) => {
    setShowClinicWizard(false);
    refetchClinics();
  };

  const handleStaffCreated = (staff: any) => {
    setShowStaffWizard(false);
    setSelectedClinicId(undefined);
    refetchStaff();
  };

  if (showClinicWizard) {
    return (
      <ClinicCreationWizard
        onComplete={handleClinicCreated}
        onCancel={() => setShowClinicWizard(false)}
      />
    );
  }

  if (showStaffWizard) {
    return (
      <StaffCreationWizard
        clinicId={selectedClinicId}
        onComplete={handleStaffCreated}
        onCancel={() => {
          setShowStaffWizard(false);
          setSelectedClinicId(undefined);
        }}
      />
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Management Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage clinics, staff, and permissions</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => setShowStaffWizard(true)}
            className="flex items-center"
          >
            <UserPlus className="mr-2 h-4 w-4" />
            Add Staff
          </Button>
          {isAdmin && (
            <Button
              onClick={() => setShowClinicWizard(true)}
              className="flex items-center"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Clinic
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clinics</p>
                  <p className="text-2xl font-bold text-gray-900">{clinicsData?.length || 0}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Staff</p>
                  <p className="text-2xl font-bold text-gray-900">{staffData?.length || 0}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Clinics</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {clinicsData?.filter((c: any) => c.status === 1).length || 0}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Clinic Owners</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {staffData?.filter((s: any) => s.isClinicOwner).length || 0}
                  </p>
                </div>
                <Settings className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="clinics">Clinics</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <OverviewTab clinics={clinicsData} staff={staffData} />
        </TabsContent>

        <TabsContent value="clinics" className="mt-6">
          <ClinicsTab
            clinics={clinicsData}
            onAddStaff={(clinicId) => {
              setSelectedClinicId(clinicId);
              setShowStaffWizard(true);
            }}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />
        </TabsContent>

        <TabsContent value="staff" className="mt-6">
          <StaffTab
            staff={staffData}
            clinics={clinicsData}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Tab Components
const OverviewTab = ({ clinics, staff }: any) => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    {/* Recent Clinics */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Building2 className="mr-2 h-5 w-5" />
          Recent Clinics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {clinics?.slice(0, 5).map((clinic: any) => (
            <div key={clinic.clinicId} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h4 className="font-medium">{clinic.clinicName}</h4>
                <p className="text-sm text-gray-600">{clinic.address}</p>
              </div>
              <Badge variant={clinic.status === 1 ? "default" : "secondary"}>
                {clinic.status === 1 ? "Active" : "Inactive"}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>

    {/* Recent Staff */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          Recent Staff
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {staff?.slice(0, 5).map((member: any) => (
            <div key={member.staffId} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h4 className="font-medium">{member.firstName} {member.lastName}</h4>
                <p className="text-sm text-gray-600">{member.jobTitle}</p>
              </div>
              <div className="flex items-center space-x-2">
                {member.isClinicOwner && (
                  <Badge variant="default">Owner</Badge>
                )}
                {member.role && (
                  <Badge variant="outline">{member.role.roleName}</Badge>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

const ClinicsTab = ({ clinics, onAddStaff, searchQuery, setSearchQuery }: any) => {
  const filteredClinics = clinics?.filter((clinic: any) =>
    clinic.clinicName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    clinic.address?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search clinics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Clinics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredClinics.map((clinic: any) => (
          <motion.div
            key={clinic.clinicId}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{clinic.clinicName}</CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Clinic
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddStaff(clinic.clinicId)}>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Add Staff
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="mr-2 h-4 w-4" />
                    {clinic.email}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="mr-2 h-4 w-4" />
                    {clinic.phoneNumber}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Building2 className="mr-2 h-4 w-4" />
                    {clinic.address}
                  </div>
                  <div className="flex items-center justify-between pt-2">
                    <Badge variant={clinic.status === 1 ? "default" : "secondary"}>
                      {clinic.status === 1 ? "Active" : "Inactive"}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      ID: {clinic.clinicId}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredClinics.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No clinics found</h3>
          <p className="mt-2 text-gray-600">
            {searchQuery ? "Try adjusting your search terms" : "Get started by creating your first clinic"}
          </p>
        </div>
      )}
    </div>
  );
};

const StaffTab = ({ staff, clinics, searchQuery, setSearchQuery }: any) => {
  const filteredStaff = staff?.filter((member: any) =>
    member.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.jobTitle?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const getClinicName = (clinicId: number) => {
    const clinic = clinics?.find((c: any) => c.clinicId === clinicId);
    return clinic?.clinicName || `Clinic ${clinicId}`;
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search staff..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Staff Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStaff.map((member: any) => (
          <motion.div
            key={member.staffId}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      {member.firstName} {member.lastName}
                    </CardTitle>
                    <p className="text-sm text-gray-600">{member.jobTitle}</p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Staff
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Shield className="mr-2 h-4 w-4" />
                        Manage Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove Staff
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="mr-2 h-4 w-4" />
                    {member.email}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="mr-2 h-4 w-4" />
                    {member.phoneNumber}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Building2 className="mr-2 h-4 w-4" />
                    {getClinicName(member.clinicId)}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="mr-2 h-4 w-4" />
                    {member.schedule?.workHours ?
                      `${member.schedule.workHours.start} - ${member.schedule.workHours.end}` :
                      'Schedule not set'
                    }
                  </div>
                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center space-x-2">
                      {member.isClinicOwner && (
                        <Badge variant="default">Owner</Badge>
                      )}
                      {member.role && (
                        <Badge variant="outline">{member.role.roleName}</Badge>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">
                      ID: {member.staffId}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredStaff.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No staff found</h3>
          <p className="mt-2 text-gray-600">
            {searchQuery ? "Try adjusting your search terms" : "Get started by adding your first staff member"}
          </p>
        </div>
      )}
    </div>
  );
};