
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
  PawPrint, Heart, Shield, Stethoscope, Scissors, 
  Calendar, Phone, MapPin, Mail 
} from "lucide-react";

const Dashboard = () => {
  const services = [
    {
      icon: <Stethoscope className="h-6 w-6 text-primary-600" />,
      title: "Veterinary Care",
      description: "Professional medical care for your pets",
      bgColor: "bg-blue-50"
    },
    {
      icon: <Scissors className="h-6 w-6 text-primary-600" />,
      title: "Pet Grooming",
      description: "Keep your pets clean and healthy",
      bgColor: "bg-pink-50"
    },
    {
      icon: <Shield className="h-6 w-6 text-primary-600" />,
      title: "Vaccinations",
      description: "Essential vaccines and preventive care",
      bgColor: "bg-purple-50"
    }
  ];

  const prices = [
    {
      title: "Basic Checkup",
      price: 49,
      description: "Regular health examination",
      features: ["Physical examination", "Basic health assessment", "Consultation"]
    },
    {
      title: "Full Service",
      price: 99,
      description: "Complete health care package",
      features: ["Full examination", "Vaccinations", "Lab tests", "Treatment plan"]
    },
    {
      title: "Premium Care",
      price: 149,
      description: "Comprehensive pet care",
      features: ["All basic & full services", "24/7 support", "Home visits", "Emergency care"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-white">
      {/* Hero Section */}
      <section className="relative pt-20 pb-32">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Welcome to Pet Care Dashboard
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Managing your pet's health has never been easier
              </p>
              <Button size="lg" className="mr-4">
                Book Appointment
              </Button>
              <Button variant="outline" size="lg">
                View Services
              </Button>
            </div>
            <div className="relative">
              <img
                src="/lovable-uploads/9cd0f624-8348-47a0-b223-14d4690dc2bc.png"
                alt="Pet care"
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our Services</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className={`p-6 ${service.bgColor} border-none`}>
                <div className="mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                <p className="text-gray-600">{service.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Pricing Plans</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {prices.map((plan, index) => (
              <Card key={index} className="p-6">
                <h3 className="text-xl font-semibold mb-2">{plan.title}</h3>
                <div className="text-3xl font-bold mb-4">${plan.price}</div>
                <p className="text-gray-600 mb-4">{plan.description}</p>
                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <PawPrint className="h-4 w-4 text-primary-600 mr-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full">Choose Plan</Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Contact Us</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="p-6 text-center">
              <Phone className="h-8 w-8 mx-auto mb-4 text-primary-600" />
              <h3 className="text-xl font-semibold mb-2">Phone</h3>
              <p className="text-gray-600">800-234-567</p>
            </Card>
            <Card className="p-6 text-center">
              <MapPin className="h-8 w-8 mx-auto mb-4 text-primary-600" />
              <h3 className="text-xl font-semibold mb-2">Address</h3>
              <p className="text-gray-600">123 Pet Care Street</p>
            </Card>
            <Card className="p-6 text-center">
              <Mail className="h-8 w-8 mx-auto mb-4 text-primary-600" />
              <h3 className="text-xl font-semibold mb-2">Email</h3>
              <p className="text-gray-600"><EMAIL></p>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Dashboard;
