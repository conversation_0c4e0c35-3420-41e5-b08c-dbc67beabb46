import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Save, Calendar, Clock, User, FileText, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import { getAppointmentById } from '@/services/appointments';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface AppointmentType {
  appointmentTypeId: number;
  name: string;
  description: string;
  duration: number;
  price: number;
  currency: string;
}

interface Staff {
  staffId: number;
  firstName: string;
  lastName: string;
  specialization?: string;
}

interface Client {
  clientId: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface Pet {
  petId: number;
  petName: string;
  species: string;
  breed: string;
  clientId: number;
}

const EditAppointment: React.FC = () => {
  const { id: appointmentId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    clientId: '',
    petId: '',
    staffId: '',
    appointmentTypes: [] as number[],
    dateTime: '',
    reason: '',
    notes: '',
    status: 'scheduled'
  });

  // Fetch appointment data
  const { data: appointment, isLoading: isLoadingAppointment } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(parseInt(appointmentId!)),
    enabled: !!appointmentId
  });

  // Fetch appointment types
  const { data: appointmentTypesResponse } = useQuery({
    queryKey: ['appointmentTypes'],
    queryFn: async () => {
      const response = await api.get('/appointment-types');
      return response.data;
    }
  });

  // Fetch staff
  const { data: staffResponse } = useQuery({
    queryKey: ['staff', 'active'],
    queryFn: async () => {
      const response = await api.get('/staff?status=1&limit=100');
      return response.data;
    }
  });

  // Fetch clients
  const { data: clientsResponse } = useQuery({
    queryKey: ['clients'],
    queryFn: async () => {
      const response = await api.get('/clients?limit=100');
      return response.data;
    }
  });

  // Fetch pets for selected client
  const { data: petsResponse } = useQuery({
    queryKey: ['pets', formData.clientId],
    queryFn: async () => {
      if (!formData.clientId) return { data: { data: [] } };
      const response = await api.get(`/pets?clientId=${formData.clientId}`);
      return response.data;
    },
    enabled: !!formData.clientId
  });

  // Update appointment mutation
  const updateMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await api.put(`/appointments/${appointmentId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      toast({
        title: "Success",
        description: "Appointment updated successfully",
      });
      navigate(`/appointments/${appointmentId}`);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update appointment",
        variant: "destructive",
      });
    }
  });

  // Prefill form when appointment data is loaded
  useEffect(() => {
    if (appointment?.data) {
      const apt = appointment.data;
      console.log('Prefilling appointment data:', apt);

      // Handle client data - could be nested object or just ID
      const clientId = apt.client?.clientId || apt.clientId || apt.client?.accountId || '';
      const petId = apt.pet?.petId || apt.petId || '';
      const staffId = apt.staff?.staffId || apt.staffId || '';

      setFormData({
        clientId: clientId.toString(),
        petId: petId.toString(),
        staffId: staffId.toString(),
        appointmentTypes: apt.appointmentTypes?.map((at: any) => at.appointmentTypeId || at.id) || [],
        dateTime: apt.dateTime ? format(new Date(apt.dateTime), "yyyy-MM-dd'T'HH:mm") : '',
        reason: apt.reason || '',
        notes: apt.notes || '',
        status: apt.status || 'scheduled'
      });
    }
  }, [appointment]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.clientId || !formData.petId || !formData.staffId || !formData.dateTime) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const updateData = {
      clientId: parseInt(formData.clientId),
      petId: parseInt(formData.petId),
      staffId: parseInt(formData.staffId),
      appointmentTypes: formData.appointmentTypes.map(id => ({ appointmentTypeId: id })),
      dateTime: new Date(formData.dateTime).toISOString(),
      reason: formData.reason,
      notes: formData.notes,
      status: formData.status
    };

    updateMutation.mutate(updateData);
  };

  const appointmentTypes = appointmentTypesResponse?.data?.data || [];
  const staff = staffResponse?.data?.data || [];
  const clients = clientsResponse?.data?.data || [];
  const pets = petsResponse?.data?.data || [];

  if (isLoadingAppointment) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">Loading appointment...</div>
      </div>
    );
  }

  if (!appointment?.data) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">Appointment not found</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(`/appointments/${appointmentId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Appointment
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Edit Appointment</h1>
          <p className="text-gray-600">Appointment #{appointmentId}</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Client Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Client Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="clientId">Client *</Label>
                <Select value={formData.clientId} onValueChange={(value) => {
                  setFormData({ ...formData, clientId: value, petId: '' });
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select client" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client: Client) => (
                      <SelectItem key={client.clientId} value={client.clientId.toString()}>
                        {client.firstName} {client.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="petId">Pet *</Label>
                <Select value={formData.petId} onValueChange={(value) => {
                  setFormData({ ...formData, petId: value });
                }} disabled={!formData.clientId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select pet" />
                  </SelectTrigger>
                  <SelectContent>
                    {pets.map((pet: Pet) => (
                      <SelectItem key={pet.petId} value={pet.petId.toString()}>
                        {pet.petName} ({pet.species})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Staff and Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Schedule Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="staffId">Assigned Staff *</Label>
                <Select value={formData.staffId} onValueChange={(value) => {
                  setFormData({ ...formData, staffId: value });
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {staff.map((member: Staff) => (
                      <SelectItem key={member.staffId} value={member.staffId.toString()}>
                        Dr. {member.firstName} {member.lastName}
                        {member.specialization && (
                          <span className="text-sm text-gray-500 ml-2">
                            ({member.specialization})
                          </span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="dateTime">Date & Time *</Label>
                <Input
                  id="dateTime"
                  type="datetime-local"
                  value={formData.dateTime}
                  onChange={(e) => setFormData({ ...formData, dateTime: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => {
                  setFormData({ ...formData, status: value });
                }}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="no_show">No Show</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Appointment Types with Beautiful Chips */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Appointment Types
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Selected Types Display */}
            {formData.appointmentTypes.length > 0 && (
              <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg border">
                {formData.appointmentTypes.map((typeId: number) => {
                  const type = appointmentTypes.find((t: AppointmentType) => t.appointmentTypeId === typeId);
                  return (
                    <Badge
                      key={typeId}
                      variant="default"
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer flex items-center gap-1 px-3 py-1"
                      onClick={() => {
                        setFormData({
                          ...formData,
                          appointmentTypes: formData.appointmentTypes.filter(id => id !== typeId)
                        });
                      }}
                    >
                      {type?.name}
                      <X className="h-3 w-3" />
                    </Badge>
                  );
                })}
              </div>
            )}

            {/* Available Types Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {appointmentTypes.map((type: AppointmentType) => {
                const isSelected = formData.appointmentTypes.includes(type.appointmentTypeId);
                return (
                  <div
                    key={type.appointmentTypeId}
                    className={cn(
                      "p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md",
                      isSelected
                        ? "border-blue-500 bg-blue-50 shadow-sm"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={() => {
                      if (isSelected) {
                        setFormData({
                          ...formData,
                          appointmentTypes: formData.appointmentTypes.filter(id => id !== type.appointmentTypeId)
                        });
                      } else {
                        setFormData({
                          ...formData,
                          appointmentTypes: [...formData.appointmentTypes, type.appointmentTypeId]
                        });
                      }
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{type.name}</h4>
                      {isSelected && (
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <X className="h-3 w-3 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-600 mb-1">
                      {type.duration} min
                    </div>
                    <div className="text-xs font-medium text-green-600">
                      {type.currency} {type.price}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Reason and Notes */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="reason">Reason for Visit</Label>
            <Textarea
              id="reason"
              value={formData.reason}
              onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
              placeholder="Describe the reason for this appointment..."
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Any additional notes or instructions..."
              rows={4}
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(`/appointments/${appointmentId}`)}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={updateMutation.isPending}>
            {updateMutation.isPending ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Update Appointment
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EditAppointment;
