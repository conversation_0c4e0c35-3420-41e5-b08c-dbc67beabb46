import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useMutation } from "@tanstack/react-query";
import { adjustInventory, InventoryItem, InventoryAdjustmentParams } from "@/services/inventory";

interface AdjustInventoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inventoryItem: InventoryItem;
  onSuccess?: () => void;
}

const AdjustInventoryModal = ({
  open,
  onOpenChange,
  inventoryItem,
  onSuccess,
}: AdjustInventoryModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<InventoryAdjustmentParams>({
    adjustmentType: 'purchase',
    quantity: 0,
    notes: '',
  });

  const adjustMutation = useMutation({
    mutationFn: (data: InventoryAdjustmentParams) => adjustInventory(inventoryItem._id, data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Inventory adjusted successfully",
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to adjust inventory",
        variant: "destructive",
      });
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Handle number inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: parseFloat(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate quantity
    if (formData.quantity <= 0) {
      toast({
        title: "Error",
        description: "Quantity must be greater than zero",
        variant: "destructive",
      });
      return;
    }
    
    // Check if there's enough stock for deductions
    if (['sale', 'expired', 'damaged'].includes(formData.adjustmentType) && 
        formData.quantity > inventoryItem.quantity) {
      toast({
        title: "Error",
        description: "Insufficient quantity in stock",
        variant: "destructive",
      });
      return;
    }
    
    adjustMutation.mutate(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adjust Inventory: {inventoryItem.name}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="adjustmentType">Adjustment Type</Label>
              <Select
                value={formData.adjustmentType}
                onValueChange={(value) => handleSelectChange('adjustmentType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="purchase">Purchase (Add)</SelectItem>
                  <SelectItem value="sale">Sale (Deduct)</SelectItem>
                  <SelectItem value="adjustment">Adjustment (Add)</SelectItem>
                  <SelectItem value="transfer">Transfer In (Add)</SelectItem>
                  <SelectItem value="expired">Expired (Deduct)</SelectItem>
                  <SelectItem value="damaged">Damaged (Deduct)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quantity">
                Quantity ({['purchase', 'adjustment', 'transfer'].includes(formData.adjustmentType) ? 'Add' : 'Deduct'})
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="quantity"
                  name="quantity"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={formData.quantity || ''}
                  onChange={handleChange}
                  required
                />
                <span className="text-sm text-muted-foreground">{inventoryItem.unit}</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Current stock: {inventoryItem.quantity} {inventoryItem.unit}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                placeholder="Add details about this adjustment"
                rows={3}
              />
            </div>
            
            {/* Optional related record fields */}
            <div className="space-y-2">
              <Label htmlFor="relatedRecordType">Related Record (Optional)</Label>
              <Select
                value={formData.relatedRecordType || ''}
                onValueChange={(value) => handleSelectChange('relatedRecordType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select record type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  <SelectItem value="healthRecord">Health Record</SelectItem>
                  <SelectItem value="purchase">Purchase Order</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {formData.relatedRecordType && (
              <div className="space-y-2">
                <Label htmlFor="relatedRecordId">Record ID</Label>
                <Input
                  id="relatedRecordId"
                  name="relatedRecordId"
                  value={formData.relatedRecordId || ''}
                  onChange={handleChange}
                  placeholder="Enter record ID"
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={adjustMutation.isPending}>
              {adjustMutation.isPending ? "Processing..." : "Adjust Inventory"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AdjustInventoryModal;
