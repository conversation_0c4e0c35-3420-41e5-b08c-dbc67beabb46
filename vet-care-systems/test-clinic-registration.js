import mongoose from 'mongoose';
import { config } from 'dotenv';
import { DB_URI } from './config/env.js';
import Clinic from './models/clinic.model.js';
import Staff from './models/staff.model.js';

// Load environment variables
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

const testClinicRegistration = async () => {
    try {
        console.log('Connecting to database...');
        await mongoose.connect(DB_URI);
        console.log('Connected to database');

        // Check clinics
        console.log('\n=== Checking Clinics ===');
        const clinics = await Clinic.find().lean();
        console.log(`Found ${clinics.length} clinics:`);
        clinics.forEach(clinic => {
            console.log(`- ID: ${clinic.clinicId}, Name: ${clinic.clinicName}, Owner: ${clinic.owner}`);
        });

        // Check staff
        console.log('\n=== Checking Staff ===');
        const staff = await Staff.find().lean();
        console.log(`Found ${staff.length} staff members:`);
        staff.forEach(member => {
            console.log(`- ID: ${member.staffId}, Name: ${member.firstName} ${member.lastName}, Email: ${member.email}, Clinic: ${member.clinicId}`);
        });

        await mongoose.disconnect();
        console.log('\n=== Test complete ===');
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
};

testClinicRegistration();
