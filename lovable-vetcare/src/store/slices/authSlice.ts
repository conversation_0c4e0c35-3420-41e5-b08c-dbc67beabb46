/**
 * Auth Store Slice
 *
 * This slice handles authentication state and operations.
 */

import { StateCreator } from 'zustand';
import { api } from '@/services/api';
import { User, Staff, StandardResponse } from '../types';

// Auth slice state interface
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  employee: Staff | null;
  clinic: any | null; // TODO: Add proper clinic type
}

// Auth slice actions interface
export interface AuthActions {
  login: (email: string, password: string) => Promise<StandardResponse<{
    token: string;
    user: User;
    staff?: Staff;
  }>>;
  logout: () => void;
  getToken: () => string | null;
  updateUser: (updatedUser: Partial<User>) => void;
  updateStaff: (updatedStaff: Partial<Staff>) => void;
}

// Combined auth slice type
export type AuthSlice = AuthState & AuthActions;

// Auth slice creator function
export const createAuthSlice: StateCreator<
  AuthSlice,
  [],
  [],
  AuthSlice
> = (set, get) => ({
  // Initial state
  isAuthenticated: false,
  user: null,
  token: null,
  employee: null,
  clinic: null,

  // Actions
  login: async (email, password) => {
    try {
      const response = await api.post<StandardResponse<{
        token: string;
        user: User;
        staff?: Staff;
      }>>('/auth/sign-in', { email, password });

      const { status, message, data } = response;

      if (status === 200) {
        set({
          isAuthenticated: true,
          user: data.user,
          token: data.token,
          employee: data.staff,
          clinic: data.staff?.primaryClinicId ? { clinicId: data.staff.primaryClinicId } : null
        });
        return {
          success: true,
          status,
          message: message || 'User signed in successfully',
          data
        };
      }
      return {
        success: false,
        status,
        message: message || 'Login failed',
        data
      };
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Login failed',
        data: error?.data || {}
      };
    }
  },

  logout: () => {
    // Clear all auth state
    set({
      isAuthenticated: false,
      user: null,
      token: null,
      employee: null,
      clinic: null
    });

    // Clear localStorage to ensure all persisted auth data is removed
    localStorage.removeItem('vet-care-store');

    // Redirect to home page
    window.location.href = '/';
  },

  getToken: () => get().token,

  updateUser: (updatedUser) => {
    const currentUser = get().user;
    if (currentUser) {
      set({
        user: { ...currentUser, ...updatedUser }
      });
    }
  },

  updateStaff: (updatedStaff) => {
    const currentStaff = get().employee;
    if (currentStaff) {
      set({
        employee: { ...currentStaff, ...updatedStaff }
      });
    }
  },
});
