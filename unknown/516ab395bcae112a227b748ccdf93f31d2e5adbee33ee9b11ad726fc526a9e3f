/**
 * Utility for standardized API responses
 */

// Standard response format for all API endpoints
export const sendResponse = (res, status, success, message, data = null) => {
    const response = { success, status, message };
    if (data) response.data = data;
    return res.status(200).json(response);
};

// Pagination helper for list endpoints
export const paginateResults = (data, totalCount, page, limit) => ({
    data,
    pagination: {
        totalCount: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        totalPages: Math.ceil(totalCount / limit),
    }
});
