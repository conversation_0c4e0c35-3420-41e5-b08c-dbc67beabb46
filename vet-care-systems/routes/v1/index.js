/**
 * API v1 Routes
 *
 * This file centralizes all v1 API routes.
 */

import { Router } from "express";
import authRouter from "./auth.routes.js";
import csrfRouter from "./csrf.routes.js";

const v1Router = Router();

// Mount CSRF routes first to ensure token availability
v1Router.use('/', csrfRouter);

// Mount all resource routers
v1Router.use('/auth', authRouter);

// Add other v1 routes as they are migrated
// v1Router.use('/users', userRouter);
// v1Router.use('/staff', staffRouter);
// v1Router.use('/clinics', clinicsRouter);
// etc.

export default v1Router;
