import express from 'express';
import {
    createClient,
    getAllClients,
    getClientById,
    updateClient,
    deleteClient
} from '../controllers/client.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Base route: /api/clients

// Protected routes
router.use(verifyToken);

router.post('/', createClient);
router.get('/', getAllClients);
router.get('/:id', getClientById);
router.put('/:accountId', updateClient);
router.delete('/:accountId', deleteClient);

export default router;