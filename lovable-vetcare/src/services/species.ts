import { Species, PaginationData, MessageResponse } from '@/store/types';
import { api } from './api';
import { ClientSearchParams } from "@/services/clients";

interface SpeciesResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        data: Species[];
        pagination: PaginationData;
    };
}

interface SingleSpeciesResponse {
    success: boolean;
    status: number;
    message: string;
    data: Species;
}

export const getSpecies = async (params: ClientSearchParams = {}): Promise<SpeciesResponse> => {
    try {
        const queryParams = new URLSearchParams();

        queryParams.append('page', String(params.page || 1));
        queryParams.append('offset', String(params.offset || 0));
        queryParams.append('limit', String(params.limit || 10));

        Object.entries(params).forEach(([key, value]) => {
            if (value && !['page', 'offset', 'limit'].includes(key)) {
                queryParams.append(key, String(value));
            }
        });

        return await api.get<SpeciesResponse>(`/species?${queryParams.toString()}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to fetch species',
            data: {
                data: [],
                pagination: {
                    totalCount: 0,
                    page: 1,
                    limit: 10,
                    offset: 0,
                    totalPages: 0
                }
            }
        };
    }
};

export const createSpecies = async (data: Partial<Species>, file?: File): Promise<SingleSpeciesResponse> => {
    try {
        let response;

        if (file) {
            // If we have a file, use FormData
            const formData = new FormData();

            // Add all data fields to FormData
            Object.entries(data).forEach(([key, value]) => {
                if (value !== undefined) {
                    formData.append(key, String(value));
                }
            });

            // Add the file
            formData.append('image', file);

            response = await api.post<SingleSpeciesResponse>('/species', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
        } else {
            // No file, just send JSON
            response = await api.post<SingleSpeciesResponse>('/species', data);
        }

        return response;
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to create species',
            data: null
        };
    }
};

export const updateSpecies = async (id: string, data: Partial<Species>, file?: File): Promise<SingleSpeciesResponse> => {
    try {
        let response;

        if (file) {
            // If we have a file, use FormData
            const formData = new FormData();

            // Add all data fields to FormData
            Object.entries(data).forEach(([key, value]) => {
                if (value !== undefined) {
                    formData.append(key, String(value));
                }
            });

            // Add the file
            formData.append('image', file);

            response = await api.put<SingleSpeciesResponse>(`/species/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
        } else {
            // No file, just send JSON
            response = await api.put<SingleSpeciesResponse>(`/species/${id}`, data);
        }

        return response;
    } catch (error: any) {
        throw {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to update species',
            data: null
        };
    }
};

export const deleteSpecies = async (id: string): Promise<MessageResponse> => {
    try {
        return await api.delete<MessageResponse>(`/species/${id}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to delete species'
        };
    }
};
