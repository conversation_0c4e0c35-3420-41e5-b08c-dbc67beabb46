import { Breed, MessageResponse, PaginationData } from "@/store/types";
import { ClientSearchParams } from "@/services/clients.ts";
import { api } from "@/services/api";

export interface BreedResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        data: Breed[];
        pagination: PaginationData;
    };
}

export interface SingleBreedResponse {
    success: boolean;
    status: number;
    message: string;
    data: Breed;
}

export const createBreed = async (breedData: Omit<Breed, never>): Promise<SingleBreedResponse> => {
    try {
        return await api.post<SingleBreedResponse>('/breeds', breedData);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to create breed',
            data: null
        };
    }
};

export const getBreeds = async (params: {
    page?: number;
    offset?: number;
    limit?: number;
    search?: string;
    speciesId?: string;
    sizeCategory?: string;
    origin?: string;
    minLifespan?: number;
    maxLifespan?: number;
    [key: string]: any;
} = {}): Promise<BreedResponse> => {
  try {
    const queryParams = new URLSearchParams();

    // Add default pagination parameters
    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    // Add all other parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<BreedResponse>(`/breeds?${queryParams.toString()}`);
  } catch (error: any) {
    console.error("Error fetching breeds:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch breeds',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: params.page || 1,
          limit: params.limit || 10,
          offset: ((params.page || 1) - 1) * (params.limit || 10),
          totalPages: 0
        }
      }
    };
  }
};

export const getBreed = async (params: { [key: string]: string }): Promise<SingleBreedResponse> => {
    try {
        const queryString = new URLSearchParams(params).toString();
        return await api.get<SingleBreedResponse>(`/breeds?${queryString}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to fetch breed',
            data: null
        };
    }
};

export const updateBreed = async (breedId: string, breedData: Partial<Breed>): Promise<SingleBreedResponse> => {
    try {
        return await api.put<SingleBreedResponse>(`/breeds/${breedId}`, breedData);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to update breed',
            data: null
        };
    }
};

export const deleteBreed = async (breedId: string,): Promise<MessageResponse> => {
    try {
        return await api.delete<MessageResponse>(`/breeds/${breedId}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to delete breed',
        };
    }
};

// Updated to accept an object with multiple query parameters
export const getBreedsBySpecies = async (params: {
    speciesId?: string;
    name?: string;
    [key: string]: any; // Allow any additional parameters
}): Promise<BreedResponse> => {
    try {
        // Create a URLSearchParams object for the query parameters
        const queryParams = new URLSearchParams();

        // Add pagination defaults
        queryParams.append('page', '1');
        queryParams.append('limit', '50');

        // Add all provided params to the query string
        Object.entries(params).forEach(([key, value]) => {
            if (value) {
                queryParams.append(key, String(value));
            }
        });

        return await api.get<BreedResponse>(`/breeds?${queryParams.toString()}`);
    } catch (error: any) {
        console.error("Error in getBreedsBySpecies:", error);
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to fetch breeds',
            data: {
                data: [],
                pagination: {
                    totalCount: 0,
                    page: 1,
                    limit: 10,
                    offset: 0,
                    totalPages: 0
                }
            }
        };
    }
};
