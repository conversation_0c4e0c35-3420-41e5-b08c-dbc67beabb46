import { Router } from 'express';
import {
  getApi<PERSON>eys,
  addApi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>,
  deleteApiKey,
  getSettings,
  updateSettings,
  generateServiceNotes
} from '../controllers/admin.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const adminRouter = Router();

// Apply authentication to all admin routes
adminRouter.use(verifyToken);

// API Keys management
adminRouter.get('/api-keys', getApiKeys);
adminRouter.post('/api-keys', addApiKey);
adminRouter.put('/api-keys/:keyId', updateApiKey);
adminRouter.delete('/api-keys/:keyId', deleteApiKey);

// System settings
adminRouter.get('/settings', getSettings);
adminRouter.put('/settings', updateSettings);

// AI services
adminRouter.post('/ai/generate-service-notes', generateServiceNotes);

export default adminRouter;
