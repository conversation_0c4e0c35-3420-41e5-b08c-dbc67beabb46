
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createClient } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { Client } from "@/store/types";
import { InputField } from "@/components/common";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    middleName: z.string().optional(),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Invalid email address"),
    phoneNumber: z.string().min(1, "Phone number is required"),
    area: z.string().min(1, "Area/Town is required"),
    address: z.string().min(1, "Address is required"),
    buildingName: z.string().optional(),
    houseNumber: z.string().optional()
});

type FormValues = z.infer<typeof formSchema>;

const AddClient = () => {
    const navigate = useNavigate();
    const {toast} = useToast();
    const queryClient = useQueryClient();

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: "",
            middleName: "",
            lastName: "",
            email: "",
            phoneNumber: "",
            area: "",
            address: "",
            buildingName: "",
            houseNumber: ""
        },
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = async (values: FormValues) => {
        setIsSubmitting(true);
        try {
            // Ensure all required fields are present before making API call
            const client: Omit<Client, never> = {
                accountId: "",
                firstName: values.firstName,
                middleName: values.middleName,
                lastName: values.lastName,
                email: values.email,
                phoneNumber: values.phoneNumber,
                area: values.area,
                address: values.address,
                buildingName: values.buildingName,
                houseNumber: values.houseNumber,
                homeLocation: [0, 0], // Default coordinates
                clientStatus: 1 // Active
            };

            const response = await createClient(client);
            await queryClient.invalidateQueries({queryKey: ["clients"]});

            if (response.status === 201) {
                toast({
                    title: "Success",
                    description: "Client created successfully",
                    duration: 2500,
                });
                navigate("/clients");
            } else {
                toast({
                    title: "Error",
                    description: response.message || "Failed to create client",
                    variant: "destructive",
                    duration: 2500,
                });
            }
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to create client",
                variant: "destructive",
                duration: 2500,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="p-8">
            <Card>
                <CardHeader>
                    <CardTitle>Add New Client</CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <InputField
                                    name="firstName"
                                    label="First Name"
                                    placeholder="Enter first name"
                                    form={form}
                                    required
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">FN</span>}
                                />
                                <InputField
                                    name="middleName"
                                    label="Middle Name"
                                    placeholder="Enter middle name (optional)"
                                    form={form}
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">MN</span>}
                                />
                                <InputField
                                    name="lastName"
                                    label="Last Name"
                                    placeholder="Enter last name"
                                    form={form}
                                    required
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">LN</span>}
                                />
                                <InputField
                                    name="email"
                                    label="Email"
                                    placeholder="Enter email address"
                                    form={form}
                                    required
                                    type="email"
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">@</span>}
                                />
                                <InputField
                                    name="phoneNumber"
                                    label="Phone"
                                    placeholder="Enter phone number"
                                    form={form}
                                    required
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">☎</span>}
                                />
                                <InputField
                                    name="area"
                                    label="Area/Town"
                                    placeholder="Enter area or town"
                                    form={form}
                                    required
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">📍</span>}
                                />
                                <InputField
                                    name="address"
                                    label="Address"
                                    placeholder="Enter address"
                                    form={form}
                                    required
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">🏠</span>}
                                />
                                <InputField
                                    name="buildingName"
                                    label="Building Name"
                                    placeholder="Enter building name (optional)"
                                    form={form}
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">🏢</span>}
                                />
                                <InputField
                                    name="houseNumber"
                                    label="House Number"
                                    placeholder="Enter house number (optional)"
                                    form={form}
                                    showValidation
                                    icon={<span className="text-xs font-medium text-muted-foreground">#</span>}
                                />
                            </div>
                            <div className="flex justify-end space-x-2 mt-6">
                                <Button
                                    variant="outline"
                                    onClick={() => navigate("/clients")}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="min-w-[120px]"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Saving...
                                        </>
                                    ) : (
                                        "Save Client"
                                    )}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    );
};

export default AddClient;
