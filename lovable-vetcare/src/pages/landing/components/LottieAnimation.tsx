import { useRef, useEffect, useState } from 'react';
import <PERSON><PERSON>, { LottieRefCurrentProps } from 'lottie-react';
import { useInView } from 'framer-motion';

interface LottieAnimationProps {
  animationData?: any;
  animationPath?: string;
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
  speed?: number;
  style?: React.CSSProperties;
}

const LottieAnimation = ({
  animationData,
  animationPath,
  className = '',
  loop = true,
  autoplay = true,
  speed = 1,
  style = {},
}: LottieAnimationProps) => {
  const lottieRef = useRef<LottieRefCurrentProps>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: false, amount: 0.3 });
  const [loadedAnimationData, setLoadedAnimationData] = useState<any>(animationData);

  // Load animation data from path if provided
  useEffect(() => {
    if (animationPath && !animationData) {
      fetch(animationPath)
        .then(response => response.json())
        .then(data => {
          setLoadedAnimationData(data);
        })
        .catch(error => {
          console.error('Error loading Lottie animation:', error);
        });
    }
  }, [animationPath, animationData]);

  useEffect(() => {
    if (lottieRef.current) {
      if (isInView) {
        lottieRef.current.play();
      } else {
        lottieRef.current.pause();
      }
    }
  }, [isInView]);

  useEffect(() => {
    if (lottieRef.current) {
      lottieRef.current.setSpeed(speed);
    }
  }, [speed]);

  if (!loadedAnimationData && !animationData) {
    return <div ref={containerRef} className={className} style={style}></div>;
  }

  return (
    <div ref={containerRef} className={className}>
      <Lottie
        lottieRef={lottieRef}
        animationData={loadedAnimationData || animationData}
        loop={loop}
        autoplay={autoplay && isInView}
        style={style}
      />
    </div>
  );
};

export default LottieAnimation;
