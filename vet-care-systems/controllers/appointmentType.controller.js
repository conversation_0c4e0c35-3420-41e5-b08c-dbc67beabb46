import AppointmentType from '../models/appointmentType.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import { appointmentTypeSchema } from '../models/appointmentType.model.js';

/**
 * Create a new appointment type
 */
export const createAppointmentType = async (req, res) => {
    const { error } = appointmentTypeSchema.validate(req.body);
    if (error) {
        return sendResponse(res, 400, false, error.details[0].message);
    }
    
    try {
        const { name, description, duration, price, color, clinicId, isActive } = req.body;
        
        // Check if appointment type with same name exists for this clinic
        const existingType = await AppointmentType.findOne({ 
            name: name,
            clinicId: clinicId
        });
        
        if (existingType) {
            return sendResponse(res, 409, false, "Appointment type with this name already exists for this clinic");
        }
        
        const appointmentType = await AppointmentType.create({
            name,
            description,
            duration,
            price,
            color,
            clinicId,
            isActive: isActive !== undefined ? isActive : true
        });
        
        return sendResponse(res, 201, true, "Appointment type created successfully", appointmentType);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get all appointment types with pagination and search
 */
export const getAllAppointmentTypes = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10,
            sortBy = "name",
            sortOrder = "asc",
            search,
            clinicId,
            isActive
        } = req.query;
        
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };
        
        let query = {};
        if (clinicId) query.clinicId = clinicId;
        if (isActive !== undefined) query.isActive = isActive === 'true';
        
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: "i" } },
                { description: { $regex: search, $options: "i" } }
            ];
        }

        const [types, totalCount] = await Promise.all([
            AppointmentType.find(query)
                .populate('clinicId', 'clinicName')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            AppointmentType.countDocuments(query)
        ]);

        return sendResponse(
            res,
            200,
            true,
            "Appointment types retrieved successfully",
            paginateResults(types, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get appointment type by ID
 */
export const getAppointmentTypeById = async (req, res) => {
    try {
        const appointmentType = await AppointmentType.findOne({ appointmentTypeId: parseInt(req.params.appointmentTypeId) })
            .populate('clinicId', 'clinicName')
            .lean();
            
        if (!appointmentType) {
            return sendResponse(res, 404, false, "Appointment type not found");
        }
        
        return sendResponse(res, 200, true, "Appointment type retrieved successfully", appointmentType);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update appointment type
 */
export const updateAppointmentType = async (req, res) => {
    const { error } = appointmentTypeSchema.validate(req.body);
    if (error) {
        return sendResponse(res, 400, false, error.details[0].message);
    }
    
    try {
        const appointmentType = await AppointmentType.findOneAndUpdate(
            { appointmentTypeId: parseInt(req.params.appointmentTypeId) },
            { $set: req.body },
            { new: true, runValidators: true }
        ).populate('clinicId', 'clinicName');
        
        if (!appointmentType) {
            return sendResponse(res, 404, false, "Appointment type not found");
        }
        
        return sendResponse(res, 200, true, "Appointment type updated successfully", appointmentType);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete appointment type
 */
export const deleteAppointmentType = async (req, res) => {
    try {
        const appointmentType = await AppointmentType.findOneAndDelete({ appointmentTypeId: parseInt(req.params.appointmentTypeId) });
        
        if (!appointmentType) {
            return sendResponse(res, 404, false, "Appointment type not found");
        }
        
        return sendResponse(res, 200, true, "Appointment type deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};