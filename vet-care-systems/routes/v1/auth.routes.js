import { Router } from "express";
import { signUp, signIn } from "../../controllers/auth.controller.js";

const authRouter = Router();

/**
 * @route POST /api/v1/auth/sign-up
 * @desc Register a new user
 * @access Public
 */
authRouter.post('/sign-up', signUp);

/**
 * @route POST /api/v1/auth/sign-in
 * @desc Authenticate user and get token
 * @access Public
 */
authRouter.post('/sign-in', signIn);

// Future endpoints
// authRouter.post('/sign-out', signOut);
// authRouter.post('/refresh-token', refreshToken);
// authRouter.post('/forgot-password', forgotPassword);
// authRouter.post('/reset-password', resetPassword);

export default authRouter;
