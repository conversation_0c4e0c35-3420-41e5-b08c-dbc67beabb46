{"v": "5.7.4", "fr": 29.9700012207031, "ip": 0, "op": 189.000007698128, "w": 1080, "h": 1080, "nm": "Time Scheduling", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Color Control", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [525.816, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [960, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Title Color", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 1}}]}, {"ty": 5, "nm": "Description Color", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 1}}]}, {"ty": 5, "nm": "Background Color", "np": 10, "mn": "ADBE Ramp", "ix": 3, "en": 1, "ef": [{"ty": 3, "nm": "Start of Ramp", "mn": "ADBE Ramp-0001", "ix": 1, "v": {"a": 0, "k": [911.25, 822], "ix": 1}}, {"ty": 2, "nm": "Start Color", "mn": "ADBE Ramp-0002", "ix": 2, "v": {"a": 0, "k": [0.372549027205, 0.039215687662, 0.529411792755, 1], "ix": 2}}, {"ty": 3, "nm": "End of Ramp", "mn": "ADBE Ramp-0003", "ix": 3, "v": {"a": 0, "k": [99, 80], "ix": 3}}, {"ty": 2, "nm": "End Color", "mn": "ADBE Ramp-0004", "ix": 4, "v": {"a": 0, "k": [0.643137276173, 0.313725501299, 0.54509806633, 1], "ix": 4}}, {"ty": 7, "nm": "<PERSON><PERSON>", "mn": "ADBE Ramp-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 0, "nm": "<PERSON><PERSON>", "mn": "ADBE Ramp-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Blend With Original", "mn": "ADBE Ramp-0007", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 6, "nm": "", "mn": "ADBE Ramp-0008", "ix": 8, "v": 0}]}], "ip": 0, "op": 600.000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 8 Outlines", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [40.97, 72.646, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [72.062, 40.678, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[7.514, 3.95], [-7.514, -1.998], [-6.581, -3.95]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [136.361, 77.157], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.655, -1.385], [1.24, -1.837], [-0.977, -0.379], [-1.007, 2.099], [-0.466, 1.282], [0.933, 0.539]], "o": [[-1.05, 2.186], [1.021, 0.393], [1.18, -1.778], [0.612, -1.312], [-0.873, -0.525], [-0.495, 1.356]], "v": [[-0.501, -1.539], [-3.958, 4.512], [-0.968, 5.648], [2.326, -0.182], [3.958, -4.06], [1.218, -5.648]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [111.741, 65.751], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.18, -1.779], [-0.962, 2.027], [11.969, 7.055], [0.613, -1.313]], "o": [[13.148, 4.897], [0.962, -2.026], [-0.466, 1.282], [-1.006, 2.099]], "v": [[-12.076, 1.341], [11.114, 6.34], [-7.15, -8.367], [-8.783, -4.49]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [122.849, 70.059], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.496, 1.356], [9.825, 4.678], [1.589, -3.353], [-27.71, -13.193], [-7.944, -3.076], [-1.05, 2.186]], "o": [[-7.448, -4.271], [-27.712, -13.193], [-1.603, 3.353], [9.708, 4.606], [1.239, -1.838], [0.655, -1.384]], "v": [[40.458, 12.317], [14.191, -1.326], [-38.855, -19.125], [8.418, 10.816], [35.283, 22.478], [38.739, 16.427]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [72.5, 47.785], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12.063, 7.478], [-13.418, -4.649], [-12.077, -7.478], [13.418, 4.65]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.26699999641, 0.328999986836, 0.426999978458, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.098, 30.773], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.751, -12.318], [-30.182, -5.83], [10.561, 12.318], [30.182, 1.299]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.431, 38.441], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-11.443, 1.872], [-21.807, -21.173], [17.959, -1.014], [21.807, 21.173]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.465, 21.423], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 10 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [743.208, 723.315, 0], "to": [0, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [743.208, 724.315, 0], "to": [0, 0.333, 0], "ti": [0, -0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 2, "s": [743.208, 725.313, 0], "to": [0, 0.33, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 3, "s": [743.208, 726.297, 0], "to": [0, 0.324, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 4, "s": [743.208, 727.259, 0], "to": [0, 0.315, 0], "ti": [0, -0.303, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 5, "s": [743.208, 728.188, 0], "to": [0, 0.303, 0], "ti": [0, -0.287, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 6, "s": [743.208, 729.076, 0], "to": [0, 0.287, 0], "ti": [0, -0.269, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 7, "s": [743.208, 729.913, 0], "to": [0, 0.269, 0], "ti": [0, -0.248, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 8, "s": [743.208, 730.691, 0], "to": [0, 0.248, 0], "ti": [0, -0.225, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 9, "s": [743.208, 731.402, 0], "to": [0, 0.225, 0], "ti": [0, -0.199, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 10, "s": [743.208, 732.04, 0], "to": [0, 0.199, 0], "ti": [0, -0.171, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 11, "s": [743.208, 732.597, 0], "to": [0, 0.171, 0], "ti": [0, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 12, "s": [743.208, 733.068, 0], "to": [0, 0.142, 0], "ti": [0, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.194}, "t": 13, "s": [743.208, 733.449, 0], "to": [0, 0.111, 0], "ti": [0, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.21}, "t": 14, "s": [743.208, 733.735, 0], "to": [0, 0.079, 0], "ti": [0, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.258}, "t": 15, "s": [743.208, 733.924, 0], "to": [0, 0.047, 0], "ti": [0, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.56}, "o": {"x": 0.167, "y": 0.376}, "t": 16, "s": [743.208, 734.014, 0], "to": [0, 0.013, 0], "ti": [0, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.758}, "o": {"x": 0.167, "y": 0.103}, "t": 17, "s": [743.208, 734.004, 0], "to": [0, -0.02, 0], "ti": [0, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.127}, "t": 18, "s": [743.208, 733.894, 0], "to": [0, -0.053, 0], "ti": [0, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.14}, "t": 19, "s": [743.208, 733.685, 0], "to": [0, -0.086, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.147}, "t": 20, "s": [743.208, 733.38, 0], "to": [0, -0.117, 0], "ti": [0, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 21, "s": [743.208, 732.98, 0], "to": [0, -0.148, 0], "ti": [0, 0.177, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 22, "s": [743.208, 732.492, 0], "to": [0, -0.177, 0], "ti": [0, 0.204, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 23, "s": [743.208, 731.918, 0], "to": [0, -0.204, 0], "ti": [0, 0.23, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 24, "s": [743.208, 731.265, 0], "to": [0, -0.23, 0], "ti": [0, 0.253, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 25, "s": [743.208, 730.54, 0], "to": [0, -0.253, 0], "ti": [0, 0.273, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 26, "s": [743.208, 729.749, 0], "to": [0, -0.273, 0], "ti": [0, 0.291, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 27, "s": [743.208, 728.901, 0], "to": [0, -0.291, 0], "ti": [0, 0.306, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 28, "s": [743.208, 728.004, 0], "to": [0, -0.306, 0], "ti": [0, 0.317, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 29, "s": [743.208, 727.068, 0], "to": [0, -0.317, 0], "ti": [0, 0.326, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 30, "s": [743.208, 726.101, 0], "to": [0, -0.326, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 31, "s": [743.208, 725.113, 0], "to": [0, -0.331, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [743.208, 724.114, 0], "to": [0, -0.333, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [743.208, 723.114, 0], "to": [0, -0.332, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 34, "s": [743.208, 722.124, 0], "to": [0, -0.327, 0], "ti": [0, 0.319, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 35, "s": [743.208, 721.152, 0], "to": [0, -0.319, 0], "ti": [0, 0.308, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 36, "s": [743.208, 720.209, 0], "to": [0, -0.308, 0], "ti": [0, 0.294, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 37, "s": [743.208, 719.304, 0], "to": [0, -0.294, 0], "ti": [0, 0.277, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 38, "s": [743.208, 718.447, 0], "to": [0, -0.277, 0], "ti": [0, 0.257, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 39, "s": [743.208, 717.645, 0], "to": [0, -0.257, 0], "ti": [0, 0.234, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 40, "s": [743.208, 716.907, 0], "to": [0, -0.234, 0], "ti": [0, 0.209, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 41, "s": [743.208, 716.241, 0], "to": [0, -0.209, 0], "ti": [0, 0.182, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 42, "s": [743.208, 715.652, 0], "to": [0, -0.182, 0], "ti": [0, 0.153, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.184}, "t": 43, "s": [743.208, 715.147, 0], "to": [0, -0.153, 0], "ti": [0, 0.123, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.191}, "t": 44, "s": [743.208, 714.731, 0], "to": [0, -0.123, 0], "ti": [0, 0.092, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.202}, "t": 45, "s": [743.208, 714.408, 0], "to": [0, -0.092, 0], "ti": [0, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.231}, "t": 46, "s": [743.208, 714.181, 0], "to": [0, -0.059, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.735}, "o": {"x": 0.167, "y": 0.409}, "t": 47, "s": [743.208, 714.053, 0], "to": [0, -0.026, 0], "ti": [0, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.718}, "o": {"x": 0.167, "y": 0.122}, "t": 48, "s": [743.208, 714.025, 0], "to": [0, 0.007, 0], "ti": [0, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.118}, "t": 49, "s": [743.208, 714.096, 0], "to": [0, 0.04, 0], "ti": [0, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.136}, "t": 50, "s": [743.208, 714.267, 0], "to": [0, 0.073, 0], "ti": [0, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 51, "s": [743.208, 714.536, 0], "to": [0, 0.105, 0], "ti": [0, -0.136, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.15}, "t": 52, "s": [743.208, 714.9, 0], "to": [0, 0.136, 0], "ti": [0, -0.166, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 53, "s": [743.208, 715.355, 0], "to": [0, 0.166, 0], "ti": [0, -0.194, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 54, "s": [743.208, 715.896, 0], "to": [0, 0.194, 0], "ti": [0, -0.22, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 55, "s": [743.208, 716.519, 0], "to": [0, 0.22, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 56, "s": [743.208, 717.218, 0], "to": [0, 0.244, 0], "ti": [0, -0.266, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 57, "s": [743.208, 717.984, 0], "to": [0, 0.266, 0], "ti": [0, -0.284, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 58, "s": [743.208, 718.811, 0], "to": [0, 0.284, 0], "ti": [0, -0.3, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 59, "s": [743.208, 719.69, 0], "to": [0, 0.3, 0], "ti": [0, -0.313, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 60, "s": [743.208, 720.612, 0], "to": [0, 0.313, 0], "ti": [0, -0.323, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 61, "s": [743.208, 721.569, 0], "to": [0, 0.323, 0], "ti": [0, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 62, "s": [743.208, 722.55, 0], "to": [0, 0.329, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 63, "s": [743.208, 723.546, 0], "to": [0, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [743.208, 724.546, 0], "to": [0, 0.333, 0], "ti": [0, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 65, "s": [743.208, 725.542, 0], "to": [0, 0.329, 0], "ti": [0, -0.323, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 66, "s": [743.208, 726.522, 0], "to": [0, 0.323, 0], "ti": [0, -0.313, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 67, "s": [743.208, 727.477, 0], "to": [0, 0.313, 0], "ti": [0, -0.3, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 68, "s": [743.208, 728.397, 0], "to": [0, 0.3, 0], "ti": [0, -0.283, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 69, "s": [743.208, 729.274, 0], "to": [0, 0.283, 0], "ti": [0, -0.265, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 70, "s": [743.208, 730.098, 0], "to": [0, 0.265, 0], "ti": [0, -0.243, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 71, "s": [743.208, 730.861, 0], "to": [0, 0.243, 0], "ti": [0, -0.219, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 72, "s": [743.208, 731.556, 0], "to": [0, 0.219, 0], "ti": [0, -0.193, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 73, "s": [743.208, 732.176, 0], "to": [0, 0.193, 0], "ti": [0, -0.165, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.183}, "t": 74, "s": [743.208, 732.713, 0], "to": [0, 0.165, 0], "ti": [0, -0.135, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 75, "s": [743.208, 733.164, 0], "to": [0, 0.135, 0], "ti": [0, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.197}, "t": 76, "s": [743.208, 733.523, 0], "to": [0, 0.104, 0], "ti": [0, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.216}, "t": 77, "s": [743.208, 733.788, 0], "to": [0, 0.072, 0], "ti": [0, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.291}, "t": 78, "s": [743.208, 733.954, 0], "to": [0, 0.039, 0], "ti": [0, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.612}, "o": {"x": 0.167, "y": 0.236}, "t": 79, "s": [743.208, 734.021, 0], "to": [0, 0.006, 0], "ti": [0, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.106}, "t": 80, "s": [743.208, 733.988, 0], "to": [0, -0.028, 0], "ti": [0, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.131}, "t": 81, "s": [743.208, 733.855, 0], "to": [0, -0.061, 0], "ti": [0, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 82, "s": [743.208, 733.623, 0], "to": [0, -0.093, 0], "ti": [0, 0.125, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 83, "s": [743.208, 733.296, 0], "to": [0, -0.125, 0], "ti": [0, 0.155, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 84, "s": [743.208, 732.875, 0], "to": [0, -0.155, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 85, "s": [743.208, 732.366, 0], "to": [0, -0.184, 0], "ti": [0, 0.21, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 86, "s": [743.208, 731.774, 0], "to": [0, -0.21, 0], "ti": [0, 0.235, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 87, "s": [743.208, 731.104, 0], "to": [0, -0.235, 0], "ti": [0, 0.258, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 88, "s": [743.208, 730.363, 0], "to": [0, -0.258, 0], "ti": [0, 0.277, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 89, "s": [743.208, 729.558, 0], "to": [0, -0.277, 0], "ti": [0, 0.294, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 90, "s": [743.208, 728.698, 0], "to": [0, -0.294, 0], "ti": [0, 0.309, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 91, "s": [743.208, 727.791, 0], "to": [0, -0.309, 0], "ti": [0, 0.32, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 92, "s": [743.208, 726.847, 0], "to": [0, -0.32, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 93, "s": [743.208, 725.874, 0], "to": [0, -0.327, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 94, "s": [743.208, 724.883, 0], "to": [0, -0.332, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [743.208, 723.883, 0], "to": [0, -0.333, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 96, "s": [743.208, 722.884, 0], "to": [0, -0.331, 0], "ti": [0, 0.326, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 97, "s": [743.208, 721.897, 0], "to": [0, -0.326, 0], "ti": [0, 0.317, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 98, "s": [743.208, 720.931, 0], "to": [0, -0.317, 0], "ti": [0, 0.305, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 99, "s": [743.208, 719.996, 0], "to": [0, -0.305, 0], "ti": [0, 0.29, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 100, "s": [743.208, 719.102, 0], "to": [0, -0.29, 0], "ti": [0, 0.272, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 101, "s": [743.208, 718.256, 0], "to": [0, -0.272, 0], "ti": [0, 0.252, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 102, "s": [743.208, 717.469, 0], "to": [0, -0.252, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 103, "s": [743.208, 716.747, 0], "to": [0, -0.229, 0], "ti": [0, 0.203, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 104, "s": [743.208, 716.098, 0], "to": [0, -0.203, 0], "ti": [0, 0.176, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 105, "s": [743.208, 715.528, 0], "to": [0, -0.176, 0], "ti": [0, 0.147, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 106, "s": [743.208, 715.043, 0], "to": [0, -0.147, 0], "ti": [0, 0.116, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.193}, "t": 107, "s": [743.208, 714.648, 0], "to": [0, -0.116, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.206}, "t": 108, "s": [743.208, 714.347, 0], "to": [0, -0.084, 0], "ti": [0, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.245}, "t": 109, "s": [743.208, 714.143, 0], "to": [0, -0.052, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.601}, "o": {"x": 0.167, "y": 0.434}, "t": 110, "s": [743.208, 714.038, 0], "to": [0, -0.018, 0], "ti": [0, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.746}, "o": {"x": 0.167, "y": 0.106}, "t": 111, "s": [743.208, 714.032, 0], "to": [0, 0.015, 0], "ti": [0, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.124}, "t": 112, "s": [743.208, 714.127, 0], "to": [0, 0.048, 0], "ti": [0, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 113, "s": [743.208, 714.321, 0], "to": [0, 0.081, 0], "ti": [0, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 114, "s": [743.208, 714.612, 0], "to": [0, 0.113, 0], "ti": [0, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 115, "s": [743.208, 714.997, 0], "to": [0, 0.143, 0], "ti": [0, -0.173, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 116, "s": [743.208, 715.472, 0], "to": [0, 0.173, 0], "ti": [0, -0.2, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 117, "s": [743.208, 716.033, 0], "to": [0, 0.2, 0], "ti": [0, -0.226, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 118, "s": [743.208, 716.674, 0], "to": [0, 0.226, 0], "ti": [0, -0.249, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 119, "s": [743.208, 717.389, 0], "to": [0, 0.249, 0], "ti": [0, -0.27, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 120, "s": [743.208, 718.17, 0], "to": [0, 0.27, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 121, "s": [743.208, 719.01, 0], "to": [0, 0.288, 0], "ti": [0, -0.303, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 122, "s": [743.208, 719.899, 0], "to": [0, 0.303, 0], "ti": [0, -0.316, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 123, "s": [743.208, 720.83, 0], "to": [0, 0.316, 0], "ti": [0, -0.325, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 124, "s": [743.208, 721.794, 0], "to": [0, 0.325, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 125, "s": [743.208, 722.779, 0], "to": [0, 0.331, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 126, "s": [743.208, 723.777, 0], "to": [0, 0.333, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [743.208, 724.777, 0], "to": [0, 0.332, 0], "ti": [0, -0.328, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 128, "s": [743.208, 725.77, 0], "to": [0, 0.328, 0], "ti": [0, -0.321, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 129, "s": [743.208, 726.745, 0], "to": [0, 0.321, 0], "ti": [0, -0.31, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 130, "s": [743.208, 727.693, 0], "to": [0, 0.31, 0], "ti": [0, -0.296, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 131, "s": [743.208, 728.604, 0], "to": [0, 0.296, 0], "ti": [0, -0.279, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 132, "s": [743.208, 729.469, 0], "to": [0, 0.279, 0], "ti": [0, -0.26, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 133, "s": [743.208, 730.28, 0], "to": [0, 0.26, 0], "ti": [0, -0.238, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 134, "s": [743.208, 731.028, 0], "to": [0, 0.238, 0], "ti": [0, -0.213, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 135, "s": [743.208, 731.706, 0], "to": [0, 0.213, 0], "ti": [0, -0.187, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 136, "s": [743.208, 732.307, 0], "to": [0, 0.187, 0], "ti": [0, -0.158, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.184}, "t": 137, "s": [743.208, 732.825, 0], "to": [0, 0.158, 0], "ti": [0, -0.128, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.189}, "t": 138, "s": [743.208, 733.255, 0], "to": [0, 0.128, 0], "ti": [0, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.2}, "t": 139, "s": [743.208, 733.593, 0], "to": [0, 0.097, 0], "ti": [0, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.224}, "t": 140, "s": [743.208, 733.835, 0], "to": [0, 0.064, 0], "ti": [0, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.354}, "t": 141, "s": [743.208, 733.978, 0], "to": [0, 0.031, 0], "ti": [0, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.149}, "t": 142, "s": [743.208, 734.022, 0], "to": [0, -0.002, 0], "ti": [0, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.113}, "t": 143, "s": [743.208, 733.966, 0], "to": [0, -0.035, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.134}, "t": 144, "s": [743.208, 733.81, 0], "to": [0, -0.068, 0], "ti": [0, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 145, "s": [743.208, 733.556, 0], "to": [0, -0.101, 0], "ti": [0, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 146, "s": [743.208, 733.207, 0], "to": [0, -0.132, 0], "ti": [0, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 147, "s": [743.208, 732.765, 0], "to": [0, -0.162, 0], "ti": [0, 0.19, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 148, "s": [743.208, 732.237, 0], "to": [0, -0.19, 0], "ti": [0, 0.216, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 149, "s": [743.208, 731.626, 0], "to": [0, -0.216, 0], "ti": [0, 0.241, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 150, "s": [743.208, 730.939, 0], "to": [0, -0.241, 0], "ti": [0, 0.262, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 151, "s": [743.208, 730.182, 0], "to": [0, -0.262, 0], "ti": [0, 0.282, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 152, "s": [743.208, 729.364, 0], "to": [0, -0.282, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 153, "s": [743.208, 728.493, 0], "to": [0, -0.298, 0], "ti": [0, 0.311, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 154, "s": [743.208, 727.576, 0], "to": [0, -0.311, 0], "ti": [0, 0.322, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 155, "s": [743.208, 726.624, 0], "to": [0, -0.322, 0], "ti": [0, 0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 156, "s": [743.208, 725.646, 0], "to": [0, -0.329, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 157, "s": [743.208, 724.652, 0], "to": [0, -0.332, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [743.208, 723.652, 0], "to": [0, -0.333, 0], "ti": [0, 0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 159, "s": [743.208, 722.655, 0], "to": [0, -0.33, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 160, "s": [743.208, 721.672, 0], "to": [0, -0.324, 0], "ti": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 161, "s": [743.208, 720.712, 0], "to": [0, -0.314, 0], "ti": [0, 0.302, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 162, "s": [743.208, 719.786, 0], "to": [0, -0.302, 0], "ti": [0, 0.286, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 163, "s": [743.208, 718.902, 0], "to": [0, -0.286, 0], "ti": [0, 0.268, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 164, "s": [743.208, 718.069, 0], "to": [0, -0.268, 0], "ti": [0, 0.246, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 165, "s": [743.208, 717.296, 0], "to": [0, -0.246, 0], "ti": [0, 0.223, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 166, "s": [743.208, 716.59, 0], "to": [0, -0.223, 0], "ti": [0, 0.197, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 167, "s": [743.208, 715.959, 0], "to": [0, -0.197, 0], "ti": [0, 0.169, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.182}, "t": 168, "s": [743.208, 715.408, 0], "to": [0, -0.169, 0], "ti": [0, 0.14, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.187}, "t": 169, "s": [743.208, 714.944, 0], "to": [0, -0.14, 0], "ti": [0, 0.109, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.195}, "t": 170, "s": [743.208, 714.57, 0], "to": [0, -0.109, 0], "ti": [0, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.211}, "t": 171, "s": [743.208, 714.291, 0], "to": [0, -0.077, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.267}, "t": 172, "s": [743.208, 714.11, 0], "to": [0, -0.044, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.575}, "o": {"x": 0.167, "y": 0.322}, "t": 173, "s": [743.208, 714.027, 0], "to": [0, -0.011, 0], "ti": [0, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.763}, "o": {"x": 0.167, "y": 0.103}, "t": 174, "s": [743.208, 714.045, 0], "to": [0, 0.023, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.129}, "t": 175, "s": [743.208, 714.163, 0], "to": [0, 0.056, 0], "ti": [0, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.141}, "t": 176, "s": [743.208, 714.379, 0], "to": [0, 0.088, 0], "ti": [0, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.148}, "t": 177, "s": [743.208, 714.692, 0], "to": [0, 0.12, 0], "ti": [0, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 178, "s": [743.208, 715.099, 0], "to": [0, 0.15, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 179, "s": [743.208, 715.594, 0], "to": [0, 0.179, 0], "ti": [0, -0.206, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 180, "s": [743.208, 716.174, 0], "to": [0, 0.206, 0], "ti": [0, -0.232, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 181, "s": [743.208, 716.833, 0], "to": [0, 0.232, 0], "ti": [0, -0.254, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 182, "s": [743.208, 717.564, 0], "to": [0, 0.254, 0], "ti": [0, -0.275, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 183, "s": [743.208, 718.359, 0], "to": [0, 0.275, 0], "ti": [0, -0.292, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 184, "s": [743.208, 719.211, 0], "to": [0, 0.292, 0], "ti": [0, -0.307, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 185, "s": [743.208, 720.111, 0], "to": [0, 0.307, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 186, "s": [743.208, 721.05, 0], "to": [0, 0.318, 0], "ti": [0, -0.326, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 187, "s": [743.208, 722.02, 0], "to": [0, 0.326, 0], "ti": [0, -0.165, 0]}, {"t": 188.000007657397, "s": [743.208, 723.009, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [113.585, 113.589, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.242], [4.226, 0], [0, 4.226], [-4.242, 0]], "o": [[0, 4.226], [-4.242, 0], [0, -4.242], [4.226, 0]], "v": [[7.668, 0.007], [0, 7.66], [-7.668, 0.007], [0, -7.66]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.905999995213, 0.889999988032, 0.885999971278, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.585, 113.593], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-2.056, 5.335]], "o": [[0, 0], [0, 0], [5.174, -2.347], [0, 0]], "v": [[-3.607, -13.097], [-12.486, -0.62], [1.189, 13.097], [12.486, 1.087]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 10, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.391, 120.283], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -12.026], [12.011, 0], [0, 12.011], [-12.027, 0]], "o": [[0, 12.011], [-12.027, 0], [0, -12.026], [12.011, 0]], "v": [[21.756, 0.007], [0.007, 21.757], [-21.756, 0.007], [0.007, -21.757]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.579, 113.593], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -23.703], [23.687, 0], [0, 23.673], [-23.704, 0]], "o": [[0, 23.673], [-23.704, 0], [0, -23.703], [23.687, 0]], "v": [[42.9, 0.011], [0, 42.903], [-42.9, 0.011], [0, -42.903]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.905999995213, 0.889999988032, 0.885999971278, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.586, 113.589], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.317, 31.727], [0, 0]], "o": [[0, 0], [31.807, -1.952], [0, 0], [0, 0]], "v": [[-44.001, 19.42], [-15.619, 47.786], [44, -11.141], [7.339, -47.787]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 10, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [133.169, 129.433], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -35.234], [35.218, 0], [0, 35.225], [-35.233, 0]], "o": [[0, 35.225], [-35.233, 0], [0, -35.234], [35.218, 0]], "v": [[63.775, 0.011], [-0.001, 63.777], [-63.775, 0.011], [-0.001, -63.777]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.586, 113.589], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -47.667], [47.638, 0], [0, 47.644], [-47.652, 0]], "o": [[0, 47.644], [-47.652, 0], [0, -47.667], [47.638, 0]], "v": [[86.273, 0.004], [0.008, 86.277], [-86.273, 0.004], [0.008, -86.277]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.905999995213, 0.889999988032, 0.885999971278, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.578, 113.596], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 62.601], [0.452, 4.081], [0, 0], [0, 0], [0, 0], [-3.003, 0]], "o": [[0, -4.228], [0, 0], [0, 0], [0, 0], [2.946, 0.234], [62.592, 0]], "v": [[88.554, -27.612], [87.855, -40.061], [38.994, -85.716], [-88.554, 30.468], [-33.703, 85.336], [-24.78, 85.716]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 10, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [138.366, 141.212], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -62.609], [62.592, 0], [0, 62.601], [-62.595, 0]], "o": [[0, 62.601], [-62.595, 0], [0, -62.609], [62.592, 0]], "v": [[113.335, 0.011], [0.001, 113.339], [-113.335, 0.011], [0.001, -113.339]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.585, 113.589], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 11 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.668}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [1035.185, 408.712, 0], "to": [0, 0.008, 0], "ti": [0, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.111}, "t": 1, "s": [1035.185, 408.762, 0], "to": [0, 0.033, 0], "ti": [0, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.134}, "t": 2, "s": [1035.185, 408.912, 0], "to": [0, 0.066, 0], "ti": [0, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.143}, "t": 3, "s": [1035.185, 409.16, 0], "to": [0, 0.099, 0], "ti": [0, -0.13, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 4, "s": [1035.185, 409.503, 0], "to": [0, 0.13, 0], "ti": [0, -0.16, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 5, "s": [1035.185, 409.939, 0], "to": [0, 0.16, 0], "ti": [0, -0.188, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 6, "s": [1035.185, 410.462, 0], "to": [0, 0.188, 0], "ti": [0, -0.215, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 7, "s": [1035.185, 411.068, 0], "to": [0, 0.215, 0], "ti": [0, -0.239, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 8, "s": [1035.185, 411.751, 0], "to": [0, 0.239, 0], "ti": [0, -0.261, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 9, "s": [1035.185, 412.503, 0], "to": [0, 0.261, 0], "ti": [0, -0.28, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 10, "s": [1035.185, 413.318, 0], "to": [0, 0.28, 0], "ti": [0, -0.297, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 11, "s": [1035.185, 414.186, 0], "to": [0, 0.297, 0], "ti": [0, -0.311, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 12, "s": [1035.185, 415.1, 0], "to": [0, 0.311, 0], "ti": [0, -0.321, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 13, "s": [1035.185, 416.05, 0], "to": [0, 0.321, 0], "ti": [0, -0.328, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 14, "s": [1035.185, 417.026, 0], "to": [0, 0.328, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 15, "s": [1035.185, 418.02, 0], "to": [0, 0.332, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [1035.185, 419.02, 0], "to": [0, 0.333, 0], "ti": [0, -0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 17, "s": [1035.185, 420.018, 0], "to": [0, 0.33, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 18, "s": [1035.185, 421.002, 0], "to": [0, 0.324, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 19, "s": [1035.185, 421.963, 0], "to": [0, 0.315, 0], "ti": [0, -0.303, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 20, "s": [1035.185, 422.892, 0], "to": [0, 0.303, 0], "ti": [0, -0.287, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 21, "s": [1035.185, 423.779, 0], "to": [0, 0.287, 0], "ti": [0, -0.269, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 22, "s": [1035.185, 424.615, 0], "to": [0, 0.269, 0], "ti": [0, -0.248, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 23, "s": [1035.185, 425.392, 0], "to": [0, 0.248, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 24, "s": [1035.185, 426.102, 0], "to": [0, 0.224, 0], "ti": [0, -0.199, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 25, "s": [1035.185, 426.739, 0], "to": [0, 0.199, 0], "ti": [0, -0.171, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 26, "s": [1035.185, 427.295, 0], "to": [0, 0.171, 0], "ti": [0, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.187}, "t": 27, "s": [1035.185, 427.765, 0], "to": [0, 0.142, 0], "ti": [0, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.194}, "t": 28, "s": [1035.185, 428.144, 0], "to": [0, 0.111, 0], "ti": [0, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.21}, "t": 29, "s": [1035.185, 428.429, 0], "to": [0, 0.079, 0], "ti": [0, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.26}, "t": 30, "s": [1035.185, 428.616, 0], "to": [0, 0.046, 0], "ti": [0, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.357}, "t": 31, "s": [1035.185, 428.705, 0], "to": [0, 0.013, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.759}, "o": {"x": 0.167, "y": 0.103}, "t": 32, "s": [1035.185, 428.693, 0], "to": [0, -0.021, 0], "ti": [0, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.128}, "t": 33, "s": [1035.185, 428.582, 0], "to": [0, -0.054, 0], "ti": [0, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.14}, "t": 34, "s": [1035.185, 428.372, 0], "to": [0, -0.086, 0], "ti": [0, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.147}, "t": 35, "s": [1035.185, 428.065, 0], "to": [0, -0.118, 0], "ti": [0, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 36, "s": [1035.185, 427.664, 0], "to": [0, -0.148, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 37, "s": [1035.185, 427.174, 0], "to": [0, -0.178, 0], "ti": [0, 0.205, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 38, "s": [1035.185, 426.599, 0], "to": [0, -0.205, 0], "ti": [0, 0.23, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 39, "s": [1035.185, 425.945, 0], "to": [0, -0.23, 0], "ti": [0, 0.253, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 40, "s": [1035.185, 425.218, 0], "to": [0, -0.253, 0], "ti": [0, 0.273, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 41, "s": [1035.185, 424.427, 0], "to": [0, -0.273, 0], "ti": [0, 0.291, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 42, "s": [1035.185, 423.578, 0], "to": [0, -0.291, 0], "ti": [0, 0.306, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 43, "s": [1035.185, 422.681, 0], "to": [0, -0.306, 0], "ti": [0, 0.317, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 44, "s": [1035.185, 421.744, 0], "to": [0, -0.317, 0], "ti": [0, 0.326, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 45, "s": [1035.185, 420.776, 0], "to": [0, -0.326, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 46, "s": [1035.185, 419.788, 0], "to": [0, -0.331, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [1035.185, 418.789, 0], "to": [0, -0.333, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [1035.185, 417.789, 0], "to": [0, -0.332, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 49, "s": [1035.185, 416.799, 0], "to": [0, -0.327, 0], "ti": [0, 0.319, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 50, "s": [1035.185, 415.828, 0], "to": [0, -0.319, 0], "ti": [0, 0.308, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 51, "s": [1035.185, 414.885, 0], "to": [0, -0.308, 0], "ti": [0, 0.293, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 52, "s": [1035.185, 413.981, 0], "to": [0, -0.293, 0], "ti": [0, 0.276, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 53, "s": [1035.185, 413.124, 0], "to": [0, -0.276, 0], "ti": [0, 0.256, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 54, "s": [1035.185, 412.324, 0], "to": [0, -0.256, 0], "ti": [0, 0.234, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 55, "s": [1035.185, 411.587, 0], "to": [0, -0.234, 0], "ti": [0, 0.209, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 56, "s": [1035.185, 410.921, 0], "to": [0, -0.209, 0], "ti": [0, 0.182, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.181}, "t": 57, "s": [1035.185, 410.334, 0], "to": [0, -0.182, 0], "ti": [0, 0.153, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.185}, "t": 58, "s": [1035.185, 409.83, 0], "to": [0, -0.153, 0], "ti": [0, 0.123, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.191}, "t": 59, "s": [1035.185, 409.416, 0], "to": [0, -0.123, 0], "ti": [0, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.202}, "t": 60, "s": [1035.185, 409.094, 0], "to": [0, -0.091, 0], "ti": [0, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.231}, "t": 61, "s": [1035.185, 408.869, 0], "to": [0, -0.059, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.731}, "o": {"x": 0.167, "y": 0.406}, "t": 62, "s": [1035.185, 408.742, 0], "to": [0, -0.026, 0], "ti": [0, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.72}, "o": {"x": 0.167, "y": 0.12}, "t": 63, "s": [1035.185, 408.715, 0], "to": [0, 0.008, 0], "ti": [0, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.119}, "t": 64, "s": [1035.185, 408.788, 0], "to": [0, 0.041, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.137}, "t": 65, "s": [1035.185, 408.961, 0], "to": [0, 0.074, 0], "ti": [0, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 66, "s": [1035.185, 409.231, 0], "to": [0, 0.106, 0], "ti": [0, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.15}, "t": 67, "s": [1035.185, 409.596, 0], "to": [0, 0.137, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 68, "s": [1035.185, 410.052, 0], "to": [0, 0.167, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 69, "s": [1035.185, 410.595, 0], "to": [0, 0.195, 0], "ti": [0, -0.221, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 70, "s": [1035.185, 411.219, 0], "to": [0, 0.221, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 71, "s": [1035.185, 411.919, 0], "to": [0, 0.244, 0], "ti": [0, -0.266, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 72, "s": [1035.185, 412.686, 0], "to": [0, 0.266, 0], "ti": [0, -0.285, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 73, "s": [1035.185, 413.514, 0], "to": [0, 0.285, 0], "ti": [0, -0.3, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 74, "s": [1035.185, 414.394, 0], "to": [0, 0.3, 0], "ti": [0, -0.313, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 75, "s": [1035.185, 415.316, 0], "to": [0, 0.313, 0], "ti": [0, -0.323, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 76, "s": [1035.185, 416.273, 0], "to": [0, 0.323, 0], "ti": [0, -0.33, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 77, "s": [1035.185, 417.255, 0], "to": [0, 0.33, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 78, "s": [1035.185, 418.251, 0], "to": [0, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [1035.185, 419.251, 0], "to": [0, 0.333, 0], "ti": [0, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 80, "s": [1035.185, 420.246, 0], "to": [0, 0.329, 0], "ti": [0, -0.322, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 81, "s": [1035.185, 421.226, 0], "to": [0, 0.322, 0], "ti": [0, -0.312, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 82, "s": [1035.185, 422.181, 0], "to": [0, 0.312, 0], "ti": [0, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 83, "s": [1035.185, 423.101, 0], "to": [0, 0.299, 0], "ti": [0, -0.283, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 84, "s": [1035.185, 423.977, 0], "to": [0, 0.283, 0], "ti": [0, -0.264, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 85, "s": [1035.185, 424.8, 0], "to": [0, 0.264, 0], "ti": [0, -0.243, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 86, "s": [1035.185, 425.562, 0], "to": [0, 0.243, 0], "ti": [0, -0.219, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 87, "s": [1035.185, 426.256, 0], "to": [0, 0.219, 0], "ti": [0, -0.192, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 88, "s": [1035.185, 426.874, 0], "to": [0, 0.192, 0], "ti": [0, -0.164, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.183}, "t": 89, "s": [1035.185, 427.411, 0], "to": [0, 0.164, 0], "ti": [0, -0.135, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 90, "s": [1035.185, 427.86, 0], "to": [0, 0.135, 0], "ti": [0, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.197}, "t": 91, "s": [1035.185, 428.218, 0], "to": [0, 0.103, 0], "ti": [0, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.216}, "t": 92, "s": [1035.185, 428.481, 0], "to": [0, 0.071, 0], "ti": [0, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.293}, "t": 93, "s": [1035.185, 428.646, 0], "to": [0, 0.038, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.613}, "o": {"x": 0.167, "y": 0.231}, "t": 94, "s": [1035.185, 428.711, 0], "to": [0, 0.005, 0], "ti": [0, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.106}, "t": 95, "s": [1035.185, 428.676, 0], "to": [0, -0.028, 0], "ti": [0, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.131}, "t": 96, "s": [1035.185, 428.542, 0], "to": [0, -0.061, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 97, "s": [1035.185, 428.309, 0], "to": [0, -0.094, 0], "ti": [0, 0.125, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 98, "s": [1035.185, 427.98, 0], "to": [0, -0.125, 0], "ti": [0, 0.155, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 99, "s": [1035.185, 427.558, 0], "to": [0, -0.155, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 100, "s": [1035.185, 427.048, 0], "to": [0, -0.184, 0], "ti": [0, 0.211, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 101, "s": [1035.185, 426.454, 0], "to": [0, -0.211, 0], "ti": [0, 0.236, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 102, "s": [1035.185, 425.783, 0], "to": [0, -0.236, 0], "ti": [0, 0.258, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 103, "s": [1035.185, 425.041, 0], "to": [0, -0.258, 0], "ti": [0, 0.278, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 104, "s": [1035.185, 424.236, 0], "to": [0, -0.278, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 105, "s": [1035.185, 423.375, 0], "to": [0, -0.295, 0], "ti": [0, 0.309, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 106, "s": [1035.185, 422.468, 0], "to": [0, -0.309, 0], "ti": [0, 0.32, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 107, "s": [1035.185, 421.523, 0], "to": [0, -0.32, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 108, "s": [1035.185, 420.549, 0], "to": [0, -0.327, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 109, "s": [1035.185, 419.558, 0], "to": [0, -0.332, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [1035.185, 418.558, 0], "to": [0, -0.333, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 111, "s": [1035.185, 417.559, 0], "to": [0, -0.331, 0], "ti": [0, 0.325, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 112, "s": [1035.185, 416.573, 0], "to": [0, -0.325, 0], "ti": [0, 0.317, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 113, "s": [1035.185, 415.607, 0], "to": [0, -0.317, 0], "ti": [0, 0.305, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 114, "s": [1035.185, 414.673, 0], "to": [0, -0.305, 0], "ti": [0, 0.29, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 115, "s": [1035.185, 413.779, 0], "to": [0, -0.29, 0], "ti": [0, 0.272, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 116, "s": [1035.185, 412.934, 0], "to": [0, -0.272, 0], "ti": [0, 0.251, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 117, "s": [1035.185, 412.148, 0], "to": [0, -0.251, 0], "ti": [0, 0.228, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 118, "s": [1035.185, 411.427, 0], "to": [0, -0.228, 0], "ti": [0, 0.203, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 119, "s": [1035.185, 410.779, 0], "to": [0, -0.203, 0], "ti": [0, 0.175, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 120, "s": [1035.185, 410.21, 0], "to": [0, -0.175, 0], "ti": [0, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 121, "s": [1035.185, 409.726, 0], "to": [0, -0.146, 0], "ti": [0, 0.116, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.193}, "t": 122, "s": [1035.185, 409.333, 0], "to": [0, -0.116, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.207}, "t": 123, "s": [1035.185, 409.033, 0], "to": [0, -0.084, 0], "ti": [0, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.246}, "t": 124, "s": [1035.185, 408.831, 0], "to": [0, -0.051, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.593}, "o": {"x": 0.167, "y": 0.432}, "t": 125, "s": [1035.185, 408.727, 0], "to": [0, -0.018, 0], "ti": [0, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.748}, "o": {"x": 0.167, "y": 0.105}, "t": 126, "s": [1035.185, 408.723, 0], "to": [0, 0.015, 0], "ti": [0, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.124}, "t": 127, "s": [1035.185, 408.819, 0], "to": [0, 0.049, 0], "ti": [0, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 128, "s": [1035.185, 409.014, 0], "to": [0, 0.081, 0], "ti": [0, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 129, "s": [1035.185, 409.307, 0], "to": [0, 0.113, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 130, "s": [1035.185, 409.693, 0], "to": [0, 0.144, 0], "ti": [0, -0.173, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 131, "s": [1035.185, 410.17, 0], "to": [0, 0.173, 0], "ti": [0, -0.201, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 132, "s": [1035.185, 410.732, 0], "to": [0, 0.201, 0], "ti": [0, -0.226, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 133, "s": [1035.185, 411.375, 0], "to": [0, 0.226, 0], "ti": [0, -0.25, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 134, "s": [1035.185, 412.09, 0], "to": [0, 0.25, 0], "ti": [0, -0.27, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 135, "s": [1035.185, 412.872, 0], "to": [0, 0.27, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 136, "s": [1035.185, 413.713, 0], "to": [0, 0.288, 0], "ti": [0, -0.304, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 137, "s": [1035.185, 414.603, 0], "to": [0, 0.304, 0], "ti": [0, -0.316, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 138, "s": [1035.185, 415.535, 0], "to": [0, 0.316, 0], "ti": [0, -0.325, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 139, "s": [1035.185, 416.498, 0], "to": [0, 0.325, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 140, "s": [1035.185, 417.484, 0], "to": [0, 0.331, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 141, "s": [1035.185, 418.482, 0], "to": [0, 0.333, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [1035.185, 419.482, 0], "to": [0, 0.332, 0], "ti": [0, -0.328, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 143, "s": [1035.185, 420.475, 0], "to": [0, 0.328, 0], "ti": [0, -0.32, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 144, "s": [1035.185, 421.449, 0], "to": [0, 0.32, 0], "ti": [0, -0.31, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 145, "s": [1035.185, 422.397, 0], "to": [0, 0.31, 0], "ti": [0, -0.296, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 146, "s": [1035.185, 423.307, 0], "to": [0, 0.296, 0], "ti": [0, -0.279, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 147, "s": [1035.185, 424.172, 0], "to": [0, 0.279, 0], "ti": [0, -0.26, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 148, "s": [1035.185, 424.982, 0], "to": [0, 0.26, 0], "ti": [0, -0.237, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 149, "s": [1035.185, 425.729, 0], "to": [0, 0.237, 0], "ti": [0, -0.213, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 150, "s": [1035.185, 426.406, 0], "to": [0, 0.213, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 151, "s": [1035.185, 427.006, 0], "to": [0, 0.186, 0], "ti": [0, -0.158, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.184}, "t": 152, "s": [1035.185, 427.523, 0], "to": [0, 0.158, 0], "ti": [0, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.19}, "t": 153, "s": [1035.185, 427.951, 0], "to": [0, 0.127, 0], "ti": [0, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.2}, "t": 154, "s": [1035.185, 428.287, 0], "to": [0, 0.096, 0], "ti": [0, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.224}, "t": 155, "s": [1035.185, 428.528, 0], "to": [0, 0.064, 0], "ti": [0, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.358}, "t": 156, "s": [1035.185, 428.67, 0], "to": [0, 0.031, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.69}, "o": {"x": 0.167, "y": 0.145}, "t": 157, "s": [1035.185, 428.712, 0], "to": [0, -0.003, 0], "ti": [0, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.114}, "t": 158, "s": [1035.185, 428.654, 0], "to": [0, -0.036, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.135}, "t": 159, "s": [1035.185, 428.497, 0], "to": [0, -0.069, 0], "ti": [0, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 160, "s": [1035.185, 428.242, 0], "to": [0, -0.101, 0], "ti": [0, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 161, "s": [1035.185, 427.891, 0], "to": [0, -0.132, 0], "ti": [0, 0.162, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 162, "s": [1035.185, 427.448, 0], "to": [0, -0.162, 0], "ti": [0, 0.19, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 163, "s": [1035.185, 426.918, 0], "to": [0, -0.19, 0], "ti": [0, 0.217, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 164, "s": [1035.185, 426.306, 0], "to": [0, -0.217, 0], "ti": [0, 0.241, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 165, "s": [1035.185, 425.618, 0], "to": [0, -0.241, 0], "ti": [0, 0.263, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 166, "s": [1035.185, 424.86, 0], "to": [0, -0.263, 0], "ti": [0, 0.282, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 167, "s": [1035.185, 424.041, 0], "to": [0, -0.282, 0], "ti": [0, 0.298, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 168, "s": [1035.185, 423.169, 0], "to": [0, -0.298, 0], "ti": [0, 0.312, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 169, "s": [1035.185, 422.252, 0], "to": [0, -0.312, 0], "ti": [0, 0.322, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 170, "s": [1035.185, 421.3, 0], "to": [0, -0.322, 0], "ti": [0, 0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 171, "s": [1035.185, 420.322, 0], "to": [0, -0.329, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 172, "s": [1035.185, 419.327, 0], "to": [0, -0.332, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [1035.185, 418.327, 0], "to": [0, -0.333, 0], "ti": [0, 0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 174, "s": [1035.185, 417.33, 0], "to": [0, -0.33, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 175, "s": [1035.185, 416.347, 0], "to": [0, -0.324, 0], "ti": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 176, "s": [1035.185, 415.388, 0], "to": [0, -0.314, 0], "ti": [0, 0.302, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 177, "s": [1035.185, 414.462, 0], "to": [0, -0.302, 0], "ti": [0, 0.286, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 178, "s": [1035.185, 413.579, 0], "to": [0, -0.286, 0], "ti": [0, 0.267, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 179, "s": [1035.185, 412.747, 0], "to": [0, -0.267, 0], "ti": [0, 0.246, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 180, "s": [1035.185, 411.975, 0], "to": [0, -0.246, 0], "ti": [0, 0.222, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 181, "s": [1035.185, 411.27, 0], "to": [0, -0.222, 0], "ti": [0, 0.197, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 182, "s": [1035.185, 410.64, 0], "to": [0, -0.197, 0], "ti": [0, 0.169, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.182}, "t": 183, "s": [1035.185, 410.09, 0], "to": [0, -0.169, 0], "ti": [0, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.187}, "t": 184, "s": [1035.185, 409.627, 0], "to": [0, -0.139, 0], "ti": [0, 0.108, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.195}, "t": 185, "s": [1035.185, 409.255, 0], "to": [0, -0.108, 0], "ti": [0, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.212}, "t": 186, "s": [1035.185, 408.978, 0], "to": [0, -0.076, 0], "ti": [0, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.269}, "t": 187, "s": [1035.185, 408.798, 0], "to": [0, -0.043, 0], "ti": [0, 0.013, 0]}, {"t": 188.000007657397, "s": [1035.185, 408.717, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [83.779, 196.323, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.589], [2.59, 0], [0, 0], [0, 2.591], [-2.589, 0], [0, 0]], "o": [[0, 2.591], [0, 0], [-2.589, 0], [0, -2.589], [0, 0], [2.59, 0]], "v": [[42.815, -0.001], [38.128, 4.691], [-38.125, 4.691], [-42.815, -0.001], [-38.125, -4.691], [38.128, -4.691]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.917999985639, 0.583999992819, 0.243000000598, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.373, 4.941], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-20.553, 0], [0, 29.205], [0, 0]], "o": [[0, 0], [0, 29.205], [20.556, 0], [0, 0], [0, 0]], "v": [[-37.218, -62.558], [-37.218, 9.675], [-0.001, 62.558], [37.218, 9.675], [37.218, -62.558]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.374, 67.349], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.413, 0.578], [-0.623, 0], [0, 0], [-0.516, 0], [0, -4.094], [3.275, 0], [0, 4.092], [-0.108, 0.555], [0, 0], [-5.536, -12.371], [-18.978, 0], [-0.14, 0.004], [0, 0], [-0.01, -1.499], [1.205, -0.008], [0.148, 0], [10.363, 23.263], [0.067, 0.727]], "o": [[0.413, -0.582], [0, 0], [0.476, -0.155], [3.275, 0], [0, 4.092], [-3.275, 0], [0, -0.593], [0, 0], [0.826, 5.62], [9.685, 21.64], [0.14, 0], [0, 0], [1.197, 0], [0.006, 1.501], [-0.149, 0.003], [-20.439, 0], [-7.787, -17.476], [-0.073, -0.775]], "v": [[-29.039, -35.991], [-27.412, -36.904], [14.749, -36.904], [16.238, -37.152], [22.168, -29.737], [16.238, -22.324], [10.309, -29.737], [10.478, -31.46], [-24.858, -31.46], [-16.17, -0.902], [27.022, 31.71], [27.446, 31.706], [27.462, 31.706], [29.642, 34.41], [27.477, 37.15], [27.036, 37.152], [-20.038, 1.605], [-29.575, -33.864]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.898, 61.665], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.413, 0.578], [0.619, 0], [0, 0], [0.516, 0], [0, -4.094], [-3.275, 0], [0, 4.092], [0.106, 0.555], [0, 0], [5.535, -12.371], [18.977, 0], [0.144, 0.004], [0, 0], [0.009, -1.499], [-1.203, -0.008], [-0.147, 0], [-10.364, 23.263], [-0.066, 0.727]], "o": [[-0.412, -0.582], [0, 0], [-0.478, -0.155], [-3.275, 0], [0, 4.092], [3.274, 0], [0, -0.593], [0, 0], [-0.826, 5.62], [-9.684, 21.64], [-0.144, 0], [0, 0], [-1.194, 0], [-0.01, 1.501], [0.146, 0.003], [20.436, 0], [7.784, -17.476], [0.075, -0.775]], "v": [[29.04, -35.991], [27.413, -36.904], [-14.749, -36.904], [-16.24, -37.152], [-22.17, -29.737], [-16.24, -22.324], [-10.311, -29.737], [-10.477, -31.46], [24.858, -31.46], [16.171, -0.902], [-27.021, 31.71], [-27.447, 31.706], [-27.465, 31.706], [-29.639, 34.41], [-27.477, 37.15], [-27.035, 37.152], [20.04, 1.605], [29.574, -33.864]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [137.659, 61.665], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.447], [4.325, 0], [0, 3.446], [-4.324, 0]], "o": [[0, 3.446], [-4.324, 0], [0, -3.447], [4.325, 0]], "v": [[7.829, 0], [-0.002, 6.242], [-7.829, 0], [-0.002, -6.242]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.917999985639, 0.583999992819, 0.243000000598, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.375, 128.205], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.796], [1.797, 0], [0, 0], [0, 1.793], [-1.798, 0], [0, 0]], "o": [[0, 1.793], [0, 0], [-1.798, 0], [0, -1.796], [0, 0], [1.797, 0]], "v": [[34.605, 0.001], [31.354, 3.252], [-31.355, 3.252], [-34.605, 0.001], [-31.355, -3.252], [31.354, -3.252]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.917999985639, 0.583999992819, 0.243000000598, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.374, 193.071], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [26.93, -0.3], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-26.933, -0.3]], "v": [[3.805, -6.733], [3.805, -32.302], [-3.799, -32.302], [-3.799, -6.733], [-3.805, -6.733], [-31.846, 21.107], [-31.846, 32.302], [31.846, 32.302], [31.846, 21.107]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.375, 164.022], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Sand Timer 6", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [85.395, 163.439, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.447, -10.365, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.307, 3.473], [0, 0], [-1.707, 2.988], [0, 0], [0, 0]], "o": [[2.302, -5.384], [0, 0], [2.018, -2.118], [0, 0], [0, 0], [0, 0]], "v": [[-10.286, -21.343], [-1.658, -35.192], [4.656, -41.797], [10.286, -49.592], [10.286, 49.591], [-10.286, 49.591]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.894000004787, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 60, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.287, 76.462], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Sand Timer 5", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85.078, 233.555, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.11, 63.183, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.307, 3.473], [0, 0], [-1.707, 2.988], [0, 0], [0, 0]], "o": [[2.302, -5.384], [0, 0], [2.018, -2.118], [0, 0], [0, 0], [0, 0]], "v": [[-10.286, -21.343], [-1.658, -35.192], [4.656, -41.797], [10.286, -49.592], [10.286, 49.591], [-10.286, 49.591]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.894000004787, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 60, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.287, 76.462], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Sand Timer Mask 3", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -64.128, "ix": 10}, "p": {"a": 0, "k": [-108.911, -136.428, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.727, 98.826, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.671, 0.671, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 0, "s": [356.25, 356.25, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.112, 0.112, 0]}, "t": 1, "s": [356.863, 356.863, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 2, "s": [358.673, 358.673, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 3, "s": [361.636, 361.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 4, "s": [365.709, 365.709, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 5, "s": [370.848, 370.848, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 6, "s": [377.01, 377.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 7, "s": [384.151, 384.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 8, "s": [392.227, 392.227, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 9, "s": [401.196, 401.196, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 10, "s": [411.014, 411.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 11, "s": [421.637, 421.637, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 12, "s": [433.021, 433.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 13, "s": [445.124, 445.124, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 14, "s": [457.901, 457.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 15, "s": [471.309, 471.309, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 16, "s": [485.305, 485.305, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 17, "s": [499.844, 499.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 18, "s": [514.884, 514.884, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 19, "s": [530.381, 530.381, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 20, "s": [546.292, 546.292, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 21, "s": [562.573, 562.573, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 22, "s": [579.179, 579.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 23, "s": [596.069, 596.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 24, "s": [613.198, 613.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 25, "s": [630.523, 630.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 26, "s": [648.001, 648.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 27, "s": [665.587, 665.587, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 28, "s": [683.238, 683.238, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 29, "s": [700.912, 700.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 30, "s": [718.563, 718.563, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 31, "s": [736.149, 736.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 32, "s": [753.627, 753.627, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 33, "s": [770.952, 770.952, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 34, "s": [788.081, 788.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 35, "s": [804.971, 804.971, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 36, "s": [821.577, 821.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 37, "s": [837.858, 837.858, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 38, "s": [853.769, 853.769, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 39, "s": [869.266, 869.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 40, "s": [884.306, 884.306, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 41, "s": [898.845, 898.845, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 42, "s": [912.841, 912.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 43, "s": [926.249, 926.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 44, "s": [939.026, 939.026, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 45, "s": [951.129, 951.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 46, "s": [962.513, 962.513, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 47, "s": [973.136, 973.136, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 48, "s": [982.954, 982.954, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 49, "s": [991.923, 991.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 50, "s": [999.999, 999.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 51, "s": [1007.14, 1007.14, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 52, "s": [1013.302, 1013.302, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 53, "s": [1018.441, 1018.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 54, "s": [1022.514, 1022.514, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 55, "s": [1025.477, 1025.477, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 56, "s": [1027.287, 1027.287, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 57, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 58, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 59, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 60, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 90, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 91, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 92, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [56.92, 56.92, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 93, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 94, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-30.816, -30.816, 0]}, "t": 95, "s": [356.863, 356.863, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 96, "s": [358.673, 358.673, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 97, "s": [361.636, 361.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 98, "s": [365.709, 365.709, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 99, "s": [370.848, 370.848, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 100, "s": [377.01, 377.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 101, "s": [384.151, 384.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 102, "s": [392.227, 392.227, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 103, "s": [401.196, 401.196, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 104, "s": [411.014, 411.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 105, "s": [421.637, 421.637, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 106, "s": [433.021, 433.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 107, "s": [445.124, 445.124, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 108, "s": [457.901, 457.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 109, "s": [471.309, 471.309, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 110, "s": [485.305, 485.305, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 111, "s": [499.844, 499.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 112, "s": [514.884, 514.884, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 113, "s": [530.381, 530.381, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 114, "s": [546.292, 546.292, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 115, "s": [562.573, 562.573, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 116, "s": [579.179, 579.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 117, "s": [596.069, 596.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 118, "s": [613.198, 613.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 119, "s": [630.523, 630.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 120, "s": [648.001, 648.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 121, "s": [665.587, 665.587, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 122, "s": [683.238, 683.238, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 123, "s": [700.912, 700.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 124, "s": [718.563, 718.563, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 125, "s": [736.149, 736.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 126, "s": [753.627, 753.627, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 127, "s": [770.952, 770.952, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 128, "s": [788.081, 788.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 129, "s": [804.971, 804.971, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 130, "s": [821.577, 821.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 131, "s": [837.858, 837.858, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 132, "s": [853.769, 853.769, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 133, "s": [869.266, 869.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 134, "s": [884.306, 884.306, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 135, "s": [898.845, 898.845, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 136, "s": [912.841, 912.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 137, "s": [926.249, 926.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 138, "s": [939.026, 939.026, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 139, "s": [951.129, 951.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 140, "s": [962.513, 962.513, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 141, "s": [973.136, 973.136, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 142, "s": [982.954, 982.954, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 143, "s": [991.923, 991.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 144, "s": [999.999, 999.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 145, "s": [1007.14, 1007.14, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 146, "s": [1013.302, 1013.302, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 147, "s": [1018.441, 1018.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 148, "s": [1022.514, 1022.514, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 149, "s": [1025.477, 1025.477, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 150, "s": [1027.287, 1027.287, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 151, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 152, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 153, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 154, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 155, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 156, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 157, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 158, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 159, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 160, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 161, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 162, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 163, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 164, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 165, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 166, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 167, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 168, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 169, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 170, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 171, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 172, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 173, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 174, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 176, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 178, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 179, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 180, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 181, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 182, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 184, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 185, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [56.971, 56.971, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 186, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 187, "s": [1027.9, 1027.9, 100]}, {"t": 188.000007657397, "s": [356.25, 356.25, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.881, 0]], "o": [[0, 0], [0, 0], [11.88, 0]], "v": [[23.008, 13.61], [-23.008, 13.61], [0, -13.61]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.727, 112.437], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Sand Timer 4", "parent": 16, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -32.128, "ix": 10}, "p": {"a": 0, "k": [44.55, 41.061, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [4.069, 0.25, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [95, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.616, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 1, "s": [95, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.697, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 2, "s": [95, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.799, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.115, 0]}, "t": 3, "s": [95, 4.613, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.142, 0]}, "t": 4, "s": [95, 16.773, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.156, 0]}, "t": 5, "s": [95, 33.966, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 6, "s": [95, 53.675, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 7, "s": [95, 73.384, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.885, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.201, 0]}, "t": 8, "s": [95, 90.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.303, 0]}, "t": 9, "s": [95, 102.737, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.384, 0]}, "t": 10, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 14, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 17, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 22, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 28, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 30, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 32, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 33, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 34, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 35, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 36, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 37, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 38, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 39, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 41, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 42, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 43, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 44, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 45, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 46, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 47, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 48, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 49, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 50, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 52, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 53, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 54, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 55, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 56, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 57, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 58, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 59, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 60, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 90, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 91, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 92, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 9.946, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 93, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 94, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.616, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -8.946, 0]}, "t": 95, "s": [95, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.697, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 96, "s": [95, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.799, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.115, 0]}, "t": 97, "s": [95, 4.613, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.821, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.142, 0]}, "t": 98, "s": [95, 16.773, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.156, 0]}, "t": 99, "s": [95, 33.966, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 100, "s": [95, 53.675, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.179, 0]}, "t": 101, "s": [95, 73.384, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.885, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.201, 0]}, "t": 102, "s": [95, 90.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.303, 0]}, "t": 103, "s": [95, 102.737, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.384, 0]}, "t": 104, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 105, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 106, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 107, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 108, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 109, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 110, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 111, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 112, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 113, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 114, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 115, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 116, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 117, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 118, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 119, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 120, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 121, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 122, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 123, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 124, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 125, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 126, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 127, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 128, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 129, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 130, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 131, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 132, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 133, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 134, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 135, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 136, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 137, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 138, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 139, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 140, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 141, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 142, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 143, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 144, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 145, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 146, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 147, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 148, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 149, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 150, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 151, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 152, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 153, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 154, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 155, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 156, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 157, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 158, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 159, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 160, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 161, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 162, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 163, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 164, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 165, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 166, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 167, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 168, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 169, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 170, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 171, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 172, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 173, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 174, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 176, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 178, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 179, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 180, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 181, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 182, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 184, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 185, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 9.946, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 186, "s": [95, 107.35, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.083, 0]}, "t": 187, "s": [95, 107.35, 100]}, {"t": 188.000007657397, "s": [95, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.457, 0.676], [0, 0], [0, 0], [0, 0], [0, -0.816]], "o": [[0, 0], [0, 0], [0, -0.816], [0, 0], [0, 0], [0, 0], [-0.457, 0.676], [0, 0]], "v": [[1.82, 64.684], [-1.82, 64.684], [-1.82, -60.477], [-2.521, -62.764], [-3.82, -64.684], [3.82, -64.684], [2.52, -62.764], [1.82, -60.477]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.069, 64.934], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Sand Timer Mask 2", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -64.128, "ix": 10}, "p": {"a": 0, "k": [-108.911, -136.428, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.727, 98.826, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.671, 0.671, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 0, "s": [356.25, 356.25, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.112, 0.112, 0]}, "t": 1, "s": [356.863, 356.863, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 2, "s": [358.673, 358.673, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 3, "s": [361.636, 361.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 4, "s": [365.709, 365.709, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 5, "s": [370.848, 370.848, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 6, "s": [377.01, 377.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 7, "s": [384.151, 384.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 8, "s": [392.227, 392.227, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 9, "s": [401.196, 401.196, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 10, "s": [411.014, 411.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 11, "s": [421.637, 421.637, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 12, "s": [433.021, 433.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 13, "s": [445.124, 445.124, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 14, "s": [457.901, 457.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 15, "s": [471.309, 471.309, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 16, "s": [485.305, 485.305, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 17, "s": [499.844, 499.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 18, "s": [514.884, 514.884, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 19, "s": [530.381, 530.381, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 20, "s": [546.292, 546.292, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 21, "s": [562.573, 562.573, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 22, "s": [579.179, 579.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 23, "s": [596.069, 596.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 24, "s": [613.198, 613.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 25, "s": [630.523, 630.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 26, "s": [648.001, 648.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 27, "s": [665.587, 665.587, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 28, "s": [683.238, 683.238, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 29, "s": [700.912, 700.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 30, "s": [718.563, 718.563, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 31, "s": [736.149, 736.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 32, "s": [753.627, 753.627, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 33, "s": [770.952, 770.952, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 34, "s": [788.081, 788.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 35, "s": [804.971, 804.971, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 36, "s": [821.577, 821.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 37, "s": [837.858, 837.858, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 38, "s": [853.769, 853.769, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 39, "s": [869.266, 869.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 40, "s": [884.306, 884.306, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 41, "s": [898.845, 898.845, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 42, "s": [912.841, 912.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 43, "s": [926.249, 926.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 44, "s": [939.026, 939.026, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 45, "s": [951.129, 951.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 46, "s": [962.513, 962.513, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 47, "s": [973.136, 973.136, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 48, "s": [982.954, 982.954, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 49, "s": [991.923, 991.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 50, "s": [999.999, 999.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 51, "s": [1007.14, 1007.14, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 52, "s": [1013.302, 1013.302, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 53, "s": [1018.441, 1018.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 54, "s": [1022.514, 1022.514, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 55, "s": [1025.477, 1025.477, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 56, "s": [1027.287, 1027.287, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 57, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 58, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 59, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 60, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 90, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 91, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 92, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [56.92, 56.92, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 93, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 94, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-30.816, -30.816, 0]}, "t": 95, "s": [356.863, 356.863, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 96, "s": [358.673, 358.673, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 97, "s": [361.636, 361.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 98, "s": [365.709, 365.709, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 99, "s": [370.848, 370.848, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.822, 0.822, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 100, "s": [377.01, 377.01, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 101, "s": [384.151, 384.151, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.825, 0.825, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 102, "s": [392.227, 392.227, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.159, 0.159, 0]}, "t": 103, "s": [401.196, 401.196, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 104, "s": [411.014, 411.014, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 105, "s": [421.637, 421.637, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 106, "s": [433.021, 433.021, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 107, "s": [445.124, 445.124, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 108, "s": [457.901, 457.901, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 109, "s": [471.309, 471.309, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 110, "s": [485.305, 485.305, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 111, "s": [499.844, 499.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 112, "s": [514.884, 514.884, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 113, "s": [530.381, 530.381, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 114, "s": [546.292, 546.292, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 115, "s": [562.573, 562.573, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 116, "s": [579.179, 579.179, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 117, "s": [596.069, 596.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 118, "s": [613.198, 613.198, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 119, "s": [630.523, 630.523, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 120, "s": [648.001, 648.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 121, "s": [665.587, 665.587, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 122, "s": [683.238, 683.238, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 123, "s": [700.912, 700.912, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 124, "s": [718.563, 718.563, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 125, "s": [736.149, 736.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 126, "s": [753.627, 753.627, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 127, "s": [770.952, 770.952, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 128, "s": [788.081, 788.081, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 129, "s": [804.971, 804.971, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 130, "s": [821.577, 821.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 131, "s": [837.858, 837.858, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 132, "s": [853.769, 853.769, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 133, "s": [869.266, 869.266, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 134, "s": [884.306, 884.306, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 135, "s": [898.845, 898.845, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 136, "s": [912.841, 912.841, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 137, "s": [926.249, 926.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 138, "s": [939.026, 939.026, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 139, "s": [951.129, 951.129, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 140, "s": [962.513, 962.513, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 141, "s": [973.136, 973.136, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 142, "s": [982.954, 982.954, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 143, "s": [991.923, 991.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.178, 0.178, 0]}, "t": 144, "s": [999.999, 999.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 145, "s": [1007.14, 1007.14, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 146, "s": [1013.302, 1013.302, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 147, "s": [1018.441, 1018.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 148, "s": [1022.514, 1022.514, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 149, "s": [1025.477, 1025.477, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 150, "s": [1027.287, 1027.287, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 151, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 152, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 153, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 154, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 155, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 156, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 157, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 158, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 159, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 160, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 161, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 162, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 163, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 164, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 165, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 166, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 167, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 168, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 169, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 170, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 171, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 172, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 173, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 174, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 176, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 178, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 179, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 180, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 181, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 182, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 184, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 185, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [56.971, 56.971, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 186, "s": [1027.9, 1027.9, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 187, "s": [1027.9, 1027.9, 100]}, {"t": 188.000007657397, "s": [356.25, 356.25, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.881, 0]], "o": [[0, 0], [0, 0], [11.88, 0]], "v": [[23.008, 13.61], [-23.008, 13.61], [0, -13.61]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.727, 112.437], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Sand Timer 3", "parent": 16, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -32.128, "ix": 10}, "p": {"a": 0, "k": [13.419, -7.41, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.11, 63.183, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.983, -8.582], [-2.632, 0], [0, 0], [-0.597, 2.558], [-5.235, 5.457], [0, 0], [0.002, 10.156], [0, 0], [0, 0], [0, 0], [-7.025, -7.377]], "o": [[5.221, 5.478], [0.592, 2.565], [0, 0], [2.626, 0], [2.004, -8.581], [0, 0], [7.044, -7.347], [0, 0], [0, 0], [0, 0], [-0.002, 10.147], [0, 0]], "v": [[-20.142, 35.156], [-8.437, 58.523], [-2.976, 62.932], [2.748, 62.932], [8.205, 58.536], [19.984, 35.21], [26.19, 28.757], [39.044, -3.085], [39.101, -62.809], [-39.007, -62.932], [-39.099, -3.197], [-26.332, 28.679]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.868, 63.183], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Sand Timer Mask 1", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 114.872, "ix": 10}, "p": {"a": 0, "k": [172.023, 156.017, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.727, 98.826, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.949, 0.949, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [55.1, 55.1, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.671, 0.671, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 1, "s": [55.1, 55.1, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.112, 0.112, 0]}, "t": 2, "s": [55.707, 55.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 3, "s": [57.498, 57.498, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 4, "s": [60.428, 60.428, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 5, "s": [64.45, 64.45, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 6, "s": [69.52, 69.52, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.823, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 7, "s": [75.592, 75.592, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 8, "s": [82.62, 82.62, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 9, "s": [90.559, 90.559, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 10, "s": [99.363, 99.363, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 11, "s": [108.987, 108.987, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 12, "s": [119.385, 119.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 13, "s": [130.511, 130.511, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 14, "s": [142.321, 142.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 15, "s": [154.768, 154.768, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 16, "s": [167.807, 167.807, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 17, "s": [181.392, 181.392, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 18, "s": [195.479, 195.479, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 19, "s": [210.02, 210.02, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 20, "s": [224.972, 224.972, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 21, "s": [240.288, 240.288, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 22, "s": [255.923, 255.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 23, "s": [271.831, 271.831, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 24, "s": [287.966, 287.966, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 25, "s": [304.284, 304.284, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 26, "s": [320.738, 320.738, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 27, "s": [337.284, 337.284, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 28, "s": [353.875, 353.875, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 29, "s": [370.466, 370.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 30, "s": [387.012, 387.012, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 31, "s": [403.466, 403.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 32, "s": [419.784, 419.784, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 33, "s": [435.919, 435.919, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 34, "s": [451.827, 451.827, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 35, "s": [467.462, 467.462, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 36, "s": [482.778, 482.778, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 37, "s": [497.73, 497.73, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 38, "s": [512.271, 512.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 39, "s": [526.358, 526.358, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 40, "s": [539.943, 539.943, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 41, "s": [552.982, 552.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 42, "s": [565.429, 565.429, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 43, "s": [577.239, 577.239, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 44, "s": [588.365, 588.365, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 45, "s": [598.763, 598.763, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 46, "s": [608.387, 608.387, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 47, "s": [617.191, 617.191, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 48, "s": [625.13, 625.13, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 49, "s": [632.158, 632.158, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 50, "s": [638.23, 638.23, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 51, "s": [643.3, 643.3, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 52, "s": [647.322, 647.322, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 53, "s": [650.252, 650.252, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 54, "s": [652.043, 652.043, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 55, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 56, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 57, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 58, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 59, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 60, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 61, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 62, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 64, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 65, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 66, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 67, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 70, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 71, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 74, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 75, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 76, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 78, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 79, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 80, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 81, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 82, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 83, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 84, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 85, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 86, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 87, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 88, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 89, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 90, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 91, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 92, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [50.796, 50.796, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 93, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 94, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.671, 0.671, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-81.929, -81.929, 0]}, "t": 95, "s": [55.1, 55.1, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.78, 0.78, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.112, 0.112, 0]}, "t": 96, "s": [55.707, 55.707, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.134, 0.134, 0]}, "t": 97, "s": [57.498, 57.498, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.812, 0.812, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 98, "s": [60.428, 60.428, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.149, 0.149, 0]}, "t": 99, "s": [64.45, 64.45, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.82, 0.82, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 100, "s": [69.52, 69.52, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.823, 0.823, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.155, 0.155, 0]}, "t": 101, "s": [75.592, 75.592, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.157, 0.157, 0]}, "t": 102, "s": [82.62, 82.62, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 103, "s": [90.559, 90.559, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 104, "s": [99.363, 99.363, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.827, 0.827, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 105, "s": [108.987, 108.987, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.828, 0.828, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.161, 0.161, 0]}, "t": 106, "s": [119.385, 119.385, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 107, "s": [130.511, 130.511, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.162, 0.162, 0]}, "t": 108, "s": [142.321, 142.321, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 109, "s": [154.768, 154.768, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 110, "s": [167.807, 167.807, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 111, "s": [181.392, 181.392, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 112, "s": [195.479, 195.479, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.831, 0.831, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.164, 0.164, 0]}, "t": 113, "s": [210.02, 210.02, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 114, "s": [224.972, 224.972, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 115, "s": [240.288, 240.288, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 116, "s": [255.923, 255.923, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.832, 0.832, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.165, 0.165, 0]}, "t": 117, "s": [271.831, 271.831, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 118, "s": [287.966, 287.966, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 119, "s": [304.284, 304.284, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 120, "s": [320.738, 320.738, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 121, "s": [337.284, 337.284, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 122, "s": [353.875, 353.875, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 123, "s": [370.466, 370.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 124, "s": [387.012, 387.012, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.834, 0.834, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 125, "s": [403.466, 403.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 126, "s": [419.784, 419.784, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 127, "s": [435.919, 435.919, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 128, "s": [451.827, 451.827, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.168, 0.168, 0]}, "t": 129, "s": [467.462, 467.462, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 130, "s": [482.778, 482.778, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 131, "s": [497.73, 497.73, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 132, "s": [512.271, 512.271, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 133, "s": [526.358, 526.358, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.837, 0.837, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.17, 0.17, 0]}, "t": 134, "s": [539.943, 539.943, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 135, "s": [552.982, 552.982, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 136, "s": [565.429, 565.429, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.839, 0.839, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 137, "s": [577.239, 577.239, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 138, "s": [588.365, 588.365, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.173, 0.173, 0]}, "t": 139, "s": [598.763, 598.763, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 140, "s": [608.387, 608.387, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.176, 0.176, 0]}, "t": 141, "s": [617.191, 617.191, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 142, "s": [625.13, 625.13, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 143, "s": [632.158, 632.158, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.851, 0.851, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 144, "s": [638.23, 638.23, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.856, 0.856, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 145, "s": [643.3, 643.3, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.198, 0.198, 0]}, "t": 146, "s": [647.322, 647.322, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.888, 0.888, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.22, 0.22, 0]}, "t": 147, "s": [650.252, 650.252, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.917, 0.917, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.329, 0.329, 0]}, "t": 148, "s": [652.043, 652.043, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.051, 0.051, 0]}, "t": 149, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 150, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 151, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 152, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 153, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 154, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 155, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 156, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 157, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 158, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 159, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 160, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 161, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 162, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 163, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 164, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 165, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 166, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 167, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 168, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 169, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 170, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 171, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 172, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 173, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 174, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 176, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 178, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 179, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 180, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 181, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 182, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 184, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 185, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [50.796, 50.796, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 186, "s": [652.65, 652.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 187, "s": [652.65, 652.65, 100]}, {"t": 188.000007657397, "s": [55.1, 55.1, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.881, 0]], "o": [[0, 0], [0, 0], [11.88, 0]], "v": [[23.008, 13.61], [-23.008, 13.61], [0, -13.61]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.727, 112.437], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Sand Timer 2", "parent": 16, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -32.128, "ix": 10}, "p": {"a": 0, "k": [86.465, 108.908, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40.11, 63.183, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.983, 8.582], [-2.632, 0], [0, 0], [-0.597, -2.558], [-5.235, -5.457], [0, 0], [0.002, -10.156], [0, 0], [0, 0], [0, 0], [-7.025, 7.377]], "o": [[5.221, -5.478], [0.592, -2.565], [0, 0], [2.626, 0], [2.004, 8.581], [0, 0], [7.044, 7.347], [0, 0], [0, 0], [0, 0], [-0.002, -10.147], [0, 0]], "v": [[-20.142, -35.157], [-8.437, -58.523], [-2.976, -62.932], [2.748, -62.932], [8.205, -58.536], [19.984, -35.211], [26.19, -28.758], [39.044, 3.085], [39.101, 62.808], [-39.007, 62.932], [-39.099, 3.197], [-26.332, -28.68]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.936999990426, 0.717999985639, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.868, 63.183], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -12.00000048877, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Sand Timer 1", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [32.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [32.128]}, {"i": {"x": [0.833], "y": [0.948]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [32.128]}, {"i": {"x": [0.833], "y": [0.651]}, "o": {"x": [0.167], "y": [0.083]}, "t": 74, "s": [32.128]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 75, "s": [32.756]}, {"i": {"x": [0.833], "y": [0.791]}, "o": {"x": [0.167], "y": [0.13]}, "t": 76, "s": [34.76]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 77, "s": [38.344]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.144]}, "t": 78, "s": [43.738]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.147]}, "t": 79, "s": [51.189]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}, "t": 80, "s": [60.933]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.153]}, "t": 81, "s": [73.125]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}, "t": 82, "s": [87.724]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.161]}, "t": 83, "s": [104.338]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [122.128]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}, "t": 85, "s": [139.919]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.178]}, "t": 86, "s": [156.533]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}, "t": 87, "s": [171.131]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.188]}, "t": 88, "s": [183.324]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.192]}, "t": 89, "s": [193.068]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 90, "s": [200.519]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.209]}, "t": 91, "s": [205.913]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 92, "s": [209.496]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.349]}, "t": 93, "s": [211.501]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.052]}, "t": 94, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 110, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 111, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 112, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 113, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 114, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 116, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 117, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 119, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 121, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 122, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 123, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 124, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 125, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 126, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 127, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 128, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 129, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 130, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 131, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 132, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 133, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 134, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 135, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 136, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 137, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 138, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 139, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 140, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 142, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 143, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 144, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 145, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 146, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 147, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 148, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 149, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 150, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 151, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 152, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 153, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 154, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 155, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 156, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 158, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 159, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 160, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 161, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 162, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 163, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 164, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 165, "s": [212.128]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 166, "s": [212.128]}, {"i": {"x": [0.833], "y": [0.948]}, "o": {"x": [0.167], "y": [0]}, "t": 167, "s": [212.128]}, {"i": {"x": [0.833], "y": [0.651]}, "o": {"x": [0.167], "y": [0.083]}, "t": 168, "s": [212.128]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 169, "s": [212.756]}, {"i": {"x": [0.833], "y": [0.791]}, "o": {"x": [0.167], "y": [0.13]}, "t": 170, "s": [214.76]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 171, "s": [218.344]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.144]}, "t": 172, "s": [223.738]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.147]}, "t": 173, "s": [231.189]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}, "t": 174, "s": [240.933]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.153]}, "t": 175, "s": [253.125]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}, "t": 176, "s": [267.724]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.161]}, "t": 177, "s": [284.338]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [302.128]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}, "t": 179, "s": [319.919]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.178]}, "t": 180, "s": [336.533]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}, "t": 181, "s": [351.131]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.188]}, "t": 182, "s": [363.324]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.192]}, "t": 183, "s": [373.068]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 184, "s": [380.519]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.209]}, "t": 185, "s": [385.913]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 186, "s": [389.496]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.349]}, "t": 187, "s": [391.501]}, {"t": 188.000007657397, "s": [392.128]}], "ix": 10}, "p": {"a": 0, "k": [50, 50.061, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [86.131, 162.052, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.255, 7.177], [-2.255, 7.177], [-2.255, -7.177], [2.255, -7.177]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 40, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.935, 316.271], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-708.041, 311.605], [-676.312, 311.605], [-676.312, 297.275], [-708.041, 297.275]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 40, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [727.478, 11.844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.255, 7.177], [-2.255, 7.177], [-2.255, -7.177], [2.255, -7.177]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 40, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.935, 7.427], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-708.041, 2.761], [-676.312, 2.761], [-676.312, -11.568], [-708.041, -11.568]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 40, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [727.478, 11.844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.625, 7.264], [-85.648, 7.015], [-85.624, -7.264], [85.648, -7.017]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.26699999641, 0.328999986836, 0.426999978458, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [85.898, 316.59], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.625, 7.265], [-85.646, 7.017], [-85.624, -7.265], [85.646, -7.027]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.26699999641, 0.328999986836, 0.426999978458, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [86.366, 7.516], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.053, 150.959], [-3.47, 150.951], [-3.055, -150.959], [3.47, -150.954]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.176000004189, 0.165000002992, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [156.312, 159.454], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.055, 150.966], [-3.468, 150.958], [-3.056, -150.966], [3.468, -150.958]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.176000004189, 0.165000002992, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.968, 159.245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.007, -5.204], [5.209, 0.003], [0, 0], [0.009, 5.205], [-5.209, -0.002], [0, 0]], "o": [[0.003, 5.194], [0, 0], [-5.208, -0.003], [-0.004, -5.193], [0, 0], [5.208, 0.003]], "v": [[57.361, 0.091], [47.921, 9.499], [-47.928, 9.35], [-57.359, -0.092], [-47.919, -9.5], [47.928, -9.351]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.176000004189, 0.165000002992, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [85.922, 307.158], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.003, -5.194], [5.208, 0.003], [0, 0], [-0.033, 5.219], [-5.209, -0.003], [0, 0]], "o": [[0.008, 5.205], [0, 0], [-5.211, -0.013], [-0.004, -5.193], [0, 0], [5.211, 0.013]], "v": [[57.371, 0.081], [47.935, 9.499], [-47.913, 9.35], [-57.345, -0.092], [-47.905, -9.499], [47.944, -9.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.176000004189, 0.165000002992, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [86.339, 17.776], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.002, 11.961], [0, 0], [0, 0], [0, 0], [-8.274, -8.689], [0, 0], [-0.043, -11.948], [8.299, -8.651], [0, 0], [-0.001, -11.961], [0, 0], [0, 0], [0, 0], [8.316, 8.675], [0, 0], [0.001, 11.963], [-8.297, 8.651]], "o": [[8.298, -8.653], [0, 0], [0, 0], [0, 0], [-0.003, 11.952], [0, 0], [8.272, 8.681], [-0.044, 11.966], [0, 0], [-8.299, 8.654], [0, 0], [0, 0], [0, 0], [0.042, -11.977], [0, 0], [-8.268, -8.67], [0.044, -11.966], [0, 0]], "v": [[31.012, -45.03], [46.153, -82.533], [46.22, -140.013], [-45.781, -140.16], [-45.889, -82.666], [-30.851, -45.12], [-23.56, -37.492], [-8.502, -0.022], [-23.662, 37.429], [-30.97, 45.029], [-46.112, 82.534], [-46.22, 140.03], [45.823, 140.16], [45.888, 82.68], [30.848, 45.125], [23.602, 37.492], [8.54, 0.012], [23.702, -37.428]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.894000004787, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [86.119, 163.668], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 3, "nm": "Null 3", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 1, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 2, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 3, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 4, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 7, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 8, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 9, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 10, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 11, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 12, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 14, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 15, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 16, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 17, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 21, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 22, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 30, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 33, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 34, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 36, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 41, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 43, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 44, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 46, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 47, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 48, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 49, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 50, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 51, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 52, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 55, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 57, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 58, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 59, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 61, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 62, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 64, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 65, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 66, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 67, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 71, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 72, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.948]}, "o": {"x": [0.167], "y": [0]}, "t": 73, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.651]}, "o": {"x": [0.167], "y": [0.083]}, "t": 74, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 75, "s": [64.885]}, {"i": {"x": [0.833], "y": [0.791]}, "o": {"x": [0.167], "y": [0.13]}, "t": 76, "s": [66.889]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 77, "s": [70.472]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.144]}, "t": 78, "s": [75.866]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.147]}, "t": 79, "s": [83.318]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}, "t": 80, "s": [93.062]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.153]}, "t": 81, "s": [105.254]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}, "t": 82, "s": [119.853]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.161]}, "t": 83, "s": [136.466]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [154.257]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}, "t": 85, "s": [172.047]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.178]}, "t": 86, "s": [188.661]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}, "t": 87, "s": [203.26]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.188]}, "t": 88, "s": [215.452]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.192]}, "t": 89, "s": [225.196]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 90, "s": [232.648]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.209]}, "t": 91, "s": [238.041]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 92, "s": [241.625]}, {"i": {"x": [0.833], "y": [24.809]}, "o": {"x": [0.167], "y": [0.349]}, "t": 93, "s": [243.629]}, {"i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.083]}, "t": 94, "s": [244.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-15]}, "t": 95, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 96, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 97, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 99, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 101, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 102, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 103, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 104, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 105, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 106, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 110, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 111, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 112, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 113, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 114, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 116, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 117, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 119, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 121, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 122, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 123, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 124, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 125, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 126, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 127, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 128, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 129, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 130, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 131, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 132, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 133, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 134, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 135, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 136, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 137, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 138, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 139, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 140, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 142, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 143, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 144, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 145, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 146, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 147, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 148, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 149, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 150, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 151, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 152, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 153, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 154, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 155, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 156, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 158, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 159, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 160, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 161, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 162, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 163, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 164, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 165, "s": [64.257]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 166, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.948]}, "o": {"x": [0.167], "y": [0]}, "t": 167, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.651]}, "o": {"x": [0.167], "y": [0.083]}, "t": 168, "s": [64.257]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 169, "s": [64.885]}, {"i": {"x": [0.833], "y": [0.791]}, "o": {"x": [0.167], "y": [0.13]}, "t": 170, "s": [66.889]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 171, "s": [70.472]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.144]}, "t": 172, "s": [75.866]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.147]}, "t": 173, "s": [83.318]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}, "t": 174, "s": [93.062]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.153]}, "t": 175, "s": [105.254]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}, "t": 176, "s": [119.853]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.161]}, "t": 177, "s": [136.466]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.167]}, "t": 178, "s": [154.257]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}, "t": 179, "s": [172.047]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.178]}, "t": 180, "s": [188.661]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}, "t": 181, "s": [203.26]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.188]}, "t": 182, "s": [215.452]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.192]}, "t": 183, "s": [225.196]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 184, "s": [232.648]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.209]}, "t": 185, "s": [238.041]}, {"i": {"x": [0.833], "y": [8.375]}, "o": {"x": [0.167], "y": [0.232]}, "t": 186, "s": [241.625]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.082]}, "t": 187, "s": [243.629]}, {"t": 188.000007657397, "s": [64.257]}], "ix": 10}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 3, "nm": "Null 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [771.391, 398.484, 0], "to": [0, -0.167, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [771.391, 397.484, 0], "to": [0, -0.333, 0], "ti": [0, 0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 2, "s": [771.391, 396.486, 0], "to": [0, -0.33, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 3, "s": [771.391, 395.502, 0], "to": [0, -0.324, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 4, "s": [771.391, 394.54, 0], "to": [0, -0.315, 0], "ti": [0, 0.303, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 5, "s": [771.391, 393.611, 0], "to": [0, -0.303, 0], "ti": [0, 0.287, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 6, "s": [771.391, 392.723, 0], "to": [0, -0.287, 0], "ti": [0, 0.269, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 7, "s": [771.391, 391.886, 0], "to": [0, -0.269, 0], "ti": [0, 0.248, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 8, "s": [771.391, 391.108, 0], "to": [0, -0.248, 0], "ti": [0, 0.225, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 9, "s": [771.391, 390.397, 0], "to": [0, -0.225, 0], "ti": [0, 0.199, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 10, "s": [771.391, 389.759, 0], "to": [0, -0.199, 0], "ti": [0, 0.171, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 11, "s": [771.391, 389.202, 0], "to": [0, -0.171, 0], "ti": [0, 0.142, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 12, "s": [771.391, 388.731, 0], "to": [0, -0.142, 0], "ti": [0, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.194}, "t": 13, "s": [771.391, 388.35, 0], "to": [0, -0.111, 0], "ti": [0, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.21}, "t": 14, "s": [771.391, 388.064, 0], "to": [0, -0.079, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.893}, "o": {"x": 0.167, "y": 0.258}, "t": 15, "s": [771.391, 387.875, 0], "to": [0, -0.047, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.56}, "o": {"x": 0.167, "y": 0.376}, "t": 16, "s": [771.391, 387.785, 0], "to": [0, -0.013, 0], "ti": [0, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.758}, "o": {"x": 0.167, "y": 0.103}, "t": 17, "s": [771.391, 387.795, 0], "to": [0, 0.02, 0], "ti": [0, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.127}, "t": 18, "s": [771.391, 387.905, 0], "to": [0, 0.053, 0], "ti": [0, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.14}, "t": 19, "s": [771.391, 388.114, 0], "to": [0, 0.086, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.147}, "t": 20, "s": [771.391, 388.419, 0], "to": [0, 0.117, 0], "ti": [0, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 21, "s": [771.391, 388.818, 0], "to": [0, 0.148, 0], "ti": [0, -0.177, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 22, "s": [771.391, 389.307, 0], "to": [0, 0.177, 0], "ti": [0, -0.204, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 23, "s": [771.391, 389.881, 0], "to": [0, 0.204, 0], "ti": [0, -0.23, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 24, "s": [771.391, 390.534, 0], "to": [0, 0.23, 0], "ti": [0, -0.253, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 25, "s": [771.391, 391.259, 0], "to": [0, 0.253, 0], "ti": [0, -0.273, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 26, "s": [771.391, 392.05, 0], "to": [0, 0.273, 0], "ti": [0, -0.291, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 27, "s": [771.391, 392.898, 0], "to": [0, 0.291, 0], "ti": [0, -0.306, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 28, "s": [771.391, 393.794, 0], "to": [0, 0.306, 0], "ti": [0, -0.317, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 29, "s": [771.391, 394.731, 0], "to": [0, 0.317, 0], "ti": [0, -0.326, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 30, "s": [771.391, 395.698, 0], "to": [0, 0.326, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 31, "s": [771.391, 396.686, 0], "to": [0, 0.331, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [771.391, 397.685, 0], "to": [0, 0.333, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [771.391, 398.685, 0], "to": [0, 0.332, 0], "ti": [0, -0.327, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 34, "s": [771.391, 399.675, 0], "to": [0, 0.327, 0], "ti": [0, -0.319, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 35, "s": [771.391, 400.647, 0], "to": [0, 0.319, 0], "ti": [0, -0.308, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 36, "s": [771.391, 401.59, 0], "to": [0, 0.308, 0], "ti": [0, -0.294, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 37, "s": [771.391, 402.495, 0], "to": [0, 0.294, 0], "ti": [0, -0.277, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 38, "s": [771.391, 403.352, 0], "to": [0, 0.277, 0], "ti": [0, -0.257, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 39, "s": [771.391, 404.154, 0], "to": [0, 0.257, 0], "ti": [0, -0.234, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 40, "s": [771.391, 404.892, 0], "to": [0, 0.234, 0], "ti": [0, -0.209, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 41, "s": [771.391, 405.558, 0], "to": [0, 0.209, 0], "ti": [0, -0.182, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 42, "s": [771.391, 406.147, 0], "to": [0, 0.182, 0], "ti": [0, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.184}, "t": 43, "s": [771.391, 406.652, 0], "to": [0, 0.153, 0], "ti": [0, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.191}, "t": 44, "s": [771.391, 407.068, 0], "to": [0, 0.123, 0], "ti": [0, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.202}, "t": 45, "s": [771.391, 407.391, 0], "to": [0, 0.092, 0], "ti": [0, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.231}, "t": 46, "s": [771.391, 407.618, 0], "to": [0, 0.059, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.735}, "o": {"x": 0.167, "y": 0.409}, "t": 47, "s": [771.391, 407.746, 0], "to": [0, 0.026, 0], "ti": [0, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.718}, "o": {"x": 0.167, "y": 0.122}, "t": 48, "s": [771.391, 407.774, 0], "to": [0, -0.007, 0], "ti": [0, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.118}, "t": 49, "s": [771.391, 407.703, 0], "to": [0, -0.04, 0], "ti": [0, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.136}, "t": 50, "s": [771.391, 407.532, 0], "to": [0, -0.073, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 51, "s": [771.391, 407.263, 0], "to": [0, -0.105, 0], "ti": [0, 0.136, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.15}, "t": 52, "s": [771.391, 406.899, 0], "to": [0, -0.136, 0], "ti": [0, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 53, "s": [771.391, 406.444, 0], "to": [0, -0.166, 0], "ti": [0, 0.194, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 54, "s": [771.391, 405.903, 0], "to": [0, -0.194, 0], "ti": [0, 0.22, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 55, "s": [771.391, 405.279, 0], "to": [0, -0.22, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 56, "s": [771.391, 404.581, 0], "to": [0, -0.244, 0], "ti": [0, 0.266, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 57, "s": [771.391, 403.815, 0], "to": [0, -0.266, 0], "ti": [0, 0.284, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 58, "s": [771.391, 402.988, 0], "to": [0, -0.284, 0], "ti": [0, 0.3, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 59, "s": [771.391, 402.109, 0], "to": [0, -0.3, 0], "ti": [0, 0.313, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 60, "s": [771.391, 401.187, 0], "to": [0, -0.313, 0], "ti": [0, 0.323, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 61, "s": [771.391, 400.23, 0], "to": [0, -0.323, 0], "ti": [0, 0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 62, "s": [771.391, 399.249, 0], "to": [0, -0.329, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 63, "s": [771.391, 398.253, 0], "to": [0, -0.333, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [771.391, 397.253, 0], "to": [0, -0.333, 0], "ti": [0, 0.329, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 65, "s": [771.391, 396.257, 0], "to": [0, -0.329, 0], "ti": [0, 0.323, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 66, "s": [771.391, 395.277, 0], "to": [0, -0.323, 0], "ti": [0, 0.313, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 67, "s": [771.391, 394.322, 0], "to": [0, -0.313, 0], "ti": [0, 0.3, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 68, "s": [771.391, 393.402, 0], "to": [0, -0.3, 0], "ti": [0, 0.283, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 69, "s": [771.391, 392.525, 0], "to": [0, -0.283, 0], "ti": [0, 0.265, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 70, "s": [771.391, 391.701, 0], "to": [0, -0.265, 0], "ti": [0, 0.243, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 71, "s": [771.391, 390.938, 0], "to": [0, -0.243, 0], "ti": [0, 0.219, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 72, "s": [771.391, 390.243, 0], "to": [0, -0.219, 0], "ti": [0, 0.193, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 73, "s": [771.391, 389.623, 0], "to": [0, -0.193, 0], "ti": [0, 0.165, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.183}, "t": 74, "s": [771.391, 389.085, 0], "to": [0, -0.165, 0], "ti": [0, 0.135, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 75, "s": [771.391, 388.635, 0], "to": [0, -0.135, 0], "ti": [0, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.197}, "t": 76, "s": [771.391, 388.276, 0], "to": [0, -0.104, 0], "ti": [0, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.216}, "t": 77, "s": [771.391, 388.011, 0], "to": [0, -0.072, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.291}, "t": 78, "s": [771.391, 387.845, 0], "to": [0, -0.039, 0], "ti": [0, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.612}, "o": {"x": 0.167, "y": 0.236}, "t": 79, "s": [771.391, 387.778, 0], "to": [0, -0.006, 0], "ti": [0, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.106}, "t": 80, "s": [771.391, 387.811, 0], "to": [0, 0.028, 0], "ti": [0, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.131}, "t": 81, "s": [771.391, 387.944, 0], "to": [0, 0.061, 0], "ti": [0, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 82, "s": [771.391, 388.176, 0], "to": [0, 0.093, 0], "ti": [0, -0.125, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 83, "s": [771.391, 388.503, 0], "to": [0, 0.125, 0], "ti": [0, -0.155, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 84, "s": [771.391, 388.924, 0], "to": [0, 0.155, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 85, "s": [771.391, 389.433, 0], "to": [0, 0.184, 0], "ti": [0, -0.21, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 86, "s": [771.391, 390.025, 0], "to": [0, 0.21, 0], "ti": [0, -0.235, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 87, "s": [771.391, 390.695, 0], "to": [0, 0.235, 0], "ti": [0, -0.258, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 88, "s": [771.391, 391.436, 0], "to": [0, 0.258, 0], "ti": [0, -0.277, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 89, "s": [771.391, 392.241, 0], "to": [0, 0.277, 0], "ti": [0, -0.294, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 90, "s": [771.391, 393.101, 0], "to": [0, 0.294, 0], "ti": [0, -0.309, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 91, "s": [771.391, 394.008, 0], "to": [0, 0.309, 0], "ti": [0, -0.32, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 92, "s": [771.391, 394.952, 0], "to": [0, 0.32, 0], "ti": [0, -0.327, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 93, "s": [771.391, 395.925, 0], "to": [0, 0.327, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 94, "s": [771.391, 396.916, 0], "to": [0, 0.332, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [771.391, 397.916, 0], "to": [0, 0.333, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 96, "s": [771.391, 398.915, 0], "to": [0, 0.331, 0], "ti": [0, -0.326, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 97, "s": [771.391, 399.902, 0], "to": [0, 0.326, 0], "ti": [0, -0.317, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 98, "s": [771.391, 400.868, 0], "to": [0, 0.317, 0], "ti": [0, -0.305, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 99, "s": [771.391, 401.803, 0], "to": [0, 0.305, 0], "ti": [0, -0.29, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 100, "s": [771.391, 402.697, 0], "to": [0, 0.29, 0], "ti": [0, -0.272, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 101, "s": [771.391, 403.543, 0], "to": [0, 0.272, 0], "ti": [0, -0.252, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 102, "s": [771.391, 404.33, 0], "to": [0, 0.252, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 103, "s": [771.391, 405.052, 0], "to": [0, 0.229, 0], "ti": [0, -0.203, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 104, "s": [771.391, 405.701, 0], "to": [0, 0.203, 0], "ti": [0, -0.176, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 105, "s": [771.391, 406.271, 0], "to": [0, 0.176, 0], "ti": [0, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 106, "s": [771.391, 406.756, 0], "to": [0, 0.147, 0], "ti": [0, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.193}, "t": 107, "s": [771.391, 407.151, 0], "to": [0, 0.116, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.206}, "t": 108, "s": [771.391, 407.452, 0], "to": [0, 0.084, 0], "ti": [0, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.245}, "t": 109, "s": [771.391, 407.656, 0], "to": [0, 0.052, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.601}, "o": {"x": 0.167, "y": 0.434}, "t": 110, "s": [771.391, 407.761, 0], "to": [0, 0.018, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.746}, "o": {"x": 0.167, "y": 0.106}, "t": 111, "s": [771.391, 407.767, 0], "to": [0, -0.015, 0], "ti": [0, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.124}, "t": 112, "s": [771.391, 407.672, 0], "to": [0, -0.048, 0], "ti": [0, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 113, "s": [771.391, 407.478, 0], "to": [0, -0.081, 0], "ti": [0, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 114, "s": [771.391, 407.187, 0], "to": [0, -0.113, 0], "ti": [0, 0.143, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 115, "s": [771.391, 406.802, 0], "to": [0, -0.143, 0], "ti": [0, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 116, "s": [771.391, 406.327, 0], "to": [0, -0.173, 0], "ti": [0, 0.2, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 117, "s": [771.391, 405.766, 0], "to": [0, -0.2, 0], "ti": [0, 0.226, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 118, "s": [771.391, 405.125, 0], "to": [0, -0.226, 0], "ti": [0, 0.249, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 119, "s": [771.391, 404.41, 0], "to": [0, -0.249, 0], "ti": [0, 0.27, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 120, "s": [771.391, 403.629, 0], "to": [0, -0.27, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 121, "s": [771.391, 402.789, 0], "to": [0, -0.288, 0], "ti": [0, 0.303, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 122, "s": [771.391, 401.9, 0], "to": [0, -0.303, 0], "ti": [0, 0.316, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 123, "s": [771.391, 400.968, 0], "to": [0, -0.316, 0], "ti": [0, 0.325, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 124, "s": [771.391, 400.005, 0], "to": [0, -0.325, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 125, "s": [771.391, 399.02, 0], "to": [0, -0.331, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 126, "s": [771.391, 398.022, 0], "to": [0, -0.333, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [771.391, 397.022, 0], "to": [0, -0.332, 0], "ti": [0, 0.328, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 128, "s": [771.391, 396.029, 0], "to": [0, -0.328, 0], "ti": [0, 0.321, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 129, "s": [771.391, 395.054, 0], "to": [0, -0.321, 0], "ti": [0, 0.31, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 130, "s": [771.391, 394.106, 0], "to": [0, -0.31, 0], "ti": [0, 0.296, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 131, "s": [771.391, 393.195, 0], "to": [0, -0.296, 0], "ti": [0, 0.279, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 132, "s": [771.391, 392.33, 0], "to": [0, -0.279, 0], "ti": [0, 0.26, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 133, "s": [771.391, 391.519, 0], "to": [0, -0.26, 0], "ti": [0, 0.238, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 134, "s": [771.391, 390.771, 0], "to": [0, -0.238, 0], "ti": [0, 0.213, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 135, "s": [771.391, 390.093, 0], "to": [0, -0.213, 0], "ti": [0, 0.187, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 136, "s": [771.391, 389.492, 0], "to": [0, -0.187, 0], "ti": [0, 0.158, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.184}, "t": 137, "s": [771.391, 388.973, 0], "to": [0, -0.158, 0], "ti": [0, 0.128, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.189}, "t": 138, "s": [771.391, 388.543, 0], "to": [0, -0.128, 0], "ti": [0, 0.097, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.2}, "t": 139, "s": [771.391, 388.206, 0], "to": [0, -0.097, 0], "ti": [0, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.224}, "t": 140, "s": [771.391, 387.964, 0], "to": [0, -0.064, 0], "ti": [0, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.354}, "t": 141, "s": [771.391, 387.821, 0], "to": [0, -0.031, 0], "ti": [0, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.686}, "o": {"x": 0.167, "y": 0.149}, "t": 142, "s": [771.391, 387.777, 0], "to": [0, 0.002, 0], "ti": [0, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.113}, "t": 143, "s": [771.391, 387.833, 0], "to": [0, 0.035, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.134}, "t": 144, "s": [771.391, 387.989, 0], "to": [0, 0.068, 0], "ti": [0, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 145, "s": [771.391, 388.243, 0], "to": [0, 0.101, 0], "ti": [0, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 146, "s": [771.391, 388.592, 0], "to": [0, 0.132, 0], "ti": [0, -0.162, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 147, "s": [771.391, 389.033, 0], "to": [0, 0.162, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 148, "s": [771.391, 389.562, 0], "to": [0, 0.19, 0], "ti": [0, -0.216, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 149, "s": [771.391, 390.173, 0], "to": [0, 0.216, 0], "ti": [0, -0.241, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 150, "s": [771.391, 390.86, 0], "to": [0, 0.241, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 151, "s": [771.391, 391.617, 0], "to": [0, 0.262, 0], "ti": [0, -0.282, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 152, "s": [771.391, 392.435, 0], "to": [0, 0.282, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 153, "s": [771.391, 393.306, 0], "to": [0, 0.298, 0], "ti": [0, -0.311, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 154, "s": [771.391, 394.223, 0], "to": [0, 0.311, 0], "ti": [0, -0.322, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 155, "s": [771.391, 395.175, 0], "to": [0, 0.322, 0], "ti": [0, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 156, "s": [771.391, 396.153, 0], "to": [0, 0.329, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 157, "s": [771.391, 397.147, 0], "to": [0, 0.332, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [771.391, 398.147, 0], "to": [0, 0.333, 0], "ti": [0, -0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 159, "s": [771.391, 399.144, 0], "to": [0, 0.33, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 160, "s": [771.391, 400.127, 0], "to": [0, 0.324, 0], "ti": [0, -0.314, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 161, "s": [771.391, 401.087, 0], "to": [0, 0.314, 0], "ti": [0, -0.302, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 162, "s": [771.391, 402.013, 0], "to": [0, 0.302, 0], "ti": [0, -0.286, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 163, "s": [771.391, 402.897, 0], "to": [0, 0.286, 0], "ti": [0, -0.268, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 164, "s": [771.391, 403.73, 0], "to": [0, 0.268, 0], "ti": [0, -0.246, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 165, "s": [771.391, 404.503, 0], "to": [0, 0.246, 0], "ti": [0, -0.223, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 166, "s": [771.391, 405.209, 0], "to": [0, 0.223, 0], "ti": [0, -0.197, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 167, "s": [771.391, 405.84, 0], "to": [0, 0.197, 0], "ti": [0, -0.169, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.182}, "t": 168, "s": [771.391, 406.391, 0], "to": [0, 0.169, 0], "ti": [0, -0.14, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.187}, "t": 169, "s": [771.391, 406.855, 0], "to": [0, 0.14, 0], "ti": [0, -0.109, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.195}, "t": 170, "s": [771.391, 407.229, 0], "to": [0, 0.109, 0], "ti": [0, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.211}, "t": 171, "s": [771.391, 407.508, 0], "to": [0, 0.077, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.267}, "t": 172, "s": [771.391, 407.689, 0], "to": [0, 0.044, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.575}, "o": {"x": 0.167, "y": 0.322}, "t": 173, "s": [771.391, 407.772, 0], "to": [0, 0.011, 0], "ti": [0, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.763}, "o": {"x": 0.167, "y": 0.103}, "t": 174, "s": [771.391, 407.754, 0], "to": [0, -0.023, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.129}, "t": 175, "s": [771.391, 407.636, 0], "to": [0, -0.056, 0], "ti": [0, 0.088, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.141}, "t": 176, "s": [771.391, 407.42, 0], "to": [0, -0.088, 0], "ti": [0, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.148}, "t": 177, "s": [771.391, 407.107, 0], "to": [0, -0.12, 0], "ti": [0, 0.15, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 178, "s": [771.391, 406.7, 0], "to": [0, -0.15, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 179, "s": [771.391, 406.205, 0], "to": [0, -0.179, 0], "ti": [0, 0.206, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 180, "s": [771.391, 405.624, 0], "to": [0, -0.206, 0], "ti": [0, 0.232, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 181, "s": [771.391, 404.966, 0], "to": [0, -0.232, 0], "ti": [0, 0.254, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 182, "s": [771.391, 404.235, 0], "to": [0, -0.254, 0], "ti": [0, 0.275, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 183, "s": [771.391, 403.44, 0], "to": [0, -0.275, 0], "ti": [0, 0.292, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 184, "s": [771.391, 402.588, 0], "to": [0, -0.292, 0], "ti": [0, 0.307, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 185, "s": [771.391, 401.688, 0], "to": [0, -0.307, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 186, "s": [771.391, 400.748, 0], "to": [0, -0.318, 0], "ti": [0, 0.326, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 187, "s": [771.391, 399.779, 0], "to": [0, -0.326, 0], "ti": [0, 0.165, 0]}, {"t": 188.000007657397, "s": [771.391, 398.79, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Hand Timer 8", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.561, 114.439, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.561, 114.439, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.832, 0.02], [0, 0], [-0.016, 7.833], [0, 0], [-7.835, -0.019], [0, 0], [0.02, -7.835]], "o": [[-0.019, 7.834], [0, 0], [-7.836, -0.018], [0, 0], [0.02, -7.835], [0, 0], [7.834, 0.02], [0, 0]], "v": [[34.611, 8.44], [20.39, 22.594], [-20.5, 22.492], [-34.656, 8.27], [-34.613, -8.44], [-20.389, -22.594], [20.5, -22.492], [34.651, -8.268]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [79.129, 195.193], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.836, 0.019], [0, 0], [-0.018, 7.834], [0, 0], [-7.835, -0.02], [0, 0], [0.02, -7.835]], "o": [[-0.02, 7.835], [0, 0], [-7.838, -0.02], [0, 0], [0.019, -7.834], [0, 0], [7.837, 0.021], [0, 0]], "v": [[34.611, 8.441], [20.388, 22.594], [-20.5, 22.494], [-34.654, 8.27], [-34.612, -8.441], [-20.388, -22.593], [20.5, -22.492], [34.653, -8.268]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.863, 146.383], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.177, 0.018], [0, 0], [-0.018, 7.175], [0, 0], [-7.176, -0.016], [0, 0], [0.014, -7.176]], "o": [[-0.016, 7.175], [0, 0], [-7.175, -0.019], [0, 0], [0.018, -7.174], [0, 0], [7.178, 0.017], [0, 0]], "v": [[38.921, 12.904], [25.895, 25.863], [-26.022, 25.736], [-38.984, 12.711], [-38.921, -12.905], [-25.894, -25.866], [26.022, -25.733], [38.987, -12.71]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.251, 87.706], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [6.943, 0.018], [0, 0], [-0.023, 8.839], [0, 0], [-6.942, -0.017], [0, 0], [0.02, -8.838]], "o": [[-0.024, 8.838], [0, 0], [-6.943, -0.017], [0, 0], [0.021, -8.839], [0, 0], [6.939, 0.017], [0, 0]], "v": [[41.329, 9.526], [28.716, 25.498], [-28.841, 25.355], [-41.373, 9.321], [-41.326, -9.526], [-28.715, -25.499], [28.846, -25.356], [41.376, -9.322]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.322, 25.766], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Hand Timer 9", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -19.208, "ix": 10}, "p": {"a": 0, "k": [342.326, 124.291, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [291.887, 229.482, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [110.026, 110.026, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34.403, 16.098], [-36.776, -9.501], [-34.403, -16.098], [36.776, 9.501]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [275.345, 275.849], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.902, 90.16], [-63.276, 64.559], [-7.632, -90.16], [63.276, -63.66]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [291.887, 229.482], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Hand Timer 10", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -19.208, "ix": 10}, "p": {"a": 0, "k": [410.753, 125.26, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [48.813, 79.2, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [110.026, 110.026, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.792, 8.389], [0, 0], [8.024, 2.732], [0, 0]], "o": [[0, 0], [8.227, 3.237], [0, 0], [2.677, -8.043], [0, 0], [0, 0]], "v": [[-49.504, 62.035], [-11.993, 76.794], [8.286, 67.314], [46.827, -48.444], [37.165, -67.915], [1.59, -80.031]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.755, 80.281], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Hand Timer 6", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11, "s": [-4]}, {"t": 22.0000008960784, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [340.11, 146.701, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [225.651, 216.576, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[89.959, 51.709], [-75.574, -47.704], [-105.423, 6.297], [9.813, 24.62], [0, 0]], "o": [[0, 0], [33.19, 20.953], [0.78, -27.69], [-28.577, -3.139], [0, 0]], "v": [[-90.515, -111.187], [-40.538, -25.054], [97.926, 104.89], [106.298, 18.193], [62.194, -10.213]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [116.361, 111.437], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -19.0000007738859, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Hand Timer 2", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [146.978, -15.796, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [42.221, 34.067, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.26, -6.479], [0, 0], [-8.391, -1.631], [0, 0], [-1.258, 6.478], [0, 0], [8.391, 1.632]], "o": [[-8.391, -1.632], [0, 0], [-1.26, 6.478], [0, 0], [8.392, 1.633], [0, 0], [1.26, -6.478], [0, 0]], "v": [[-12.521, -25.624], [-30.033, -16.852], [-34.159, 4.371], [-21.211, 19.069], [12.522, 25.628], [30.025, 16.852], [34.152, -4.372], [21.214, -19.064]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[11.659, 2.267], [0, 0], [-1.895, 9.736], [0, 0], [-11.664, -2.267], [0, 0], [1.893, -9.735], [0, 0]], "o": [[0, 0], [-11.664, -2.268], [0, 0], [1.893, -9.736], [0, 0], [11.66, 2.266], [0, 0], [-1.891, 9.734]], "v": [[11.372, 31.55], [-22.363, 24.991], [-40.076, 3.219], [-35.949, -18.002], [-11.369, -31.549], [22.366, -24.989], [40.078, -3.22], [35.951, 18.004]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.221, 34.067], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -19.0000007738859, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Hand Timer 5", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [121.832, 113.527, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [6.573, 6.575, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.352, -1.804], [1.795, 0.349], [-0.346, 1.782], [-1.799, -0.35]], "o": [[-0.348, 1.786], [-1.799, -0.35], [0.352, -1.807], [1.793, 0.348]], "v": [[3.25, 0.649], [-0.633, 3.24], [-3.255, -0.614], [0.628, -3.239]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969000004787, 0.969000004787, 0.972999961703, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.573, 6.573], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.616, -3.168], [3.151, 0.611], [-0.613, 3.14], [-3.151, -0.612]], "o": [[-0.613, 3.14], [-3.146, -0.611], [0.615, -3.172], [3.148, 0.612]], "v": [[5.707, 1.125], [-1.115, 5.714], [-5.709, -1.094], [1.109, -5.713]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.463000009574, 0.455000005984, 0.46699999641, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.572, 6.575], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -19.0000007738859, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Hand Timer 3", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [144.918, 4.427, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [142.918, 12.427, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 22.0000008960784, "s": [144.918, 4.427, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [26.003, 25.413, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[21.848, 14.668], [-25.753, 5.414], [-21.848, -14.668], [25.753, -5.413]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.003, 14.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.836, -15.234], [-17.607, 9.314], [12.833, 15.234], [17.607, -9.315]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.804000016755, 0.811999990426, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [22.206, 35.342], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -19.0000007738859, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Hand Timer 4", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"t": 63.0000025660426, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [121.397, 113.808, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [5.632, 62.471, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[2.657, 31.855], [-8.757, 29.724], [8.757, -31.855]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.007, 32.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": -19.0000007738859, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Hand Timer 1", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [22.013]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 1, "s": [21.764]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 2, "s": [21.508]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 3, "s": [21.247]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 4, "s": [20.982]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 5, "s": [20.714]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [20.445]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [20.175]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [19.905]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 9, "s": [19.637]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 10, "s": [19.373]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 11, "s": [19.112]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 12, "s": [18.858]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 13, "s": [18.609]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 14, "s": [18.368]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 15, "s": [18.136]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 16, "s": [17.914]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 17, "s": [17.703]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 18, "s": [17.503]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 19, "s": [17.316]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}, "t": 20, "s": [17.142]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.175]}, "t": 21, "s": [16.982]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 22, "s": [16.838]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.179]}, "t": 23, "s": [16.708]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.181]}, "t": 24, "s": [16.595]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.185]}, "t": 25, "s": [16.499]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.19]}, "t": 26, "s": [16.419]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.2]}, "t": 27, "s": [16.357]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.223]}, "t": 28, "s": [16.313]}, {"i": {"x": [0.833], "y": [1.011]}, "o": {"x": [0.167], "y": [0.344]}, "t": 29, "s": [16.287]}, {"i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.009]}, "t": 30, "s": [16.278]}, {"i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.112]}, "t": 31, "s": [16.288]}, {"i": {"x": [0.833], "y": [0.801]}, "o": {"x": [0.167], "y": [0.134]}, "t": 32, "s": [16.315]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.143]}, "t": 33, "s": [16.361]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.149]}, "t": 34, "s": [16.424]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.152]}, "t": 35, "s": [16.504]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.155]}, "t": 36, "s": [16.602]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 37, "s": [16.716]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.158]}, "t": 38, "s": [16.846]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}, "t": 39, "s": [16.991]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 40, "s": [17.152]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 41, "s": [17.327]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 42, "s": [17.515]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "t": 43, "s": [17.715]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 44, "s": [17.927]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 45, "s": [18.15]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 46, "s": [18.383]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 47, "s": [18.624]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 48, "s": [18.873]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 49, "s": [19.128]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 50, "s": [19.389]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 51, "s": [19.653]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 52, "s": [19.921]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "t": 53, "s": [20.191]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [20.461]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [20.731]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 56, "s": [20.998]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 57, "s": [21.263]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 58, "s": [21.524]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 59, "s": [21.779]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 60, "s": [22.028]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 61, "s": [22.27]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 62, "s": [22.502]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 63, "s": [22.725]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "t": 64, "s": [22.938]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 65, "s": [23.138]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 66, "s": [23.327]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}, "t": 67, "s": [23.501]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.175]}, "t": 68, "s": [23.662]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 69, "s": [23.808]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.178]}, "t": 70, "s": [23.938]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.181]}, "t": 71, "s": [24.053]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.184]}, "t": 72, "s": [24.151]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.19]}, "t": 73, "s": [24.231]}, {"i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.199]}, "t": 74, "s": [24.295]}, {"i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.22]}, "t": 75, "s": [24.34]}, {"i": {"x": [0.833], "y": [0.986]}, "o": {"x": [0.167], "y": [0.319]}, "t": 76, "s": [24.368]}, {"i": {"x": [0.833], "y": [0.65]}, "o": {"x": [0.167], "y": [-0.017]}, "t": 77, "s": [24.378]}, {"i": {"x": [0.833], "y": [0.776]}, "o": {"x": [0.167], "y": [0.109]}, "t": 78, "s": [24.37]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.133]}, "t": 79, "s": [24.344]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.143]}, "t": 80, "s": [24.3]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.148]}, "t": 81, "s": [24.238]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.152]}, "t": 82, "s": [24.159]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 83, "s": [24.063]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 84, "s": [23.95]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.158]}, "t": 85, "s": [23.821]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}, "t": 86, "s": [23.676]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 87, "s": [23.517]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 88, "s": [23.343]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 89, "s": [23.156]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "t": 90, "s": [22.957]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 91, "s": [22.746]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 92, "s": [22.524]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 93, "s": [22.292]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 94, "s": [22.051]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 95, "s": [21.803]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 96, "s": [21.548]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 97, "s": [21.288]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 98, "s": [21.023]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 99, "s": [20.756]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 100, "s": [20.486]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [20.216]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [19.946]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 103, "s": [19.678]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 104, "s": [19.413]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 105, "s": [19.152]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 106, "s": [18.896]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 107, "s": [18.647]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 108, "s": [18.405]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 109, "s": [18.171]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 110, "s": [17.948]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 111, "s": [17.734]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 112, "s": [17.533]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 113, "s": [17.344]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}, "t": 114, "s": [17.168]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.175]}, "t": 115, "s": [17.006]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 116, "s": [16.859]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.178]}, "t": 117, "s": [16.727]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.181]}, "t": 118, "s": [16.612]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.184]}, "t": 119, "s": [16.513]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.189]}, "t": 120, "s": [16.43]}, {"i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.198]}, "t": 121, "s": [16.366]}, {"i": {"x": [0.833], "y": [0.885]}, "o": {"x": [0.167], "y": [0.218]}, "t": 122, "s": [16.319]}, {"i": {"x": [0.833], "y": [0.967]}, "o": {"x": [0.167], "y": [0.3]}, "t": 123, "s": [16.289]}, {"i": {"x": [0.833], "y": [0.612]}, "o": {"x": [0.167], "y": [-0.055]}, "t": 124, "s": [16.278]}, {"i": {"x": [0.833], "y": [0.773]}, "o": {"x": [0.167], "y": [0.106]}, "t": 125, "s": [16.285]}, {"i": {"x": [0.833], "y": [0.799]}, "o": {"x": [0.167], "y": [0.132]}, "t": 126, "s": [16.31]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.142]}, "t": 127, "s": [16.352]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.148]}, "t": 128, "s": [16.413]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.152]}, "t": 129, "s": [16.491]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 130, "s": [16.586]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 131, "s": [16.697]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.158]}, "t": 132, "s": [16.825]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}, "t": 133, "s": [16.968]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 134, "s": [17.126]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 135, "s": [17.299]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 136, "s": [17.485]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "t": 137, "s": [17.684]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 138, "s": [17.894]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 139, "s": [18.115]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 140, "s": [18.346]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}, "t": 141, "s": [18.586]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "t": 142, "s": [18.834]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 143, "s": [19.088]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}, "t": 144, "s": [19.348]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 145, "s": [19.612]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 146, "s": [19.88]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "t": 147, "s": [20.149]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 148, "s": [20.419]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [20.689]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 150, "s": [20.957]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 151, "s": [21.223]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}, "t": 152, "s": [21.484]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 153, "s": [21.74]}, {"i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}, "t": 154, "s": [21.99]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 155, "s": [22.233]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "t": 156, "s": [22.467]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 157, "s": [22.692]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}, "t": 158, "s": [22.906]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 159, "s": [23.108]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 160, "s": [23.298]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}, "t": 161, "s": [23.475]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.175]}, "t": 162, "s": [23.638]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 163, "s": [23.787]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.178]}, "t": 164, "s": [23.919]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}, "t": 165, "s": [24.036]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.184]}, "t": 166, "s": [24.137]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.189]}, "t": 167, "s": [24.22]}, {"i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.197]}, "t": 168, "s": [24.286]}, {"i": {"x": [0.833], "y": [0.882]}, "o": {"x": [0.167], "y": [0.215]}, "t": 169, "s": [24.335]}, {"i": {"x": [0.833], "y": [0.952]}, "o": {"x": [0.167], "y": [0.285]}, "t": 170, "s": [24.365]}, {"i": {"x": [0.833], "y": [0.556]}, "o": {"x": [0.167], "y": [-0.111]}, "t": 171, "s": [24.378]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.103]}, "t": 172, "s": [24.372]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.131]}, "t": 173, "s": [24.349]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.142]}, "t": 174, "s": [24.308]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.148]}, "t": 175, "s": [24.249]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.151]}, "t": 176, "s": [24.172]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 177, "s": [24.079]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 178, "s": [23.968]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.157]}, "t": 179, "s": [23.842]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}, "t": 180, "s": [23.7]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 181, "s": [23.542]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 182, "s": [23.371]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 183, "s": [23.186]}, {"i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "t": 184, "s": [22.988]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 185, "s": [22.779]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "t": 186, "s": [22.558]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.164]}, "t": 187, "s": [22.328]}, {"t": 188.000007657397, "s": [22.089]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [1177.253, 659.068, 0], "to": [0.003, -0.075, 0], "ti": [-0.01, 0.15, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [1177.268, 658.618, 0], "to": [0.01, -0.15, 0], "ti": [-0.02, 0.149, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [1177.313, 658.169, 0], "to": [0.02, -0.149, 0], "ti": [-0.03, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [1177.387, 657.726, 0], "to": [0.03, -0.146, 0], "ti": [-0.04, 0.142, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 4, "s": [1177.492, 657.293, 0], "to": [0.04, -0.142, 0], "ti": [-0.049, 0.136, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 5, "s": [1177.625, 656.875, 0], "to": [0.049, -0.136, 0], "ti": [-0.058, 0.129, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 6, "s": [1177.786, 656.476, 0], "to": [0.058, -0.129, 0], "ti": [-0.068, 0.121, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 7, "s": [1177.976, 656.099, 0], "to": [0.068, -0.121, 0], "ti": [-0.076, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 8, "s": [1178.192, 655.749, 0], "to": [0.076, -0.112, 0], "ti": [-0.085, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 9, "s": [1178.434, 655.429, 0], "to": [0.085, -0.101, 0], "ti": [-0.093, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 10, "s": [1178.701, 655.142, 0], "to": [0.093, -0.09, 0], "ti": [-0.101, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 11, "s": [1178.991, 654.891, 0], "to": [0.101, -0.077, 0], "ti": [-0.108, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [1179.304, 654.679, 0], "to": [0.108, -0.064, 0], "ti": [-0.114, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 13, "s": [1179.637, 654.508, 0], "to": [0.114, -0.05, 0], "ti": [-0.121, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 14, "s": [1179.99, 654.379, 0], "to": [0.121, -0.036, 0], "ti": [-0.126, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 15, "s": [1180.361, 654.294, 0], "to": [0.126, -0.021, 0], "ti": [-0.131, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 16, "s": [1180.748, 654.254, 0], "to": [0.131, -0.006, 0], "ti": [-0.136, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 17, "s": [1181.15, 654.258, 0], "to": [0.136, 0.009, 0], "ti": [-0.14, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 18, "s": [1181.564, 654.308, 0], "to": [0.14, 0.024, 0], "ti": [-0.143, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 19, "s": [1181.989, 654.402, 0], "to": [0.143, 0.039, 0], "ti": [-0.146, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 20, "s": [1182.423, 654.539, 0], "to": [0.146, 0.053, 0], "ti": [-0.148, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 21, "s": [1182.865, 654.719, 0], "to": [0.148, 0.067, 0], "ti": [-0.149, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 22, "s": [1183.311, 654.939, 0], "to": [0.149, 0.08, 0], "ti": [-0.15, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 23, "s": [1183.76, 655.197, 0], "to": [0.15, 0.092, 0], "ti": [-0.15, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 24, "s": [1184.21, 655.491, 0], "to": [0.15, 0.103, 0], "ti": [-0.149, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 25, "s": [1184.66, 655.817, 0], "to": [0.149, 0.114, 0], "ti": [-0.148, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 26, "s": [1185.106, 656.173, 0], "to": [0.148, 0.123, 0], "ti": [-0.146, -0.131, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 27, "s": [1185.548, 656.554, 0], "to": [0.146, 0.131, 0], "ti": [-0.143, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 28, "s": [1185.983, 656.958, 0], "to": [0.143, 0.138, 0], "ti": [-0.14, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 29, "s": [1186.409, 657.379, 0], "to": [0.14, 0.143, 0], "ti": [-0.136, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [1186.824, 657.815, 0], "to": [0.136, 0.147, 0], "ti": [-0.132, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [1187.226, 658.259, 0], "to": [0.132, 0.149, 0], "ti": [-0.127, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 32, "s": [1187.615, 658.709, 0], "to": [0.127, 0.15, 0], "ti": [-0.121, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 33, "s": [1187.987, 659.159, 0], "to": [0.121, 0.149, 0], "ti": [-0.115, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 34, "s": [1188.341, 659.604, 0], "to": [0.115, 0.147, 0], "ti": [-0.108, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 35, "s": [1188.677, 660.042, 0], "to": [0.108, 0.144, 0], "ti": [-0.101, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 36, "s": [1188.991, 660.466, 0], "to": [0.101, 0.139, 0], "ti": [-0.093, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 37, "s": [1189.283, 660.873, 0], "to": [0.093, 0.132, 0], "ti": [-0.085, -0.124, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 38, "s": [1189.552, 661.259, 0], "to": [0.085, 0.124, 0], "ti": [-0.077, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 39, "s": [1189.796, 661.62, 0], "to": [0.077, 0.115, 0], "ti": [-0.068, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 40, "s": [1190.014, 661.952, 0], "to": [0.068, 0.105, 0], "ti": [-0.059, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 41, "s": [1190.205, 662.252, 0], "to": [0.059, 0.094, 0], "ti": [-0.05, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 42, "s": [1190.369, 662.517, 0], "to": [0.05, 0.082, 0], "ti": [-0.04, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 43, "s": [1190.504, 662.744, 0], "to": [0.04, 0.069, 0], "ti": [-0.031, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.192}, "t": 44, "s": [1190.611, 662.931, 0], "to": [0.031, 0.055, 0], "ti": [-0.021, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.205}, "t": 45, "s": [1190.688, 663.076, 0], "to": [0.021, 0.041, 0], "ti": [-0.011, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.238}, "t": 46, "s": [1190.735, 663.178, 0], "to": [0.011, 0.027, 0], "ti": [-0.001, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.725}, "o": {"x": 0.167, "y": 0.299}, "t": 47, "s": [1190.752, 663.236, 0], "to": [0.001, 0.012, 0], "ti": [0.009, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.753}, "o": {"x": 0.167, "y": 0.119}, "t": 48, "s": [1190.74, 663.249, 0], "to": [-0.009, -0.003, 0], "ti": [0.019, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.126}, "t": 49, "s": [1190.697, 663.217, 0], "to": [-0.019, -0.018, 0], "ti": [0.029, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 50, "s": [1190.624, 663.14, 0], "to": [-0.029, -0.033, 0], "ti": [0.039, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 51, "s": [1190.522, 663.019, 0], "to": [-0.039, -0.047, 0], "ti": [0.048, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 52, "s": [1190.391, 662.855, 0], "to": [-0.048, -0.061, 0], "ti": [0.058, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 53, "s": [1190.232, 662.65, 0], "to": [-0.058, -0.075, 0], "ti": [0.067, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 54, "s": [1190.045, 662.407, 0], "to": [-0.067, -0.087, 0], "ti": [0.076, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 55, "s": [1189.831, 662.126, 0], "to": [-0.076, -0.099, 0], "ti": [0.084, 0.11, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 56, "s": [1189.591, 661.812, 0], "to": [-0.084, -0.11, 0], "ti": [0.092, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 57, "s": [1189.326, 661.467, 0], "to": [-0.092, -0.119, 0], "ti": [0.1, 0.128, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 58, "s": [1189.037, 661.095, 0], "to": [-0.1, -0.128, 0], "ti": [0.107, 0.135, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 59, "s": [1188.726, 660.7, 0], "to": [-0.107, -0.135, 0], "ti": [0.114, 0.141, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 60, "s": [1188.394, 660.284, 0], "to": [-0.114, -0.141, 0], "ti": [0.12, 0.145, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 61, "s": [1188.043, 659.854, 0], "to": [-0.12, -0.145, 0], "ti": [0.126, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 62, "s": [1187.673, 659.413, 0], "to": [-0.126, -0.148, 0], "ti": [0.131, 0.15, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 63, "s": [1187.287, 658.964, 0], "to": [-0.131, -0.15, 0], "ti": [0.136, 0.15, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 64, "s": [1186.886, 658.514, 0], "to": [-0.136, -0.15, 0], "ti": [0.14, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 65, "s": [1186.473, 658.066, 0], "to": [-0.14, -0.148, 0], "ti": [0.143, 0.145, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [1186.049, 657.625, 0], "to": [-0.143, -0.145, 0], "ti": [0.146, 0.141, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [1185.615, 657.195, 0], "to": [-0.146, -0.141, 0], "ti": [0.148, 0.135, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 68, "s": [1185.175, 656.781, 0], "to": [-0.148, -0.135, 0], "ti": [0.149, 0.128, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 69, "s": [1184.729, 656.387, 0], "to": [-0.149, -0.128, 0], "ti": [0.15, 0.119, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 70, "s": [1184.28, 656.016, 0], "to": [-0.15, -0.119, 0], "ti": [0.15, 0.109, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 71, "s": [1183.829, 655.672, 0], "to": [-0.15, -0.109, 0], "ti": [0.149, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 72, "s": [1183.38, 655.36, 0], "to": [-0.149, -0.099, 0], "ti": [0.148, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 73, "s": [1182.933, 655.081, 0], "to": [-0.148, -0.087, 0], "ti": [0.146, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 74, "s": [1182.491, 654.839, 0], "to": [-0.146, -0.074, 0], "ti": [0.144, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 75, "s": [1182.055, 654.636, 0], "to": [-0.144, -0.061, 0], "ti": [0.14, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 76, "s": [1181.629, 654.474, 0], "to": [-0.14, -0.047, 0], "ti": [0.137, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 77, "s": [1181.213, 654.356, 0], "to": [-0.137, -0.032, 0], "ti": [0.132, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 78, "s": [1180.809, 654.281, 0], "to": [-0.132, -0.017, 0], "ti": [0.127, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 79, "s": [1180.42, 654.251, 0], "to": [-0.127, -0.003, 0], "ti": [0.122, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 80, "s": [1180.046, 654.266, 0], "to": [-0.122, 0.012, 0], "ti": [0.115, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 81, "s": [1179.69, 654.325, 0], "to": [-0.115, 0.027, 0], "ti": [0.109, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 82, "s": [1179.354, 654.43, 0], "to": [-0.109, 0.042, 0], "ti": [0.102, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 83, "s": [1179.038, 654.577, 0], "to": [-0.102, 0.056, 0], "ti": [0.094, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 84, "s": [1178.744, 654.766, 0], "to": [-0.094, 0.07, 0], "ti": [0.086, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 85, "s": [1178.473, 654.995, 0], "to": [-0.086, 0.083, 0], "ti": [0.078, -0.095, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 86, "s": [1178.227, 655.262, 0], "to": [-0.078, 0.095, 0], "ti": [0.069, -0.106, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 87, "s": [1178.007, 655.563, 0], "to": [-0.069, 0.106, 0], "ti": [0.06, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 88, "s": [1177.814, 655.897, 0], "to": [-0.06, 0.116, 0], "ti": [0.051, -0.125, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 89, "s": [1177.648, 656.259, 0], "to": [-0.051, 0.125, 0], "ti": [0.041, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 90, "s": [1177.51, 656.646, 0], "to": [-0.041, 0.133, 0], "ti": [0.031, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 91, "s": [1177.401, 657.054, 0], "to": [-0.031, 0.139, 0], "ti": [0.021, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 92, "s": [1177.322, 657.479, 0], "to": [-0.021, 0.144, 0], "ti": [0.012, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 93, "s": [1177.272, 657.917, 0], "to": [-0.012, 0.147, 0], "ti": [0.002, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 94, "s": [1177.253, 658.363, 0], "to": [-0.002, 0.149, 0], "ti": [-0.008, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 95, "s": [1177.263, 658.813, 0], "to": [0.008, 0.15, 0], "ti": [-0.018, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [1177.304, 659.262, 0], "to": [0.018, 0.149, 0], "ti": [-0.028, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [1177.374, 659.706, 0], "to": [0.028, 0.146, 0], "ti": [-0.038, -0.143, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 98, "s": [1177.474, 660.141, 0], "to": [0.038, 0.143, 0], "ti": [-0.048, -0.137, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 99, "s": [1177.602, 660.562, 0], "to": [0.048, 0.137, 0], "ti": [-0.057, -0.13, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 100, "s": [1177.76, 660.964, 0], "to": [0.057, 0.13, 0], "ti": [-0.066, -0.122, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 101, "s": [1177.945, 661.345, 0], "to": [0.066, 0.122, 0], "ti": [-0.075, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 102, "s": [1178.157, 661.699, 0], "to": [0.075, 0.113, 0], "ti": [-0.084, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 103, "s": [1178.395, 662.024, 0], "to": [0.084, 0.103, 0], "ti": [-0.092, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 104, "s": [1178.658, 662.316, 0], "to": [0.092, 0.091, 0], "ti": [-0.099, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 105, "s": [1178.945, 662.573, 0], "to": [0.099, 0.079, 0], "ti": [-0.107, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [1179.254, 662.791, 0], "to": [0.107, 0.066, 0], "ti": [-0.113, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [1179.585, 662.968, 0], "to": [0.113, 0.052, 0], "ti": [-0.12, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 108, "s": [1179.935, 663.104, 0], "to": [0.12, 0.038, 0], "ti": [-0.125, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 109, "s": [1180.303, 663.196, 0], "to": [0.125, 0.023, 0], "ti": [-0.131, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 110, "s": [1180.688, 663.243, 0], "to": [0.131, 0.008, 0], "ti": [-0.135, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 111, "s": [1181.087, 663.245, 0], "to": [0.135, -0.007, 0], "ti": [-0.139, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 112, "s": [1181.5, 663.203, 0], "to": [0.139, -0.022, 0], "ti": [-0.143, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 113, "s": [1181.923, 663.116, 0], "to": [0.143, -0.036, 0], "ti": [-0.146, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 114, "s": [1182.356, 662.985, 0], "to": [0.146, -0.051, 0], "ti": [-0.148, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 115, "s": [1182.796, 662.811, 0], "to": [0.148, -0.065, 0], "ti": [-0.149, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 116, "s": [1183.242, 662.598, 0], "to": [0.149, -0.078, 0], "ti": [-0.15, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 117, "s": [1183.691, 662.345, 0], "to": [0.15, -0.09, 0], "ti": [-0.15, 0.102, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 118, "s": [1184.141, 662.057, 0], "to": [0.15, -0.102, 0], "ti": [-0.149, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 119, "s": [1184.591, 661.735, 0], "to": [0.149, -0.112, 0], "ti": [-0.148, 0.122, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 120, "s": [1185.038, 661.384, 0], "to": [0.148, -0.122, 0], "ti": [-0.146, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 121, "s": [1185.481, 661.006, 0], "to": [0.146, -0.13, 0], "ti": [-0.144, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 122, "s": [1185.916, 660.605, 0], "to": [0.144, -0.137, 0], "ti": [-0.141, 0.142, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 123, "s": [1186.344, 660.186, 0], "to": [0.141, -0.142, 0], "ti": [-0.137, 0.146, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [1186.761, 659.753, 0], "to": [0.137, -0.146, 0], "ti": [-0.133, 0.149, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [1187.165, 659.309, 0], "to": [0.133, -0.149, 0], "ti": [-0.128, 0.15, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 126, "s": [1187.556, 658.86, 0], "to": [0.128, -0.15, 0], "ti": [-0.122, 0.149, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 127, "s": [1187.931, 658.41, 0], "to": [0.122, -0.149, 0], "ti": [-0.116, 0.148, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 128, "s": [1188.288, 657.964, 0], "to": [0.116, -0.148, 0], "ti": [-0.109, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 129, "s": [1188.626, 657.525, 0], "to": [0.109, -0.144, 0], "ti": [-0.102, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 130, "s": [1188.944, 657.098, 0], "to": [0.102, -0.139, 0], "ti": [-0.095, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 131, "s": [1189.24, 656.688, 0], "to": [0.095, -0.133, 0], "ti": [-0.087, 0.126, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 132, "s": [1189.512, 656.299, 0], "to": [0.087, -0.126, 0], "ti": [-0.078, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 133, "s": [1189.76, 655.934, 0], "to": [0.078, -0.117, 0], "ti": [-0.07, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 134, "s": [1189.982, 655.597, 0], "to": [0.07, -0.107, 0], "ti": [-0.061, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 135, "s": [1190.177, 655.292, 0], "to": [0.061, -0.096, 0], "ti": [-0.051, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.181}, "t": 136, "s": [1190.345, 655.022, 0], "to": [0.051, -0.084, 0], "ti": [-0.042, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.185}, "t": 137, "s": [1190.485, 654.789, 0], "to": [0.042, -0.071, 0], "ti": [-0.032, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.191}, "t": 138, "s": [1190.596, 654.595, 0], "to": [0.032, -0.058, 0], "ti": [-0.022, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.203}, "t": 139, "s": [1190.678, 654.443, 0], "to": [0.022, -0.043, 0], "ti": [-0.012, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.886}, "o": {"x": 0.167, "y": 0.23}, "t": 140, "s": [1190.73, 654.334, 0], "to": [0.012, -0.029, 0], "ti": [-0.002, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.308}, "t": 141, "s": [1190.752, 654.27, 0], "to": [0.002, -0.014, 0], "ti": [0.008, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.741}, "o": {"x": 0.167, "y": 0.129}, "t": 142, "s": [1190.744, 654.25, 0], "to": [-0.008, 0.001, 0], "ti": [0.018, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.123}, "t": 143, "s": [1190.705, 654.275, 0], "to": [-0.018, 0.016, 0], "ti": [0.028, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.138}, "t": 144, "s": [1190.638, 654.346, 0], "to": [-0.028, 0.031, 0], "ti": [0.037, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.145}, "t": 145, "s": [1190.54, 654.46, 0], "to": [-0.037, 0.045, 0], "ti": [0.047, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.15}, "t": 146, "s": [1190.413, 654.617, 0], "to": [-0.047, 0.059, 0], "ti": [0.056, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 147, "s": [1190.258, 654.816, 0], "to": [-0.056, 0.073, 0], "ti": [0.066, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 148, "s": [1190.075, 655.053, 0], "to": [-0.066, 0.085, 0], "ti": [0.074, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 149, "s": [1189.865, 655.328, 0], "to": [-0.074, 0.097, 0], "ti": [0.083, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 150, "s": [1189.629, 655.638, 0], "to": [-0.083, 0.108, 0], "ti": [0.091, -0.118, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 151, "s": [1189.368, 655.978, 0], "to": [-0.091, 0.118, 0], "ti": [0.099, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 152, "s": [1189.083, 656.346, 0], "to": [-0.099, 0.127, 0], "ti": [0.106, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 153, "s": [1188.775, 656.738, 0], "to": [-0.106, 0.134, 0], "ti": [0.113, -0.14, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 154, "s": [1188.446, 657.151, 0], "to": [-0.113, 0.14, 0], "ti": [0.119, -0.145, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 155, "s": [1188.098, 657.579, 0], "to": [-0.119, 0.145, 0], "ti": [0.125, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 156, "s": [1187.731, 658.019, 0], "to": [-0.125, 0.148, 0], "ti": [0.13, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 157, "s": [1187.347, 658.466, 0], "to": [-0.13, 0.15, 0], "ti": [0.135, -0.15, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 158, "s": [1186.949, 658.917, 0], "to": [-0.135, 0.15, 0], "ti": [0.139, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 159, "s": [1186.537, 659.365, 0], "to": [-0.139, 0.148, 0], "ti": [0.143, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [1186.114, 659.808, 0], "to": [-0.143, 0.146, 0], "ti": [0.145, -0.141, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [1185.682, 660.239, 0], "to": [-0.145, 0.141, 0], "ti": [0.147, -0.136, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 162, "s": [1185.243, 660.656, 0], "to": [-0.147, 0.136, 0], "ti": [0.149, -0.129, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 163, "s": [1184.797, 661.054, 0], "to": [-0.149, 0.129, 0], "ti": [0.15, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 164, "s": [1184.349, 661.429, 0], "to": [-0.15, 0.12, 0], "ti": [0.15, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 165, "s": [1183.898, 661.777, 0], "to": [-0.15, 0.111, 0], "ti": [0.15, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 166, "s": [1183.448, 662.094, 0], "to": [-0.15, 0.1, 0], "ti": [0.148, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 167, "s": [1183.001, 662.379, 0], "to": [-0.148, 0.089, 0], "ti": [0.147, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 168, "s": [1182.558, 662.626, 0], "to": [-0.147, 0.076, 0], "ti": [0.144, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 169, "s": [1182.122, 662.835, 0], "to": [-0.144, 0.063, 0], "ti": [0.141, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 170, "s": [1181.694, 663.003, 0], "to": [-0.141, 0.049, 0], "ti": [0.137, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 171, "s": [1181.276, 663.129, 0], "to": [-0.137, 0.035, 0], "ti": [0.133, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 172, "s": [1180.87, 663.211, 0], "to": [-0.133, 0.02, 0], "ti": [0.128, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 173, "s": [1180.478, 663.248, 0], "to": [-0.128, 0.005, 0], "ti": [0.122, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 174, "s": [1180.102, 663.24, 0], "to": [-0.122, -0.01, 0], "ti": [0.116, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 175, "s": [1179.744, 663.187, 0], "to": [-0.116, -0.025, 0], "ti": [0.11, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 176, "s": [1179.404, 663.089, 0], "to": [-0.11, -0.04, 0], "ti": [0.103, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [1179.085, 662.948, 0], "to": [-0.103, -0.054, 0], "ti": [0.095, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 178, "s": [1178.787, 662.766, 0], "to": [-0.095, -0.068, 0], "ti": [0.087, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 179, "s": [1178.513, 662.543, 0], "to": [-0.087, -0.081, 0], "ti": [0.079, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 180, "s": [1178.263, 662.281, 0], "to": [-0.079, -0.093, 0], "ti": [0.07, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 181, "s": [1178.039, 661.985, 0], "to": [-0.07, -0.104, 0], "ti": [0.061, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 182, "s": [1177.842, 661.656, 0], "to": [-0.061, -0.114, 0], "ti": [0.052, 0.124, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 183, "s": [1177.671, 661.298, 0], "to": [-0.052, -0.124, 0], "ti": [0.043, 0.131, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 184, "s": [1177.529, 660.915, 0], "to": [-0.043, -0.131, 0], "ti": [0.033, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 185, "s": [1177.416, 660.51, 0], "to": [-0.033, -0.138, 0], "ti": [0.023, 0.143, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 186, "s": [1177.332, 660.087, 0], "to": [-0.023, -0.143, 0], "ti": [0.013, 0.147, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 187, "s": [1177.278, 659.651, 0], "to": [-0.013, -0.147, 0], "ti": [0.004, 0.074, 0]}, {"t": 188.000007657397, "s": [1177.254, 659.206, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.561, 114.439, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [92, 92, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.832, 0.02], [0, 0], [-0.016, 7.833], [0, 0], [-7.835, -0.019], [0, 0], [0.02, -7.835]], "o": [[-0.019, 7.834], [0, 0], [-7.836, -0.018], [0, 0], [0.02, -7.835], [0, 0], [7.834, 0.02], [0, 0]], "v": [[34.611, 8.44], [20.39, 22.594], [-20.5, 22.492], [-34.656, 8.27], [-34.613, -8.44], [-20.389, -22.594], [20.5, -22.492], [34.651, -8.268]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [79.129, 195.193], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.836, 0.019], [0, 0], [-0.018, 7.834], [0, 0], [-7.835, -0.02], [0, 0], [0.02, -7.835]], "o": [[-0.02, 7.835], [0, 0], [-7.838, -0.02], [0, 0], [0.019, -7.834], [0, 0], [7.837, 0.021], [0, 0]], "v": [[34.611, 8.441], [20.388, 22.594], [-20.5, 22.494], [-34.654, 8.27], [-34.612, -8.441], [-20.388, -22.593], [20.5, -22.492], [34.653, -8.268]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.863, 146.383], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.177, 0.018], [0, 0], [-0.018, 7.175], [0, 0], [-7.176, -0.016], [0, 0], [0.014, -7.176]], "o": [[-0.016, 7.175], [0, 0], [-7.175, -0.019], [0, 0], [0.018, -7.174], [0, 0], [7.178, 0.017], [0, 0]], "v": [[38.921, 12.904], [25.895, 25.863], [-26.022, 25.736], [-38.984, 12.711], [-38.921, -12.905], [-25.894, -25.866], [26.022, -25.733], [38.987, -12.71]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.251, 87.706], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [6.943, 0.018], [0, 0], [-0.023, 8.839], [0, 0], [-6.942, -0.017], [0, 0], [0.02, -8.838]], "o": [[-0.024, 8.838], [0, 0], [-6.943, -0.017], [0, 0], [0.021, -8.839], [0, 0], [6.939, 0.017], [0, 0]], "v": [[41.329, 9.526], [28.716, 25.498], [-28.841, 25.355], [-41.373, 9.321], [-41.326, -9.526], [-28.715, -25.499], [28.846, -25.356], [41.376, -9.322]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.760999971278, 0.497999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.322, 25.766], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.088, -3.212], [-4.175, -6.569], [7.088, 3.236], [4.181, 6.569]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.133, 66.835], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.086, -3.243], [-4.176, -6.569], [7.086, 3.209], [4.173, 6.569]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [175.533, 160.224], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.531, -6.324], [-0.328, -7.787], [4.531, 6.322], [0.331, 7.787]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [98.67, 46.245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.531, -6.323], [-0.327, -7.785], [4.531, 6.323], [0.327, 7.785]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [144.994, 180.812], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.221, -7.096], [6.57, -4.163], [-3.221, 7.096], [-6.57, 4.166]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.52, 59.827], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.225, -7.096], [6.569, -4.165], [-3.22, 7.096], [-6.569, 4.163]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [75.145, 167.23], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.336, -4.527], [7.777, -0.32], [-6.329, 4.527], [-7.777, 0.347]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [189.113, 90.359], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.334, -4.526], [7.781, -0.352], [-6.337, 4.526], [-7.781, 0.319]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [54.552, 136.698], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 4, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.747, -0.759], [6.9, 3.609], [-7.747, 0.761], [-6.898, -3.609]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [191.677, 127.11], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 4, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.753, -0.761], [6.901, 3.609], [-7.753, 0.759], [-6.904, -3.609]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.987, 99.948], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 4, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.754, 7.736], [-3.596, 6.891], [-0.753, -7.736], [3.596, -6.889]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [108.253, 183.381], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 4, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.752, 7.737], [-3.598, 6.893], [-0.753, -7.737], [3.598, -6.891]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.583999992819, 0.596000043084, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [135.418, 43.681], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 4, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.929, -45.915], [45.897, 8.925], [-8.924, 45.891], [-45.892, -8.925]], "o": [[-8.924, 45.891], [-45.893, -8.923], [8.929, -45.917], [45.897, 8.925]], "v": [[83.095, 16.171], [-16.162, 83.089], [-83.099, -16.146], [16.151, -83.089]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.894000004787, 0.894000004787, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.835, 113.529], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 4, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.47, -53.846], [53.84, 10.468], [-10.466, 53.818], [-53.834, -10.467]], "o": [[-10.466, 53.816], [-53.833, -10.47], [10.469, -53.843], [53.838, 10.47]], "v": [[97.472, 18.967], [-18.959, 97.467], [-97.476, -18.943], [18.948, -97.469]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.835, 113.53], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 4, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.163, 0.231], [0, 0], [0.47, -0.313], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.097, -0.165], [0, 0], [-0.315, 0.107], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.48, -0.313], [0, 0], [-0.164, -0.23], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.224, 0.253], [0, 0], [0.113, 0.166], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[13.95, 7.884], [7.358, -1.886], [7.949, -2.289], [8.887, -2.922], [8.891, -2.92], [10.397, -3.926], [10.4, -3.947], [11.338, -4.579], [12.844, -5.58], [13.188, -5.831], [13.758, -6.819], [8.93, -13.992], [7.783, -13.843], [7.438, -13.619], [5.928, -12.585], [4.984, -11.959], [3.484, -10.953], [2.539, -10.301], [1.032, -9.294], [-0.517, -8.24], [-1.417, -7.634], [-2.361, -7.009], [-3.879, -5.978], [-4.82, -5.354], [-6.332, -4.318], [-7.274, -3.694], [-8.776, -2.687], [-9.72, -2.034], [-11.234, -1.028], [-12.779, 0.026], [-13.677, 0.631], [-13.64, 0.694], [-13.853, 1.378], [-9.02, 8.524], [-8.309, 8.605], [-8.265, 8.642], [-7.873, 8.373], [-7.864, 8.376], [-5.821, 7.009], [-5.573, 6.828], [-3.366, 5.353], [-2.037, 4.457], [4.549, 14.222]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.804000016755, 0.811999990426, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [63.843, 22.862], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 4, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.12, 0.318], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.235, -0.152], [0, 0], [-0.317, -0.47], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.155, 0.114], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.321, -0.469], [0, 0], [-0.241, 0.152], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.259, 0.219], [0, 0], [0.161, -0.114]], "v": [[11.348, 8.292], [11.413, 8.249], [11.074, 7.753], [9.761, 5.793], [8.749, 4.297], [8.108, 3.334], [7.098, 1.841], [6.451, 0.906], [5.443, -0.619], [4.799, -1.551], [3.791, -3.075], [1.496, -6.466], [0.476, -7.963], [-0.159, -8.922], [-1.172, -10.417], [-1.816, -11.351], [-2.822, -12.875], [-3.059, -13.211], [-4.069, -13.782], [-11.227, -8.938], [-11.078, -7.81], [-10.846, -7.448], [-9.832, -5.957], [-9.195, -4.996], [-8.181, -3.5], [-8.172, -3.498], [-7.538, -2.562], [-6.523, -1.044], [-5.886, -0.104], [-5.891, -0.106], [-4.872, 1.384], [-2.579, 4.809], [-1.568, 6.304], [-0.925, 7.239], [-0.928, 7.264], [0.083, 8.761], [0.728, 9.694], [1.738, 11.218], [1.741, 11.19], [2.374, 12.152], [3.395, 13.648], [3.442, 13.629], [4.13, 13.82], [11.29, 9.006]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.234999997008, 0.365000017952, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [210.603, 48.661], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 4, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.841, 8.085], [-8.181, -1.315], [1.848, -8.085], [8.181, 1.321]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.894000004787, 0.894000004787, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [201.868, 54.595], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 4, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-15.409, -21.008], [0, 0], [0, 0], [38.979, 0.097], [0, 0]], "o": [[0, 0], [15.411, 21.004], [0, 0], [0, 0], [-36.476, 27.155], [0, 0]], "v": [[-151.325, -77.614], [48.275, -78.756], [151.324, -55.619], [151.004, 72.865], [47.927, 72.609], [-141.947, 89.494]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.929000016755, 0.698000021542, 0.383999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [223.547, 128.863], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 4, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Time Schedule", "parent": 29, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.824, 254.967, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.943, 255.054, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.118], [-30.118, 30.118], [-30.118, -30.118], [30.118, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [136.943, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Schedule Calendar", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.668}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [964.377, 626.106, 0], "to": [0, -0.008, 0], "ti": [0, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.111}, "t": 1, "s": [964.377, 626.056, 0], "to": [0, -0.033, 0], "ti": [0, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.134}, "t": 2, "s": [964.377, 625.906, 0], "to": [0, -0.066, 0], "ti": [0, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.143}, "t": 3, "s": [964.377, 625.659, 0], "to": [0, -0.099, 0], "ti": [0, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 4, "s": [964.377, 625.315, 0], "to": [0, -0.13, 0], "ti": [0, 0.16, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 5, "s": [964.377, 624.88, 0], "to": [0, -0.16, 0], "ti": [0, 0.188, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 6, "s": [964.377, 624.356, 0], "to": [0, -0.188, 0], "ti": [0, 0.215, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 7, "s": [964.377, 623.75, 0], "to": [0, -0.215, 0], "ti": [0, 0.239, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 8, "s": [964.377, 623.067, 0], "to": [0, -0.239, 0], "ti": [0, 0.261, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 9, "s": [964.377, 622.315, 0], "to": [0, -0.261, 0], "ti": [0, 0.28, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 10, "s": [964.377, 621.501, 0], "to": [0, -0.28, 0], "ti": [0, 0.297, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 11, "s": [964.377, 620.632, 0], "to": [0, -0.297, 0], "ti": [0, 0.311, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 12, "s": [964.377, 619.719, 0], "to": [0, -0.311, 0], "ti": [0, 0.321, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 13, "s": [964.377, 618.769, 0], "to": [0, -0.321, 0], "ti": [0, 0.328, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 14, "s": [964.377, 617.792, 0], "to": [0, -0.328, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 15, "s": [964.377, 616.799, 0], "to": [0, -0.332, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [964.377, 615.798, 0], "to": [0, -0.333, 0], "ti": [0, 0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 17, "s": [964.377, 614.801, 0], "to": [0, -0.33, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 18, "s": [964.377, 613.817, 0], "to": [0, -0.324, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 19, "s": [964.377, 612.855, 0], "to": [0, -0.315, 0], "ti": [0, 0.303, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 20, "s": [964.377, 611.926, 0], "to": [0, -0.303, 0], "ti": [0, 0.287, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 21, "s": [964.377, 611.04, 0], "to": [0, -0.287, 0], "ti": [0, 0.269, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 22, "s": [964.377, 610.203, 0], "to": [0, -0.269, 0], "ti": [0, 0.248, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 23, "s": [964.377, 609.426, 0], "to": [0, -0.248, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 24, "s": [964.377, 608.716, 0], "to": [0, -0.224, 0], "ti": [0, 0.199, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 25, "s": [964.377, 608.08, 0], "to": [0, -0.199, 0], "ti": [0, 0.171, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 26, "s": [964.377, 607.524, 0], "to": [0, -0.171, 0], "ti": [0, 0.142, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.187}, "t": 27, "s": [964.377, 607.054, 0], "to": [0, -0.142, 0], "ti": [0, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.194}, "t": 28, "s": [964.377, 606.675, 0], "to": [0, -0.111, 0], "ti": [0, 0.079, 0]}, {"i": {"x": 0.833, "y": 0.877}, "o": {"x": 0.167, "y": 0.21}, "t": 29, "s": [964.377, 606.39, 0], "to": [0, -0.079, 0], "ti": [0, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.26}, "t": 30, "s": [964.377, 606.202, 0], "to": [0, -0.046, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.357}, "t": 31, "s": [964.377, 606.114, 0], "to": [0, -0.013, 0], "ti": [0, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.759}, "o": {"x": 0.167, "y": 0.103}, "t": 32, "s": [964.377, 606.125, 0], "to": [0, 0.021, 0], "ti": [0, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.128}, "t": 33, "s": [964.377, 606.237, 0], "to": [0, 0.054, 0], "ti": [0, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.14}, "t": 34, "s": [964.377, 606.447, 0], "to": [0, 0.086, 0], "ti": [0, -0.118, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.147}, "t": 35, "s": [964.377, 606.754, 0], "to": [0, 0.118, 0], "ti": [0, -0.148, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 36, "s": [964.377, 607.155, 0], "to": [0, 0.148, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 37, "s": [964.377, 607.645, 0], "to": [0, 0.178, 0], "ti": [0, -0.205, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 38, "s": [964.377, 608.22, 0], "to": [0, 0.205, 0], "ti": [0, -0.23, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 39, "s": [964.377, 608.874, 0], "to": [0, 0.23, 0], "ti": [0, -0.253, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 40, "s": [964.377, 609.6, 0], "to": [0, 0.253, 0], "ti": [0, -0.273, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 41, "s": [964.377, 610.392, 0], "to": [0, 0.273, 0], "ti": [0, -0.291, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 42, "s": [964.377, 611.24, 0], "to": [0, 0.291, 0], "ti": [0, -0.306, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 43, "s": [964.377, 612.138, 0], "to": [0, 0.306, 0], "ti": [0, -0.317, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 44, "s": [964.377, 613.075, 0], "to": [0, 0.317, 0], "ti": [0, -0.326, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 45, "s": [964.377, 614.042, 0], "to": [0, 0.326, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 46, "s": [964.377, 615.03, 0], "to": [0, 0.331, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [964.377, 616.029, 0], "to": [0, 0.333, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [964.377, 617.029, 0], "to": [0, 0.332, 0], "ti": [0, -0.327, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 49, "s": [964.377, 618.019, 0], "to": [0, 0.327, 0], "ti": [0, -0.319, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 50, "s": [964.377, 618.991, 0], "to": [0, 0.319, 0], "ti": [0, -0.308, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 51, "s": [964.377, 619.933, 0], "to": [0, 0.308, 0], "ti": [0, -0.293, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 52, "s": [964.377, 620.837, 0], "to": [0, 0.293, 0], "ti": [0, -0.276, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 53, "s": [964.377, 621.694, 0], "to": [0, 0.276, 0], "ti": [0, -0.256, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 54, "s": [964.377, 622.495, 0], "to": [0, 0.256, 0], "ti": [0, -0.234, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 55, "s": [964.377, 623.232, 0], "to": [0, 0.234, 0], "ti": [0, -0.209, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 56, "s": [964.377, 623.897, 0], "to": [0, 0.209, 0], "ti": [0, -0.182, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.181}, "t": 57, "s": [964.377, 624.485, 0], "to": [0, 0.182, 0], "ti": [0, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.185}, "t": 58, "s": [964.377, 624.988, 0], "to": [0, 0.153, 0], "ti": [0, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.191}, "t": 59, "s": [964.377, 625.403, 0], "to": [0, 0.123, 0], "ti": [0, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.202}, "t": 60, "s": [964.377, 625.724, 0], "to": [0, 0.091, 0], "ti": [0, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.231}, "t": 61, "s": [964.377, 625.95, 0], "to": [0, 0.059, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.731}, "o": {"x": 0.167, "y": 0.406}, "t": 62, "s": [964.377, 626.077, 0], "to": [0, 0.026, 0], "ti": [0, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.72}, "o": {"x": 0.167, "y": 0.12}, "t": 63, "s": [964.377, 626.103, 0], "to": [0, -0.008, 0], "ti": [0, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.119}, "t": 64, "s": [964.377, 626.03, 0], "to": [0, -0.041, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.137}, "t": 65, "s": [964.377, 625.858, 0], "to": [0, -0.074, 0], "ti": [0, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 66, "s": [964.377, 625.588, 0], "to": [0, -0.106, 0], "ti": [0, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.15}, "t": 67, "s": [964.377, 625.223, 0], "to": [0, -0.137, 0], "ti": [0, 0.167, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 68, "s": [964.377, 624.766, 0], "to": [0, -0.167, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 69, "s": [964.377, 624.223, 0], "to": [0, -0.195, 0], "ti": [0, 0.221, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 70, "s": [964.377, 623.599, 0], "to": [0, -0.221, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 71, "s": [964.377, 622.9, 0], "to": [0, -0.244, 0], "ti": [0, 0.266, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 72, "s": [964.377, 622.132, 0], "to": [0, -0.266, 0], "ti": [0, 0.285, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 73, "s": [964.377, 621.305, 0], "to": [0, -0.285, 0], "ti": [0, 0.3, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 74, "s": [964.377, 620.425, 0], "to": [0, -0.3, 0], "ti": [0, 0.313, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 75, "s": [964.377, 619.502, 0], "to": [0, -0.313, 0], "ti": [0, 0.323, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 76, "s": [964.377, 618.545, 0], "to": [0, -0.323, 0], "ti": [0, 0.33, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 77, "s": [964.377, 617.564, 0], "to": [0, -0.33, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 78, "s": [964.377, 616.568, 0], "to": [0, -0.333, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [964.377, 615.567, 0], "to": [0, -0.333, 0], "ti": [0, 0.329, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 80, "s": [964.377, 614.572, 0], "to": [0, -0.329, 0], "ti": [0, 0.322, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 81, "s": [964.377, 613.592, 0], "to": [0, -0.322, 0], "ti": [0, 0.312, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 82, "s": [964.377, 612.638, 0], "to": [0, -0.312, 0], "ti": [0, 0.299, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 83, "s": [964.377, 611.718, 0], "to": [0, -0.299, 0], "ti": [0, 0.283, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 84, "s": [964.377, 610.842, 0], "to": [0, -0.283, 0], "ti": [0, 0.264, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 85, "s": [964.377, 610.018, 0], "to": [0, -0.264, 0], "ti": [0, 0.243, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 86, "s": [964.377, 609.256, 0], "to": [0, -0.243, 0], "ti": [0, 0.219, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 87, "s": [964.377, 608.562, 0], "to": [0, -0.219, 0], "ti": [0, 0.192, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 88, "s": [964.377, 607.944, 0], "to": [0, -0.192, 0], "ti": [0, 0.164, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.183}, "t": 89, "s": [964.377, 607.408, 0], "to": [0, -0.164, 0], "ti": [0, 0.135, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 90, "s": [964.377, 606.958, 0], "to": [0, -0.135, 0], "ti": [0, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.197}, "t": 91, "s": [964.377, 606.6, 0], "to": [0, -0.103, 0], "ti": [0, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.216}, "t": 92, "s": [964.377, 606.338, 0], "to": [0, -0.071, 0], "ti": [0, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.293}, "t": 93, "s": [964.377, 606.173, 0], "to": [0, -0.038, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.613}, "o": {"x": 0.167, "y": 0.231}, "t": 94, "s": [964.377, 606.107, 0], "to": [0, -0.005, 0], "ti": [0, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.106}, "t": 95, "s": [964.377, 606.142, 0], "to": [0, 0.028, 0], "ti": [0, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.131}, "t": 96, "s": [964.377, 606.276, 0], "to": [0, 0.061, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 97, "s": [964.377, 606.509, 0], "to": [0, 0.094, 0], "ti": [0, -0.125, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 98, "s": [964.377, 606.838, 0], "to": [0, 0.125, 0], "ti": [0, -0.155, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 99, "s": [964.377, 607.26, 0], "to": [0, 0.155, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 100, "s": [964.377, 607.77, 0], "to": [0, 0.184, 0], "ti": [0, -0.211, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 101, "s": [964.377, 608.364, 0], "to": [0, 0.211, 0], "ti": [0, -0.236, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 102, "s": [964.377, 609.035, 0], "to": [0, 0.236, 0], "ti": [0, -0.258, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 103, "s": [964.377, 609.777, 0], "to": [0, 0.258, 0], "ti": [0, -0.278, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 104, "s": [964.377, 610.583, 0], "to": [0, 0.278, 0], "ti": [0, -0.295, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 105, "s": [964.377, 611.444, 0], "to": [0, 0.295, 0], "ti": [0, -0.309, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 106, "s": [964.377, 612.351, 0], "to": [0, 0.309, 0], "ti": [0, -0.32, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 107, "s": [964.377, 613.296, 0], "to": [0, 0.32, 0], "ti": [0, -0.327, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 108, "s": [964.377, 614.269, 0], "to": [0, 0.327, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 109, "s": [964.377, 615.261, 0], "to": [0, 0.332, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [964.377, 616.261, 0], "to": [0, 0.333, 0], "ti": [0, -0.331, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 111, "s": [964.377, 617.259, 0], "to": [0, 0.331, 0], "ti": [0, -0.325, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 112, "s": [964.377, 618.246, 0], "to": [0, 0.325, 0], "ti": [0, -0.317, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 113, "s": [964.377, 619.211, 0], "to": [0, 0.317, 0], "ti": [0, -0.305, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 114, "s": [964.377, 620.146, 0], "to": [0, 0.305, 0], "ti": [0, -0.29, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 115, "s": [964.377, 621.04, 0], "to": [0, 0.29, 0], "ti": [0, -0.272, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 116, "s": [964.377, 621.884, 0], "to": [0, 0.272, 0], "ti": [0, -0.251, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 117, "s": [964.377, 622.671, 0], "to": [0, 0.251, 0], "ti": [0, -0.228, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 118, "s": [964.377, 623.392, 0], "to": [0, 0.228, 0], "ti": [0, -0.203, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 119, "s": [964.377, 624.04, 0], "to": [0, 0.203, 0], "ti": [0, -0.175, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 120, "s": [964.377, 624.609, 0], "to": [0, 0.175, 0], "ti": [0, -0.146, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 121, "s": [964.377, 625.092, 0], "to": [0, 0.146, 0], "ti": [0, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.193}, "t": 122, "s": [964.377, 625.486, 0], "to": [0, 0.116, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.207}, "t": 123, "s": [964.377, 625.785, 0], "to": [0, 0.084, 0], "ti": [0, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.246}, "t": 124, "s": [964.377, 625.988, 0], "to": [0, 0.051, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.593}, "o": {"x": 0.167, "y": 0.432}, "t": 125, "s": [964.377, 626.092, 0], "to": [0, 0.018, 0], "ti": [0, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.748}, "o": {"x": 0.167, "y": 0.105}, "t": 126, "s": [964.377, 626.095, 0], "to": [0, -0.015, 0], "ti": [0, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.124}, "t": 127, "s": [964.377, 625.999, 0], "to": [0, -0.049, 0], "ti": [0, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 128, "s": [964.377, 625.804, 0], "to": [0, -0.081, 0], "ti": [0, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 129, "s": [964.377, 625.512, 0], "to": [0, -0.113, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 130, "s": [964.377, 625.125, 0], "to": [0, -0.144, 0], "ti": [0, 0.173, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 131, "s": [964.377, 624.648, 0], "to": [0, -0.173, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 132, "s": [964.377, 624.086, 0], "to": [0, -0.201, 0], "ti": [0, 0.226, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 133, "s": [964.377, 623.444, 0], "to": [0, -0.226, 0], "ti": [0, 0.25, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 134, "s": [964.377, 622.728, 0], "to": [0, -0.25, 0], "ti": [0, 0.27, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 135, "s": [964.377, 621.946, 0], "to": [0, -0.27, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 136, "s": [964.377, 621.106, 0], "to": [0, -0.288, 0], "ti": [0, 0.304, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 137, "s": [964.377, 620.215, 0], "to": [0, -0.304, 0], "ti": [0, 0.316, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 138, "s": [964.377, 619.284, 0], "to": [0, -0.316, 0], "ti": [0, 0.325, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 139, "s": [964.377, 618.32, 0], "to": [0, -0.325, 0], "ti": [0, 0.331, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 140, "s": [964.377, 617.335, 0], "to": [0, -0.331, 0], "ti": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 141, "s": [964.377, 616.337, 0], "to": [0, -0.333, 0], "ti": [0, 0.332, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [964.377, 615.336, 0], "to": [0, -0.332, 0], "ti": [0, 0.328, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 143, "s": [964.377, 614.344, 0], "to": [0, -0.328, 0], "ti": [0, 0.32, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 144, "s": [964.377, 613.369, 0], "to": [0, -0.32, 0], "ti": [0, 0.31, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 145, "s": [964.377, 612.422, 0], "to": [0, -0.31, 0], "ti": [0, 0.296, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 146, "s": [964.377, 611.511, 0], "to": [0, -0.296, 0], "ti": [0, 0.279, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 147, "s": [964.377, 610.646, 0], "to": [0, -0.279, 0], "ti": [0, 0.26, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 148, "s": [964.377, 609.837, 0], "to": [0, -0.26, 0], "ti": [0, 0.237, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 149, "s": [964.377, 609.089, 0], "to": [0, -0.237, 0], "ti": [0, 0.213, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 150, "s": [964.377, 608.412, 0], "to": [0, -0.213, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 151, "s": [964.377, 607.813, 0], "to": [0, -0.186, 0], "ti": [0, 0.158, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.184}, "t": 152, "s": [964.377, 607.296, 0], "to": [0, -0.158, 0], "ti": [0, 0.127, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.19}, "t": 153, "s": [964.377, 606.867, 0], "to": [0, -0.127, 0], "ti": [0, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.2}, "t": 154, "s": [964.377, 606.531, 0], "to": [0, -0.096, 0], "ti": [0, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.891}, "o": {"x": 0.167, "y": 0.224}, "t": 155, "s": [964.377, 606.291, 0], "to": [0, -0.064, 0], "ti": [0, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.358}, "t": 156, "s": [964.377, 606.149, 0], "to": [0, -0.031, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.69}, "o": {"x": 0.167, "y": 0.145}, "t": 157, "s": [964.377, 606.106, 0], "to": [0, 0.003, 0], "ti": [0, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.114}, "t": 158, "s": [964.377, 606.164, 0], "to": [0, 0.036, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.135}, "t": 159, "s": [964.377, 606.322, 0], "to": [0, 0.069, 0], "ti": [0, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 160, "s": [964.377, 606.577, 0], "to": [0, 0.101, 0], "ti": [0, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 161, "s": [964.377, 606.928, 0], "to": [0, 0.132, 0], "ti": [0, -0.162, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 162, "s": [964.377, 607.37, 0], "to": [0, 0.162, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 163, "s": [964.377, 607.9, 0], "to": [0, 0.19, 0], "ti": [0, -0.217, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 164, "s": [964.377, 608.512, 0], "to": [0, 0.217, 0], "ti": [0, -0.241, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 165, "s": [964.377, 609.201, 0], "to": [0, 0.241, 0], "ti": [0, -0.263, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 166, "s": [964.377, 609.958, 0], "to": [0, 0.263, 0], "ti": [0, -0.282, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 167, "s": [964.377, 610.777, 0], "to": [0, 0.282, 0], "ti": [0, -0.298, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 168, "s": [964.377, 611.649, 0], "to": [0, 0.298, 0], "ti": [0, -0.312, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 169, "s": [964.377, 612.566, 0], "to": [0, 0.312, 0], "ti": [0, -0.322, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 170, "s": [964.377, 613.519, 0], "to": [0, 0.322, 0], "ti": [0, -0.329, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 171, "s": [964.377, 614.497, 0], "to": [0, 0.329, 0], "ti": [0, -0.332, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 172, "s": [964.377, 615.491, 0], "to": [0, 0.332, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [964.377, 616.492, 0], "to": [0, 0.333, 0], "ti": [0, -0.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 174, "s": [964.377, 617.488, 0], "to": [0, 0.33, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 175, "s": [964.377, 618.471, 0], "to": [0, 0.324, 0], "ti": [0, -0.314, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 176, "s": [964.377, 619.43, 0], "to": [0, 0.314, 0], "ti": [0, -0.302, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 177, "s": [964.377, 620.356, 0], "to": [0, 0.302, 0], "ti": [0, -0.286, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 178, "s": [964.377, 621.239, 0], "to": [0, 0.286, 0], "ti": [0, -0.267, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 179, "s": [964.377, 622.071, 0], "to": [0, 0.267, 0], "ti": [0, -0.246, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 180, "s": [964.377, 622.844, 0], "to": [0, 0.246, 0], "ti": [0, -0.222, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 181, "s": [964.377, 623.548, 0], "to": [0, 0.222, 0], "ti": [0, -0.197, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 182, "s": [964.377, 624.179, 0], "to": [0, 0.197, 0], "ti": [0, -0.169, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.182}, "t": 183, "s": [964.377, 624.728, 0], "to": [0, 0.169, 0], "ti": [0, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.187}, "t": 184, "s": [964.377, 625.191, 0], "to": [0, 0.139, 0], "ti": [0, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.195}, "t": 185, "s": [964.377, 625.563, 0], "to": [0, 0.108, 0], "ti": [0, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.212}, "t": 186, "s": [964.377, 625.841, 0], "to": [0, 0.076, 0], "ti": [0, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.269}, "t": 187, "s": [964.377, 626.021, 0], "to": [0, 0.043, 0], "ti": [0, -0.013, 0]}, {"t": 188.000007657397, "s": [964.377, 626.101, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [204.312, 216.768, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [338.945, 322.586], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [271.883, 322.586], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.116, 30.117], [-30.116, 30.117], [-30.116, -30.117], [30.116, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.823, 322.586], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [137.761, 322.586], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [70.699, 322.586], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.118], [-30.118, 30.118], [-30.118, -30.118], [30.118, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [338.945, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.118], [-30.118, 30.118], [-30.118, -30.118], [30.118, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [271.883, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.116, 30.118], [-30.116, 30.118], [-30.116, -30.118], [30.116, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.823, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.118], [-30.118, 30.118], [-30.118, -30.118], [30.118, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.003921568859, 0.72549021244, 0.678431391716, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [136.943, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.118], [-30.118, 30.118], [-30.118, -30.118], [30.118, -30.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [70.699, 255.053], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [338.945, 187.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [271.883, 187.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 4, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.116, 30.117], [-30.116, 30.117], [-30.116, -30.117], [30.116, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.823, 187.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 4, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [137.761, 187.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 4, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.118, 30.117], [-30.118, 30.117], [-30.118, -30.117], [30.118, -30.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.004000000393, 0.725, 0.677999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [70.699, 187.521], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 4, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[204.062, 4.399], [-204.061, 4.399], [-204.061, -4.399], [204.062, -4.399]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.894000004787, 0.902000038297, 0.902000038297, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.312, 116.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 4, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.619, 0], [0, 0], [0, 4.619], [0, 0], [-4.618, 0], [0, 0], [0, -4.618]], "o": [[0, 4.619], [0, 0], [-4.618, 0], [0, 0], [0, -4.618], [0, 0], [4.619, 0], [0, 0]], "v": [[9.618, 30.587], [1.254, 38.951], [-1.255, 38.951], [-9.618, 30.587], [-9.618, -30.588], [-1.255, -38.951], [1.254, -38.951], [9.618, -30.588]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.255000005984, 0.250999989229, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [355.679, 39.201], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 4, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -10.623], [10.623, 0], [0, 10.623], [-10.624, 0]], "o": [[0, 10.623], [-10.624, 0], [0, -10.623], [10.623, 0]], "v": [[19.235, -0.001], [0, 19.236], [-19.235, -0.001], [0, -19.236]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [355.679, 66.319], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 4, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.619, 0], [0, 0], [0, 4.619], [0, 0], [-4.618, 0], [0, 0], [0, -4.618]], "o": [[0, 4.619], [0, 0], [-4.618, 0], [0, 0], [0, -4.618], [0, 0], [4.619, 0], [0, 0]], "v": [[9.618, 30.587], [1.255, 38.951], [-1.256, 38.951], [-9.618, 30.587], [-9.618, -30.588], [-1.256, -38.951], [1.255, -38.951], [9.618, -30.588]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.255000005984, 0.250999989229, 0.258999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.647, 39.201], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 4, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -10.623], [10.623, 0], [0, 10.623], [-10.624, 0]], "o": [[0, 10.623], [-10.624, 0], [0, -10.623], [10.623, 0]], "v": [[19.236, -0.001], [0, 19.236], [-19.236, -0.001], [0, -19.236]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.647, 66.319], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 4, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.619, 0], [0, 0], [0, -4.619], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-4.619, 0], [0, 0], [0, 0], [0, 0], [0, -4.619]], "v": [[195.698, -46.267], [-195.698, -46.267], [-204.061, -37.903], [-204.061, 46.267], [204.061, 46.267], [204.061, -37.903]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941000007181, 0.317999985639, 0.380000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.312, 67.324], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 4, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.618, 0], [0, 0], [0, 4.618], [0, 0], [-4.618, 0], [0, 0], [0, -4.62]], "o": [[0, 4.618], [0, 0], [-4.618, 0], [0, 0], [0, -4.62], [0, 0], [4.618, 0], [0, 0]], "v": [[204.062, 197.751], [195.698, 206.115], [-195.699, 206.115], [-204.061, 197.751], [-204.061, -197.75], [-195.699, -206.114], [195.698, -206.114], [204.062, -197.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.948999980852, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.311, 227.172], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 4, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 189.000007698128, "st": 0, "bm": 0}], "markers": []}