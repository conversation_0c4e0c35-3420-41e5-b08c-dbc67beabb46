import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { dispenseMedication, MedicationDispensingCreateParams } from "@/services/medicationDispensing";
import { getInventoryItems, InventoryItem } from "@/services/inventory";
import { format } from "date-fns";

interface DispenseMedicationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  healthRecordId: string;
  petId: string;
  clinicId: string;
  onSuccess?: () => void;
}

const DispenseMedicationModal = ({
  open,
  onOpenChange,
  healthRecordId,
  petId,
  clinicId,
  onSuccess,
}: DispenseMedicationModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<MedicationDispensingCreateParams>({
    healthRecordId,
    inventoryItemId: '',
    petId,
    clinicId,
    quantity: 1,
    unit: '',
    dosage: '',
    frequency: '',
    duration: '',
    price: 0,
    instructions: '',
    notes: '',
  });

  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [medicationOnly, setMedicationOnly] = useState(true);

  // Fetch inventory items
  const { data: inventoryData } = useQuery({
    queryKey: ['inventory', searchQuery, medicationOnly],
    queryFn: () => getInventoryItems({
      search: searchQuery || undefined,
      isMedication: medicationOnly ? true : undefined,
      status: 'active',
      limit: 100,
      sortBy: "name",
      sortOrder: "asc"
    }),
    enabled: open
  });

  const inventoryItems = inventoryData?.data?.data || [];

  // Dispense medication mutation
  const dispenseMutation = useMutation({
    mutationFn: (data: MedicationDispensingCreateParams) => dispenseMedication(data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Medication dispensed successfully",
      });
      onSuccess?.();
      onOpenChange(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to dispense medication",
        variant: "destructive",
      });
    },
  });

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  // Update form when selected item changes
  useEffect(() => {
    if (selectedItem) {
      setFormData(prev => ({
        ...prev,
        inventoryItemId: selectedItem._id,
        unit: selectedItem.unit,
        price: selectedItem.sellingPrice,
        currency: selectedItem.currency,
      }));
    }
  }, [selectedItem]);

  const resetForm = () => {
    setFormData({
      healthRecordId,
      inventoryItemId: '',
      petId,
      clinicId,
      quantity: 1,
      unit: '',
      dosage: '',
      frequency: '',
      duration: '',
      price: 0,
      instructions: '',
      notes: '',
    });
    setSelectedItem(null);
    setSearchQuery('');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: parseFloat(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    if (name === 'inventoryItemId') {
      const item = inventoryItems.find(item => item.inventoryId === value);
      if (item) {
        setSelectedItem(item);
      }
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.inventoryItemId) {
      toast({
        title: "Error",
        description: "Please select a medication",
        variant: "destructive",
      });
      return;
    }

    if (formData.quantity <= 0) {
      toast({
        title: "Error",
        description: "Quantity must be greater than zero",
        variant: "destructive",
      });
      return;
    }

    if (!formData.dosage) {
      toast({
        title: "Error",
        description: "Please enter dosage information",
        variant: "destructive",
      });
      return;
    }

    if (!formData.frequency) {
      toast({
        title: "Error",
        description: "Please enter frequency information",
        variant: "destructive",
      });
      return;
    }

    // Check if there's enough stock
    if (selectedItem && formData.quantity > selectedItem.quantity) {
      toast({
        title: "Error",
        description: "Insufficient quantity in stock",
        variant: "destructive",
      });
      return;
    }

    dispenseMutation.mutate(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Dispense Medication</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="inventoryItemId">Medication</Label>
              <Select
                value={formData.inventoryItemId}
                onValueChange={(value) => handleSelectChange('inventoryItemId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select medication" />
                </SelectTrigger>
                <SelectContent>
                  {inventoryItems.map((item) => (
                    <SelectItem key={item.inventoryId} value={item.inventoryId?.toString() || ""}>
                      {item.name} ({item.quantity} {item.unit} available)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  placeholder="Search medications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="text-sm"
                />
                <div className="flex items-center space-x-1">
                  <input
                    type="checkbox"
                    id="medicationOnly"
                    checked={medicationOnly}
                    onChange={(e) => setMedicationOnly(e.target.checked)}
                  />
                  <Label htmlFor="medicationOnly" className="text-xs">Medications only</Label>
                </div>
              </div>
            </div>

            {selectedItem && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={formData.quantity || ''}
                      onChange={handleChange}
                      required
                    />
                    <span className="text-sm text-muted-foreground">{selectedItem.unit}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Available: {selectedItem.quantity} {selectedItem.unit}
                    {selectedItem.expiryDate && ` (Expires: ${format(new Date(selectedItem.expiryDate), 'dd/MM/yyyy')})`}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">Price</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.price || ''}
                      onChange={handleChange}
                      required
                    />
                    <span className="text-sm text-muted-foreground">{selectedItem.currency}</span>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dosage">Dosage</Label>
                <Input
                  id="dosage"
                  name="dosage"
                  value={formData.dosage}
                  onChange={handleChange}
                  placeholder="E.g., 1 tablet"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Input
                  id="frequency"
                  name="frequency"
                  value={formData.frequency}
                  onChange={handleChange}
                  placeholder="E.g., Twice daily"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  name="duration"
                  value={formData.duration || ''}
                  onChange={handleChange}
                  placeholder="E.g., 7 days"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="instructions">Instructions</Label>
              <Textarea
                id="instructions"
                name="instructions"
                value={formData.instructions || ''}
                onChange={handleChange}
                placeholder="E.g., Take with food"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                placeholder="Any additional notes"
                rows={2}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={dispenseMutation.isPending}>
              {dispenseMutation.isPending ? "Dispensing..." : "Dispense Medication"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DispenseMedicationModal;
