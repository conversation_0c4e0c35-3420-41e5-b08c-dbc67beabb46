
import { <PERSON>, Stethoscope, UserPlus, Shield, Star, Sparkles } from "lucide-react";
import { motion } from "framer-motion";
import FlipCarousel from "./FlipCarousel";
import LottieAnimation from "./LottieAnimation";
import TriangleDecoration from "./TriangleDecoration";

const testimonials = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    role: "Veterinarian",
    content: "VetCare has completely transformed how we operate. Patient records are now accessible instantly, and the scheduling system has reduced our no-shows by 40%.",
    avatar: "/pets/cat.jpg"
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Clinic Manager",
    content: "The analytics dashboard gives me insights we never had before. We've been able to optimize staffing and improve our service offerings based on real data.",
    avatar: "/pets/dog.jpg"
  },
  {
    id: 3,
    name: "Dr. <PERSON>",
    role: "Practice Owner",
    content: "Our clients love the pet health portal. Being able to check vaccination schedules and test results online has greatly improved our client satisfaction.",
    avatar: "/pets/parrot.jpg"
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Veterinary Technician",
    content: "The medication tracking system has eliminated errors and made our workflow much more efficient. I can't imagine going back to our old system.",
    avatar: "/pets/horse.jpg"
  }
];

const features = [
  {
    icon: Clock,
    title: "24/7 Support",
    description: "Round-the-clock technical support for your practice",
    animation: "/animation/lottie/stethoscope.json"
  },
  {
    icon: Stethoscope,
    title: "Expert Care",
    description: "Built by veterinarians for veterinarians",
    animation: "/animation/lottie/stethoscope.json"
  },
  {
    icon: UserPlus,
    title: "Client Satisfaction",
    description: "Tools to improve client engagement and loyalty",
    animation: "/animation/lottie/cat.json"
  },
  {
    icon: Shield,
    title: "Data Security",
    description: "HIPAA-compliant, secure data storage",
    animation: "/animation/lottie/pill.json"
  },
  {
    icon: Star,
    title: "Quality Assurance",
    description: "Regular updates with new features and improvements",
    animation: "/animation/lottie/dog.json"
  },
  {
    icon: Sparkles,
    title: "Customization",
    description: "Tailor the platform to your practice's unique needs",
    animation: "/animation/lottie/bird.json"
  }
];

const WhyChooseUsSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="why-choose-us" className="py-20 bg-gradient-to-r from-primary-600 to-primary-700 text-white scroll-mt-20 relative overflow-hidden">
      {/* Background decorations */}
      <TriangleDecoration top="5%" left="2%" size={80} color="bg-white/5" rotate={15} delay={0.2} />
      <TriangleDecoration top="10%" right="5%" size={60} color="bg-white/5" rotate={45} delay={0.3} />
      <TriangleDecoration bottom="15%" left="10%" size={70} color="bg-white/5" rotate={-30} delay={0.4} />

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose VetCare?</h2>
          <p className="text-lg text-white/90 max-w-3xl mx-auto">
            Join thousands of veterinary practices that trust VetCare to streamline their operations
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 items-start">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
          >
            <div className="grid grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex flex-col items-center md:items-start text-center md:text-left relative overflow-hidden bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10"
                  variants={itemVariants}
                >
                  {/* Background Lottie animation */}
                  <div className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none">
                    <LottieAnimation
                      animationPath={feature.animation}
                      className="w-full h-full"
                      speed={0.5}
                    />
                  </div>

                  <div className="bg-white/10 p-3 rounded-full mb-4 relative z-10">
                    <feature.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 relative z-10">{feature.title}</h3>
                  <p className="text-white/80 relative z-10">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            className="bg-gradient-to-br from-primary-700/80 to-primary-900/80 backdrop-blur-sm rounded-2xl p-6 border border-white/10 shadow-xl relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Background Lottie animations */}
            <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
              <LottieAnimation
                animationPath="/animation/lottie/stethoscope.json"
                className="w-full h-full"
                speed={0.3}
              />
            </div>

            {/* Corner animations */}
            <div className="absolute top-0 right-0 w-1/3 h-1/3 opacity-10 pointer-events-none">
              <LottieAnimation
                animationPath="/animation/lottie/cat.json"
                className="w-full h-full"
                speed={0.5}
              />
            </div>

            <div className="absolute bottom-0 left-0 w-1/3 h-1/3 opacity-10 pointer-events-none">
              <LottieAnimation
                animationPath="/animation/lottie/dog.json"
                className="w-full h-full"
                speed={0.5}
              />
            </div>

            <h3 className="text-2xl font-bold mb-8 text-center bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80 relative z-10">
              What Our Clients Say
            </h3>
            <div className="h-[300px] relative perspective-1000 z-10">
              <FlipCarousel testimonials={testimonials} autoPlayInterval={6000} />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;
