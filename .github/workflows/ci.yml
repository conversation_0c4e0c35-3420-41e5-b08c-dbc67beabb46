name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: 'vet-care-systems/package-lock.json'

    - name: Install backend dependencies
      working-directory: ./vet-care-systems
      run: npm ci

    - name: Run backend tests
      working-directory: ./vet-care-systems
      run: npm test
      env:
        NODE_ENV: test
        JWT_SECRET: test_secret
        PORT: 3000
        DB_URI: mongodb://localhost:27017/test
        JWT_EXPIRES_IN: 1h

  frontend-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: 'lovable-vetcare/package-lock.json'

    - name: Install frontend dependencies
      working-directory: ./lovable-vetcare
      run: npm ci

    - name: Run frontend tests
      working-directory: ./lovable-vetcare
      run: npm test
      env:
        NODE_ENV: test

  lint:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install backend dependencies
      working-directory: ./vet-care-systems
      run: npm ci

    - name: Lint backend
      working-directory: ./vet-care-systems
      run: npx eslint .

    - name: Install frontend dependencies
      working-directory: ./lovable-vetcare
      run: npm ci

    - name: Lint frontend
      working-directory: ./lovable-vetcare
      run: npx eslint .

  build:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, lint]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Build backend
      working-directory: ./vet-care-systems
      run: |
        npm ci
        npm run build --if-present

    - name: Build frontend
      working-directory: ./lovable-vetcare
      run: |
        npm ci
        npm run build
