
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Activity, Users, Calendar, DollarSign } from "lucide-react";

const stats = [
  {
    name: "Total Patients",
    value: "2,345",
    change: "+12.3%",
    icon: Users,
    trend: "up",
  },
  {
    name: "Appointments Today",
    value: "24",
    change: "+4.5%",
    icon: Calendar,
    trend: "up",
  },
  {
    name: "Active Cases",
    value: "45",
    change: "-2.3%",
    icon: Activity,
    trend: "down",
  },
  {
    name: "Revenue",
    value: "$12,345",
    change: "+8.4%",
    icon: DollarSign,
    trend: "up",
  },
];

const Index = () => {
  return (
    <div className="p-8">
      <h1 className="text-3xl font-semibold mb-8">Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.name} className="backdrop-blur-md bg-white/80">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                {stat.name}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div
                className={`text-xs ${
                  stat.trend === "up"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {stat.change}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Index;
