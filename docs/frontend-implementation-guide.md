# Frontend Implementation Guide

This document outlines the implementation plan for the frontend components of the Vet-Care SaaS system, focusing on client registration, pet management, appointments, health records, and automated workflows.

## Table of Contents

1. [Client Registration Flow](#client-registration-flow)
2. [Pet Management](#pet-management)
3. [Appointment Scheduling](#appointment-scheduling)
4. [Health Records Management](#health-records-management)
5. [Automated Workflows](#automated-workflows)
6. [UI Components](#ui-components)
7. [State Management](#state-management)
8. [Implementation Roadmap](#implementation-roadmap)

## Client Registration Flow

### Client Registration Page

Create a multi-step registration form with the following steps:

1. **Basic Information**
   - First name, last name, email, phone number
   - Password creation and confirmation
   - Terms and conditions acceptance

2. **Contact Details**
   - Address
   - Alternative contact information
   - Emergency contact

3. **Preferences**
   - Communication preferences (email, SMS, phone)
   - Data sharing preferences
   - Preferred clinic selection

### Implementation Details

```tsx
// Example of ClientRegistrationForm component
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { createClient } from '@/services/client';

// Step 1: Basic Information schema
const basicInfoSchema = z.object({
  firstName: z.string().min(2, "First name is required"),
  lastName: z.string().min(2, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string().min(10, "Phone number is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});

// Step 2: Contact Details schema
const contactDetailsSchema = z.object({
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  zipCode: z.string().min(1, "Zip code is required"),
  alternativePhone: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional()
});

// Step 3: Preferences schema
const preferencesSchema = z.object({
  communicationPreferences: z.object({
    email: z.boolean().default(true),
    sms: z.boolean().default(true),
    phone: z.boolean().default(true)
  }),
  dataSharingPreferences: z.object({
    shareAllClinics: z.boolean().default(true)
  }),
  preferredClinicId: z.string().optional()
});

// Combined schema for all steps
const clientSchema = z.object({
  ...basicInfoSchema.shape,
  ...contactDetailsSchema.shape,
  ...preferencesSchema.shape
});

type ClientFormValues = z.infer<typeof clientSchema>;

const ClientRegistrationForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const form = useForm<ClientFormValues>({
    resolver: zodResolver(
      currentStep === 1 ? basicInfoSchema : 
      currentStep === 2 ? contactDetailsSchema : 
      preferencesSchema
    ),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      communicationPreferences: {
        email: true,
        sms: true,
        phone: true
      },
      dataSharingPreferences: {
        shareAllClinics: true
      }
    }
  });
  
  const nextStep = () => {
    setCurrentStep(prev => prev + 1);
  };
  
  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };
  
  const onSubmit = async (values: ClientFormValues) => {
    try {
      const result = await createClient(values);
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Client registered successfully",
        });
        navigate(`/clients/${result.data._id}`);
      } else {
        throw new Error(result.message);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to register client",
        variant: "destructive",
      });
    }
  };
  
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Client Registration</h2>
        <div className="flex space-x-2">
          <div className={`h-2 w-8 rounded ${currentStep >= 1 ? 'bg-primary' : 'bg-gray-200'}`}></div>
          <div className={`h-2 w-8 rounded ${currentStep >= 2 ? 'bg-primary' : 'bg-gray-200'}`}></div>
          <div className={`h-2 w-8 rounded ${currentStep >= 3 ? 'bg-primary' : 'bg-gray-200'}`}></div>
        </div>
      </div>
      
      <form onSubmit={form.handleSubmit(currentStep === 3 ? onSubmit : nextStep)}>
        {currentStep === 1 && (
          <BasicInformationStep form={form} />
        )}
        
        {currentStep === 2 && (
          <ContactDetailsStep form={form} />
        )}
        
        {currentStep === 3 && (
          <PreferencesStep form={form} />
        )}
        
        <div className="flex justify-between mt-8">
          {currentStep > 1 && (
            <Button type="button" variant="outline" onClick={prevStep}>
              Previous
            </Button>
          )}
          
          <Button type="submit">
            {currentStep === 3 ? "Register" : "Next"}
          </Button>
        </div>
      </form>
    </div>
  );
};
```

## Pet Management

### Pet Registration

Create a form to add pets to a client's profile:

1. **Basic Pet Information**
   - Name, species, breed
   - Date of birth, gender
   - Microchip ID, color

2. **Medical Information**
   - Weight
   - Allergies
   - Chronic conditions
   - Vaccination history

### Pet Profile Page

Create a comprehensive pet profile page with:

1. **Basic Information Section**
   - Pet details
   - Owner information
   - Edit functionality

2. **Medical History Tab**
   - Timeline of medical records
   - Filterable by record type, date, clinic
   - Detailed view for each record

3. **Appointments Tab**
   - Upcoming appointments
   - Past appointments
   - Ability to schedule new appointments

### Implementation Example

```tsx
// Example of PetProfile component
const PetProfile = () => {
  const { petId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Fetch pet data with minimal fields initially
  const { data: petData, isLoading: petLoading } = useQuery({
    queryKey: ["pet", petId, "basic"],
    queryFn: () => getPet(petId!),
  });
  
  // Tabs state
  const [activeTab, setActiveTab] = useState("info");
  
  // Only fetch medical history when that tab is active
  const { data: medicalHistory, isLoading: historyLoading } = useQuery({
    queryKey: ["pet", petId, "medicalHistory"],
    queryFn: () => getPetMedicalHistory(petId!),
    enabled: activeTab === "medical",
  });
  
  // Only fetch appointments when that tab is active
  const { data: appointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ["pet", petId, "appointments"],
    queryFn: () => getPetAppointments(petId!),
    enabled: activeTab === "appointments",
  });
  
  if (petLoading) {
    return <LoadingSpinner />;
  }
  
  if (!petData?.data) {
    return <PetNotFound />;
  }
  
  const pet = petData.data;
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{pet.name}</h1>
        <Button onClick={() => navigate(`/pets/${petId}/edit`)}>
          Edit Pet
        </Button>
      </div>
      
      <PetBasicInfo pet={pet} />
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="info">Information</TabsTrigger>
          <TabsTrigger value="medical">Medical History</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
        </TabsList>
        
        <TabsContent value="info">
          <PetDetailedInfo pet={pet} />
        </TabsContent>
        
        <TabsContent value="medical">
          {activeTab === "medical" && (
            historyLoading ? <LoadingSpinner /> : <MedicalHistoryList data={medicalHistory?.data || []} />
          )}
        </TabsContent>
        
        <TabsContent value="appointments">
          {activeTab === "appointments" && (
            appointmentsLoading ? <LoadingSpinner /> : <AppointmentsList data={appointments?.data || []} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

## Appointment Scheduling

### Appointment Booking Flow

Create a multi-step appointment booking process:

1. **Service Selection**
   - Choose service type
   - Select pet(s) for the appointment

2. **Date and Time Selection**
   - Calendar view of available slots
   - Time slot selection

3. **Confirmation**
   - Appointment details summary
   - Special instructions or notes
   - Confirmation button

### Appointment Management

Create views for:

1. **Clinic Calendar**
   - Daily, weekly, monthly views
   - Color-coded by appointment type
   - Drag-and-drop rescheduling

2. **Appointment Details**
   - Client and pet information
   - Service details
   - Options to check in, reschedule, or cancel

## Health Records Management

### Health Record Creation

Create a form to add health records after appointments:

1. **Basic Information**
   - Record type (consultation, vaccination, surgery, etc.)
   - Date and time
   - Performing staff member

2. **Medical Details**
   - Diagnosis
   - Treatment
   - Medications
   - Lab results
   - Vital signs

3. **Follow-up Information**
   - Follow-up date
   - Instructions
   - Attachments

### Medical History View

Create a comprehensive view of a pet's medical history:

1. **Timeline View**
   - Chronological display of all records
   - Filterable by type, date, clinic

2. **Record Details**
   - Expandable record cards
   - Full medical details
   - Attached documents and images

## Automated Workflows

### Appointment to Health Record Workflow

Implement automatic transitions from appointments to health records:

1. **Appointment Check-in**
   - When a pet is checked in, create a draft health record
   - Pre-fill information from appointment

2. **Health Record Creation**
   - After appointment completion, prompt for health record completion
   - Link health record to appointment

### Reminders System

Implement automated reminders:

1. **Appointment Reminders**
   - Email/SMS reminders 24 hours before appointment
   - Configurable reminder times

2. **Follow-up Reminders**
   - Based on health record follow-up dates
   - Staff notifications for follow-up calls

3. **Vaccination Reminders**
   - Based on pet vaccination schedules
   - Automatic reminder generation

### Billing and Invoicing Workflow

Implement automated billing:

1. **Invoice Generation**
   - Create invoice after health record completion
   - Include services, medications, and lab tests

2. **Payment Processing**
   - Online payment options
   - Receipt generation

3. **Account Updates**
   - Update client account balance
   - Track payment history

## UI Components

### Common Components

Develop reusable components:

1. **Client/Pet Selector**
   - Searchable dropdown with client/pet information
   - Quick access to frequently used clients/pets

2. **Date/Time Picker**
   - Calendar view with available slots
   - Time slot selection

3. **Medical Record Card**
   - Compact view of medical record
   - Expandable for details

### Layout Components

Develop consistent layouts:

1. **Dashboard Layout**
   - Sidebar navigation
   - Header with user information
   - Main content area

2. **Form Layout**
   - Consistent form styling
   - Validation error display
   - Form section organization

## State Management

### Global State

Implement global state management:

1. **Authentication State**
   - Current user information
   - Role-based access control
   - Token management

2. **Clinic Context**
   - Current clinic information
   - Clinic-specific settings

### API Integration

Implement API service layer:

1. **API Client**
   - Axios instance with interceptors
   - Token handling
   - Error handling

2. **Service Functions**
   - Typed API functions for each entity
   - Response transformation

## Implementation Roadmap

### Phase 1: Client and Pet Management

1. Client registration and profile management
2. Pet registration and profile management
3. Client-pet relationship management

### Phase 2: Appointment System

1. Appointment booking flow
2. Calendar views
3. Appointment management

### Phase 3: Health Records

1. Health record creation
2. Medical history views
3. Cross-clinic record access

### Phase 4: Automated Workflows

1. Appointment to health record workflow
2. Reminders system
3. Billing and invoicing workflow

### Phase 5: Optimization and Polish

1. Performance optimization
2. UI/UX improvements
3. Mobile responsiveness
