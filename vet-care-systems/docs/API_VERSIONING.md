# API Versioning Strategy

This document outlines the API versioning strategy for the Vet Care application to ensure backward compatibility while allowing for evolution of the API.

## Versioning Approach

We use URL path versioning with the following format:

```
/api/v{major}/resource
```

For example:
- `/api/v1/pets` - Version 1 of the pets API
- `/api/v2/pets` - Version 2 of the pets API (when created)

## Version Lifecycle

1. **Current Version**: The latest stable version of the API.
2. **Deprecated Version**: A version that is still supported but will be removed in the future.
3. **Sunset Version**: A version that has been deprecated for a significant period and will be removed soon.

## Compatibility Guidelines

### Breaking Changes

The following are considered breaking changes and require a new API version:

- Removing or renaming endpoints
- Removing or renaming required request parameters
- Changing the structure of response objects
- Changing the semantics of request or response values
- Changing error codes or error response format

### Non-Breaking Changes

The following changes do not require a new API version:

- Adding new endpoints
- Adding optional request parameters
- Adding new fields to response objects (clients should ignore unknown fields)
- Adding new error codes for new conditions
- Performance improvements with identical behavior

## Version Support Policy

- We support at least the two most recent major versions of the API.
- When a new version is released, the oldest supported version may be deprecated.
- Deprecated versions will be supported for at least 6 months before being removed.
- Clients will be notified of deprecation through response headers and documentation.

## Deprecation Process

1. **Announcement**: When a version is deprecated, we add a deprecation notice in the documentation.
2. **Warning Headers**: Deprecated API versions return a `Deprecation` header in responses.
3. **Sunset Headers**: When a version is scheduled for removal, we add a `Sunset` header with the removal date.

Example headers:
```
Deprecation: true
Sunset: Sat, 31 Dec 2023 23:59:59 GMT
Link: <https://api.example.com/docs/api-upgrades>; rel="deprecation"
```

## Changelog

A changelog is maintained for each API version to track changes:

### v1 (Current)

- Initial release with core functionality
- Endpoints for authentication, users, clinics, staff, clients, pets, appointments, etc.

## Implementation Details

### Route Organization

API routes are organized by version in the codebase:

```
routes/
  v1/
    auth.routes.js
    user.routes.js
    ...
  v2/
    auth.routes.js
    user.routes.js
    ...
```

### Controller Organization

Controllers are also organized by version:

```
controllers/
  v1/
    auth.controller.js
    user.controller.js
    ...
  v2/
    auth.controller.js
    user.controller.js
    ...
```

### Version-Specific Middleware

Version-specific middleware can be applied to all routes of a specific version:

```javascript
// Apply version-specific middleware to all v1 routes
app.use('/api/v1', v1SpecificMiddleware);
```

## Testing

Each API version has its own test suite to ensure backward compatibility is maintained.
