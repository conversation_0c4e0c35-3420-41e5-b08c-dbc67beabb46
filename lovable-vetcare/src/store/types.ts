interface BaseEntity {
  _id?: string;
  createdAt?: string;
  updatedAt?: string;
}

// User types
interface User extends BaseEntity {
  userId?: number;
  userProfileId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  digitalAddress?: string;
  dob?: Date;
  password?: string;
  userStatus: 0 | 1 | 2 | 3 | 4; // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
  userType?: 'client' | 'staff' | 'admin';
  lastLogin?: Date;
  loginCount?: number;
  currentClinicId?: string;
}

interface UserCreateDto extends Omit<User, 'userId' | 'createdAt' | 'updatedAt'> {}
interface UserUpdateDto extends Partial<Omit<User, 'userId' | 'createdAt' | 'updatedAt'>> {}

type GeoLocation = [number, number]; // [latitude, longitude]

// Client types
interface Client extends BaseEntity {
  clientId?: number;
  accountId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address?: string;
  homeLocation: GeoLocation;
  area?: string;
  houseNumber?: string;
  buildingName?: string;
  isWalkIn?: boolean; // Walk-in client flag
  clientStatus: 0 | 1 | 2 | 3 | 4; // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
}

interface ClientCreateDto extends Omit<Client, '_id' | 'createdAt' | 'updatedAt'> {}
interface ClientUpdateDto extends Partial<Omit<Client, '_id' | 'createdAt' | 'updatedAt'>> {}

// Visit types
interface Visit extends BaseEntity {
  visitDate: string;
  notes?: string;
  petId?: string;
  vetId?: string;
  clinicId?: string;
}

interface VisitCreateDto extends Omit<Visit, 'visitId' | 'createdAt' | 'updatedAt'> {}
interface VisitUpdateDto extends Partial<Omit<Visit, 'visitId' | 'createdAt' | 'updatedAt'>> {}

// Pet types
interface Pet extends BaseEntity {
  petId?: number;
  name: string;
  speciesId: number;
  breedId: number;
  microchipId: string;
  color: string;
  lifeStatus: 'alive' | 'deceased';
  gender: 'male' | 'female';
  dateOfBirth: Date;
  weight: number;
  petStatus?: 0 | 1 | 2 | 3 | 4; // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
  clientId: number; // Reference to Client
  ownerId?: number; // Deprecated, use clientId instead
  // Populated fields
  species?: Species;
  breed?: Breed;
  client?: Client;
}

interface PetCreateDto extends Omit<Pet, 'petId' | 'createdAt' | 'updatedAt' | 'species' | 'breed' | 'client'> {}
interface PetUpdateDto extends Partial<Omit<Pet, 'petId' | 'createdAt' | 'updatedAt' | 'species' | 'breed' | 'client'>> {}

// Owner types (deprecated, use Client instead)
interface Owner extends User {
  // Inherits all User properties
}

// Clinic types
interface Clinic extends BaseEntity {
  clinicId?: number;
  clinicName: string;
  ownerId: string;
  phoneNumber: string;
  email: string;
  address: string;
  status?: 0 | 1; // 0: Inactive, 1: Active
  location?: {
    type: string;
    coordinates: [number, number]; // [longitude, latitude]
  };
  operatingHours?: {
    monday?: { open?: string; close?: string };
    tuesday?: { open?: string; close?: string };
    wednesday?: { open?: string; close?: string };
    thursday?: { open?: string; close?: string };
    friday?: { open?: string; close?: string };
    saturday?: { open?: string; close?: string };
    sunday?: { open?: string; close?: string };
  };
  description?: string;
  website?: string;
  logo?: string;
}

interface ClinicCreateDto extends Omit<Clinic, '_id' | 'createdAt' | 'updatedAt'> {}
interface ClinicUpdateDto extends Partial<Omit<Clinic, '_id' | 'createdAt' | 'updatedAt'>> {}

// Staff types
interface Staff extends BaseEntity {
  // Auto-increment ID
  staffId?: number;

  // User information (populated)
  userId?: {
    _id: string;
    userId?: number;
    firstName: string;
    middleName?: string;
    lastName: string;
    email: string;
    phoneNumber: string;
  } | string;

  // Clinic information (populated)
  clinicId?: {
    _id: string;
    clinicId?: number;
    clinicName: string;
    address?: string;
  } | string;
  primaryClinicId?: {
    _id: string;
    clinicId?: number;
    clinicName: string;
    address?: string;
  } | string;

  // Role information (populated)
  roleId?: {
    _id: string;
    roleId: number;
    name: string;
    permissions: number[];
  } | number | string;

  // Direct staff properties
  firstName?: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  name?: string; // Fallback for display
  address?: string;
  dob?: Date;

  // Additional clinics
  additionalClinics?: string[];

  // Job information
  jobTitle?: string;
  employmentDate?: Date;
  salary?: number;
  isClinicOwner?: boolean;

  // Contact information
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };

  // Schedule
  schedule?: {
    sunday?: { start: string; end: string };
    monday?: { start: string; end: string };
    tuesday?: { start: string; end: string };
    wednesday?: { start: string; end: string };
    thursday?: { start: string; end: string };
    friday?: { start: string; end: string };
    saturday?: { start: string; end: string };
  };

  // Status and role
  status: 0 | 1; // 0: Inactive, 1: Active
  role?: string;
  specialization?: string;
  bio?: string;
  imageUrl?: string;

  // Permissions
  specialPermissions?: number[];
  revokedPermissions?: number[];

  // Activity tracking
  clinicActivity?: Array<{
    clinicId: string;
    lastActive: Date;
    activityCount: number;
  }>;
}

interface StaffCreateDto extends Omit<Staff, '_id' | 'createdAt' | 'updatedAt'> {}
interface StaffUpdateDto extends Partial<Omit<Staff, '_id' | 'createdAt' | 'updatedAt'>> {}

// Vet types (extends Staff)
interface Vet extends Staff {
  // Vet-specific properties
  specialization: string;
  licenseNumber?: string;
  education?: string;
  certifications?: string[];
}

interface VetCreateDto extends Omit<Vet, '_id' | 'createdAt' | 'updatedAt'> {}
interface VetUpdateDto extends Partial<Omit<Vet, '_id' | 'createdAt' | 'updatedAt'>> {}

// Appointment types
interface Appointment extends BaseEntity {
  appointmentId?: number;
  petId: number;
  petName: string;
  clientId: number;
  clientName: string;
  vetId: number;
  appointmentTypes: Array<{
    appointmentTypeId: number;
    price: number;
    currency: Currency;
  }>;
  dateTime: Date;
  duration: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  reason: string;
  notes?: string;
}

interface AppointmentCreateDto extends Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'> {}
interface AppointmentUpdateDto extends Partial<Omit<Appointment, '_id' | 'createdAt' | 'updatedAt'>> {}

type Currency = 'KES' | 'USD' | 'EUR' | 'GBP' | 'CNY' | 'JPY' | 'AUD' | 'CAD' | 'CHF' | 'AED';

// AppointmentType types
interface AppointmentType extends BaseEntity {
  name: string;
  description?: string;
  defaultDuration: number;
  price: number;
  currency: Currency;
  isActive: boolean;
}

interface AppointmentTypeCreateDto extends Omit<AppointmentType, '_id' | 'createdAt' | 'updatedAt'> {}
interface AppointmentTypeUpdateDto extends Partial<Omit<AppointmentType, '_id' | 'createdAt' | 'updatedAt'>> {}

// ServiceType types
interface ServiceType extends BaseEntity {
  name: string;
  description?: string;
  category: 'vaccination' | 'treatment' | 'surgery' | 'grooming' | 'checkup' | 'medication' | 'other';
  minCharge: number;
  maxCharge: number;
  defaultDuration: number;
  currency: Currency;
  isActive: boolean;
}

interface ServiceTypeCreateDto extends Omit<ServiceType, '_id' | 'createdAt' | 'updatedAt'> {}
interface ServiceTypeUpdateDto extends Partial<Omit<ServiceType, '_id' | 'createdAt' | 'updatedAt'>> {}

// Role types
interface Role extends BaseEntity {
  roleId?: number;
  name: string;
  description?: string;
  permissions: number[]; // Array of permission IDs
  status?: 0 | 1; // 0: Inactive, 1: Active
}

interface RoleCreateDto extends Omit<Role, '_id' | 'createdAt' | 'updatedAt' | 'roleId'> {}
interface RoleUpdateDto extends Partial<Omit<Role, '_id' | 'createdAt' | 'updatedAt' | 'roleId'>> {}

// Permission types
interface Permission extends BaseEntity {
  permissionId: number;
  name: string;
  description?: string;
  module: string;
  status: 0 | 1; // 0: Inactive, 1: Active
}

interface PermissionCreateDto extends Omit<Permission, '_id' | 'createdAt' | 'updatedAt'> {}
interface PermissionUpdateDto extends Partial<Omit<Permission, '_id' | 'createdAt' | 'updatedAt'>> {}

// Species types
interface Species extends BaseEntity {
  speciesId?: number;
  name: string; // Changed from speciesName to name to match frontend usage
  speciesName?: string; // Keep for backend compatibility
  description?: string;
  imageUrl?: string;
  status: 0 | 1; // 0: Inactive, 1: Active
}

interface SpeciesCreateDto extends Omit<Species, '_id' | 'createdAt' | 'updatedAt'> {}
interface SpeciesUpdateDto extends Partial<Omit<Species, '_id' | 'createdAt' | 'updatedAt'>> {}

// Breed types
interface Breed extends BaseEntity {
  breedId?: number;
  speciesId: number | Species; // Can be populated with Species object
  breedName: string;
  commonColour?: string;
  origin?: string;
  sizeCategory?: 'small' | 'medium' | 'large';
  lifespan?: number;
  temperament?: string;
  breedProfileId?: string;
}

interface BreedCreateDto extends Omit<Breed, '_id' | 'createdAt' | 'updatedAt'> {}
interface BreedUpdateDto extends Partial<Omit<Breed, '_id' | 'createdAt' | 'updatedAt'>> {}

// Health Record types
interface HealthRecord extends BaseEntity {
  petId: string;
  visitId: string;
  vetId: string;
  diagnosis: string;
  treatment: string;
  medications: Array<{
    name: string;
    dosage: string;
    frequency: string;
    duration?: string;
    notes?: string;
  }>;
  labResults?: Array<{
    testName: string;
    result: string;
    normalRange?: string;
    interpretation?: string;
    attachmentUrl?: string;
  }>;
  vitalSigns?: {
    temperature?: number;
    heartRate?: number;
    respiratoryRate?: number;
    weight?: number;
    bloodPressure?: string;
  };
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileType: string;
    uploadDate?: Date;
  }>;
  followUpDate?: Date;
  followUpInstructions?: string;
  notes?: string;
}

interface HealthRecordCreateDto extends Omit<HealthRecord, '_id' | 'createdAt' | 'updatedAt'> {}
interface HealthRecordUpdateDto extends Partial<Omit<HealthRecord, '_id' | 'createdAt' | 'updatedAt'>> {}

// Response types
interface PaginationData {
  totalCount: number;
  page: number;
  limit: number;
  offset: number;
  totalPages: number;
}

interface MessageResponse {
  success: boolean;
  status: number;
  message: string;
}

interface StandardResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data: T;
}

interface PaginatedResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: T[];
    pagination: PaginationData;
  };
}

export type {
  // Base types
  BaseEntity,
  GeoLocation,
  Currency,

  // Entity interfaces
  User,
  Client,
  Visit,
  Pet,
  Owner,
  Clinic,
  Staff,
  Vet,
  Appointment,
  AppointmentType,
  ServiceType,
  Role,
  Permission,
  Species,
  Breed,
  HealthRecord,

  // DTO interfaces for creating entities
  UserCreateDto,
  ClientCreateDto,
  VisitCreateDto,
  PetCreateDto,
  ClinicCreateDto,
  StaffCreateDto,
  VetCreateDto,
  AppointmentCreateDto,
  AppointmentTypeCreateDto,
  ServiceTypeCreateDto,
  RoleCreateDto,
  PermissionCreateDto,
  SpeciesCreateDto,
  BreedCreateDto,
  HealthRecordCreateDto,

  // DTO interfaces for updating entities
  UserUpdateDto,
  ClientUpdateDto,
  VisitUpdateDto,
  PetUpdateDto,
  ClinicUpdateDto,
  StaffUpdateDto,
  VetUpdateDto,
  AppointmentUpdateDto,
  AppointmentTypeUpdateDto,
  ServiceTypeUpdateDto,
  RoleUpdateDto,
  PermissionUpdateDto,
  SpeciesUpdateDto,
  BreedUpdateDto,
  HealthRecordUpdateDto,

  // Response types
  PaginationData,
  MessageResponse,
  StandardResponse,
  PaginatedResponse
};
