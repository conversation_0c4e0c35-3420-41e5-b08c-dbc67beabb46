import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createClientClinicRelationship,
    getClientRelationships,
    getClinicRelationships,
    getRelationship,
    updateRelationship
} from '../controllers/clientClinicRelationship.controller.js';

const router = Router();

// Base route: /api/v1/client-clinic-relationships

// Protected routes
router.use(verifyToken);

// Create a new client-clinic relationship
router.post('/', createClientClinicRelationship);

// Get all relationships for a specific client
router.get('/client/:clientId', getClientRelationships);

// Get all relationships for a specific clinic
router.get('/clinic/:clinicId', getClinicRelationships);

// Get a specific relationship
router.get('/:relationshipId', getRelationship);

// Update a relationship
router.put('/:relationshipId', updateRelationship);

export default router;
