import express from 'express';
import {
    getReceiptById,
    getReceiptByAppointment,
    getAllReceipts,
    updateReceiptStatus
} from '../controllers/receipt.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all receipts
router.get('/', getAllReceipts);

// Get receipt by ID
router.get('/:receiptId', getReceiptById);

// Get receipt by appointment ID
router.get('/appointment/:appointmentId', getReceiptByAppointment);

// Update receipt status
router.patch('/:receiptId/status', updateReceiptStatus);

export default router;
