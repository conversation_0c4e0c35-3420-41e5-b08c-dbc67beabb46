import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Clock,
  User,
  Shield,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Globe,
  Camera
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { api } from "@/services/api";

interface ClinicData {
  // Basic Info
  clinicName: string;
  description: string;
  phoneNumber: string;
  email: string;
  website: string;

  // Address
  address: string;
  city: string;
  state: string;
  zipCode: string;

  // Operating Hours
  operatingHours: {
    [key: string]: { open: string; close: string; closed?: boolean };
  };

  // Owner Info
  ownerFirstName: string;
  ownerLastName: string;
  ownerEmail: string;
  ownerPhone: string;
  ownerAddress: string;
}

interface ClinicCreationWizardProps {
  onComplete: (clinic: any) => void;
  onCancel: () => void;
}

const STEPS = [
  { id: 1, title: "Basic Information", icon: Building2 },
  { id: 2, title: "Contact & Location", icon: MapPin },
  { id: 3, title: "Operating Hours", icon: Clock },
  { id: 4, title: "Owner Details", icon: User },
  { id: 5, title: "Review & Create", icon: CheckCircle }
];

const DAYS = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' }
];

export const ClinicCreationWizard = ({ onComplete, onCancel }: ClinicCreationWizardProps) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ClinicData>({
    clinicName: "",
    description: "",
    phoneNumber: "",
    email: "",
    website: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    operatingHours: {
      monday: { open: "08:00", close: "18:00" },
      tuesday: { open: "08:00", close: "18:00" },
      wednesday: { open: "08:00", close: "18:00" },
      thursday: { open: "08:00", close: "18:00" },
      friday: { open: "08:00", close: "18:00" },
      saturday: { open: "08:00", close: "16:00" },
      sunday: { closed: true, open: "", close: "" }
    },
    ownerFirstName: "",
    ownerLastName: "",
    ownerEmail: "",
    ownerPhone: "",
    ownerAddress: ""
  });

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateOperatingHours = (day: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      operatingHours: {
        ...prev.operatingHours,
        [day]: {
          ...prev.operatingHours[day],
          [field]: value
        }
      }
    }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await api.post('/clinics/register-owner', {
        // Owner details
        firstName: formData.ownerFirstName,
        lastName: formData.ownerLastName,
        email: formData.ownerEmail,
        phoneNumber: formData.ownerPhone,
        address: formData.ownerAddress,

        // Clinic details
        clinicName: formData.clinicName,
        clinicAddress: `${formData.address}, ${formData.city}, ${formData.state} ${formData.zipCode}`,
        clinicPhoneNumber: formData.phoneNumber,
        clinicEmail: formData.email,
        operatingHours: formData.operatingHours,
        location: { type: 'Point', coordinates: [0, 0] } // Default coordinates
      });

      if (response.data?.success) {
        toast({
          title: "Success!",
          description: "Clinic and owner created successfully",
        });
        onComplete(response.data.data);
      } else {
        throw new Error(response.data?.message || "Failed to create clinic");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create clinic",
      });
    } finally {
      setLoading(false);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Create New Clinic</h1>
        <p className="text-gray-600">Set up your veterinary clinic in just a few steps</p>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progress} className="h-2" />
        <div className="flex justify-between text-sm text-gray-500">
          <span>Step {currentStep} of {STEPS.length}</span>
          <span>{Math.round(progress)}% Complete</span>
        </div>
      </div>

      {/* Steps Navigation */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4">
          {STEPS.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;

            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all
                  ${isActive ? 'border-primary bg-primary text-white' :
                    isCompleted ? 'border-green-500 bg-green-500 text-white' :
                    'border-gray-300 bg-white text-gray-400'}
                `}>
                  <Icon className="h-5 w-5" />
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="min-h-[500px]">
        <CardHeader>
          <CardTitle className="flex items-center">
            {React.createElement(STEPS[currentStep - 1].icon, { className: "mr-2 h-5 w-5" })}
            {STEPS[currentStep - 1].title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && (
                <BasicInfoStep formData={formData} updateFormData={updateFormData} />
              )}
              {currentStep === 2 && (
                <ContactLocationStep formData={formData} updateFormData={updateFormData} />
              )}
              {currentStep === 3 && (
                <OperatingHoursStep
                  formData={formData}
                  updateOperatingHours={updateOperatingHours}
                />
              )}
              {currentStep === 4 && (
                <OwnerDetailsStep formData={formData} updateFormData={updateFormData} />
              )}
              {currentStep === 5 && (
                <ReviewStep formData={formData} />
              )}
            </motion.div>
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={currentStep === 1 ? onCancel : prevStep}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {currentStep === 1 ? 'Cancel' : 'Previous'}
        </Button>

        <Button
          onClick={currentStep === STEPS.length ? handleSubmit : nextStep}
          disabled={loading}
          className="flex items-center"
        >
          {currentStep === STEPS.length ? (
            loading ? 'Creating...' : 'Create Clinic'
          ) : (
            <>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

// Step Components
const BasicInfoStep = ({ formData, updateFormData }: any) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="clinicName">Clinic Name *</Label>
        <Input
          id="clinicName"
          placeholder="Happy Paws Veterinary Clinic"
          value={formData.clinicName}
          onChange={(e) => updateFormData('clinicName', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="website">Website</Label>
        <Input
          id="website"
          placeholder="https://www.happypaws.com"
          value={formData.website}
          onChange={(e) => updateFormData('website', e.target.value)}
        />
      </div>
    </div>

    <div className="space-y-2">
      <Label htmlFor="description">Description</Label>
      <Textarea
        id="description"
        placeholder="Describe your clinic's services and specialties..."
        value={formData.description}
        onChange={(e) => updateFormData('description', e.target.value)}
        rows={4}
      />
    </div>
  </div>
);

const ContactLocationStep = ({ formData, updateFormData }: any) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="email">Email *</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="phoneNumber">Phone Number *</Label>
        <Input
          id="phoneNumber"
          placeholder="+****************"
          value={formData.phoneNumber}
          onChange={(e) => updateFormData('phoneNumber', e.target.value)}
        />
      </div>
    </div>

    <div className="space-y-2">
      <Label htmlFor="address">Street Address *</Label>
      <Input
        id="address"
        placeholder="123 Main Street"
        value={formData.address}
        onChange={(e) => updateFormData('address', e.target.value)}
      />
    </div>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="space-y-2">
        <Label htmlFor="city">City *</Label>
        <Input
          id="city"
          placeholder="New York"
          value={formData.city}
          onChange={(e) => updateFormData('city', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="state">State *</Label>
        <Input
          id="state"
          placeholder="NY"
          value={formData.state}
          onChange={(e) => updateFormData('state', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="zipCode">ZIP Code *</Label>
        <Input
          id="zipCode"
          placeholder="10001"
          value={formData.zipCode}
          onChange={(e) => updateFormData('zipCode', e.target.value)}
        />
      </div>
    </div>
  </div>
);

const OperatingHoursStep = ({ formData, updateOperatingHours }: any) => (
  <div className="space-y-6">
    <div className="text-center space-y-2">
      <h3 className="text-lg font-medium">Set Your Operating Hours</h3>
      <p className="text-gray-600">Configure when your clinic is open for appointments</p>
    </div>

    <div className="space-y-4">
      {DAYS.map((day) => {
        const hours = formData.operatingHours[day.key];
        const isClosed = hours.closed;

        return (
          <div key={day.key} className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="w-20">
                <Label className="font-medium">{day.label}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={!isClosed}
                  onChange={(e) => updateOperatingHours(day.key, 'closed', !e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm text-gray-600">Open</span>
              </div>
            </div>

            {!isClosed && (
              <div className="flex items-center space-x-2">
                <Input
                  type="time"
                  value={hours.open}
                  onChange={(e) => updateOperatingHours(day.key, 'open', e.target.value)}
                  className="w-32"
                />
                <span className="text-gray-500">to</span>
                <Input
                  type="time"
                  value={hours.close}
                  onChange={(e) => updateOperatingHours(day.key, 'close', e.target.value)}
                  className="w-32"
                />
              </div>
            )}

            {isClosed && (
              <Badge variant="secondary">Closed</Badge>
            )}
          </div>
        );
      })}
    </div>
  </div>
);

const OwnerDetailsStep = ({ formData, updateFormData }: any) => (
  <div className="space-y-6">
    <div className="text-center space-y-2">
      <h3 className="text-lg font-medium">Clinic Owner Information</h3>
      <p className="text-gray-600">This person will have full administrative access to the clinic</p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="ownerFirstName">First Name *</Label>
        <Input
          id="ownerFirstName"
          placeholder="Dr. John"
          value={formData.ownerFirstName}
          onChange={(e) => updateFormData('ownerFirstName', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="ownerLastName">Last Name *</Label>
        <Input
          id="ownerLastName"
          placeholder="Smith"
          value={formData.ownerLastName}
          onChange={(e) => updateFormData('ownerLastName', e.target.value)}
        />
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="ownerEmail">Email *</Label>
        <Input
          id="ownerEmail"
          type="email"
          placeholder="<EMAIL>"
          value={formData.ownerEmail}
          onChange={(e) => updateFormData('ownerEmail', e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="ownerPhone">Phone Number *</Label>
        <Input
          id="ownerPhone"
          placeholder="+****************"
          value={formData.ownerPhone}
          onChange={(e) => updateFormData('ownerPhone', e.target.value)}
        />
      </div>
    </div>

    <div className="space-y-2">
      <Label htmlFor="ownerAddress">Address</Label>
      <Input
        id="ownerAddress"
        placeholder="456 Owner Street, City, State 12345"
        value={formData.ownerAddress}
        onChange={(e) => updateFormData('ownerAddress', e.target.value)}
      />
    </div>

    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
        <div>
          <h4 className="font-medium text-blue-900">Default Login Credentials</h4>
          <p className="text-sm text-blue-700 mt-1">
            The owner will receive login credentials with the email above and password: <code className="bg-blue-100 px-1 rounded">pass123</code>
          </p>
          <p className="text-sm text-blue-700 mt-1">
            They should change this password on first login for security.
          </p>
        </div>
      </div>
    </div>
  </div>
);

const ReviewStep = ({ formData }: any) => (
  <div className="space-y-6">
    <div className="text-center space-y-2">
      <h3 className="text-lg font-medium">Review & Confirm</h3>
      <p className="text-gray-600">Please review all information before creating the clinic</p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Clinic Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <Building2 className="mr-2 h-4 w-4" />
            Clinic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label className="text-sm text-gray-600">Name</Label>
            <p className="font-medium">{formData.clinicName}</p>
          </div>
          <div>
            <Label className="text-sm text-gray-600">Address</Label>
            <p className="text-sm">{formData.address}, {formData.city}, {formData.state} {formData.zipCode}</p>
          </div>
          <div>
            <Label className="text-sm text-gray-600">Contact</Label>
            <p className="text-sm">{formData.email}</p>
            <p className="text-sm">{formData.phoneNumber}</p>
          </div>
        </CardContent>
      </Card>

      {/* Owner Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <User className="mr-2 h-4 w-4" />
            Owner Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label className="text-sm text-gray-600">Name</Label>
            <p className="font-medium">{formData.ownerFirstName} {formData.ownerLastName}</p>
          </div>
          <div>
            <Label className="text-sm text-gray-600">Email</Label>
            <p className="text-sm">{formData.ownerEmail}</p>
          </div>
          <div>
            <Label className="text-sm text-gray-600">Phone</Label>
            <p className="text-sm">{formData.ownerPhone}</p>
          </div>
        </CardContent>
      </Card>
    </div>

    {/* Operating Hours */}
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center">
          <Clock className="mr-2 h-4 w-4" />
          Operating Hours
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {DAYS.map((day) => {
            const hours = formData.operatingHours[day.key];
            return (
              <div key={day.key} className="text-center">
                <Label className="text-sm font-medium">{day.label}</Label>
                <p className="text-sm text-gray-600">
                  {hours.closed ? 'Closed' : `${hours.open} - ${hours.close}`}
                </p>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  </div>
);
