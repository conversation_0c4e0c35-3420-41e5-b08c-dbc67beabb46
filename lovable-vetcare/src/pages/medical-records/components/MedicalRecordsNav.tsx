
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Stethoscope, 
  Syringe, 
  Scissors, 
  TestTube 
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';

const MedicalRecordsNav = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname.split('/').pop();

  const handleValueChange = (value: string) => {
    navigate(`/records/${value.toLowerCase()}`);
  };

  return (
    <div className="w-full max-w-xs mb-6">
      <Select
        value={currentPath || 'consultations'}
        onValueChange={handleValueChange}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select Record Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="consultations">
            <div className="flex items-center gap-2">
              <Stethoscope className="h-4 w-4" />
              <span>Consultations</span>
            </div>
          </SelectItem>
          <SelectItem value="vaccinations">
            <div className="flex items-center gap-2">
              <Syringe className="h-4 w-4" />
              <span>Vaccinations</span>
            </div>
          </SelectItem>
          <SelectItem value="surgeries">
            <div className="flex items-center gap-2">
              <Scissors className="h-4 w-4" />
              <span>Surgeries</span>
            </div>
          </SelectItem>
          <SelectItem value="laboratory">
            <div className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              <span>Laboratory</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default MedicalRecordsNav;
