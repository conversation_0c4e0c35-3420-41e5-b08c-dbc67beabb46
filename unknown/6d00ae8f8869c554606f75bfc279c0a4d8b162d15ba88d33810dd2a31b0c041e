# Frontend State Management Guide

## Overview

This guide provides detailed instructions for implementing improved state management in the Veterinary SaaS frontend application. It focuses on using Zustand with a modular approach and integrating with React Query for data fetching.

## Table of Contents

1. [Zustand Store Structure](#zustand-store-structure)
2. [Creating Store Slices](#creating-store-slices)
3. [API Integration](#api-integration)
4. [React Query Integration](#react-query-integration)
5. [Authentication Flow](#authentication-flow)
6. [Example Usage](#example-usage)

## Zustand Store Structure

### Directory Structure

```
src/
├── store/
│   ├── index.ts
│   ├── types.ts
│   └── slices/
│       ├── authSlice.ts
│       ├── clinicSlice.ts
│       ├── staffSlice.ts
│       ├── clientSlice.ts
│       ├── petSlice.ts
│       └── appointmentSlice.ts
```

### Main Store File

```typescript
// src/store/index.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createAuthSlice, AuthState } from './slices/authSlice';
import { createClinic<PERSON>lice, ClinicState } from './slices/clinicSlice';
import { createStaffSlice, StaffState } from './slices/staffSlice';
import { createClientSlice, ClientState } from './slices/clientSlice';
import { createPetSlice, PetState } from './slices/petSlice';
import { createAppointmentSlice, AppointmentState } from './slices/appointmentSlice';

export type StoreState = AuthState & 
  ClinicState & 
  StaffState & 
  ClientState & 
  PetState & 
  AppointmentState;

export const useStore = create<StoreState>()(
  persist(
    (...a) => ({
      ...createAuthSlice(...a),
      ...createClinicSlice(...a),
      ...createStaffSlice(...a),
      ...createClientSlice(...a),
      ...createPetSlice(...a),
      ...createAppointmentSlice(...a),
    }),
    {
      name: 'vetcare-storage',
      partialize: (state) => ({
        token: state.token,
        userType: state.userType,
        currentClinic: state.currentClinic,
      }),
    }
  )
);
```

## Creating Store Slices

### Auth Slice

```typescript
// src/store/slices/authSlice.ts
import { StateCreator } from 'zustand';
import { Staff, Client, Admin, Clinic, StandardResponse } from '../types';
import api from '@/services/api';

export interface AuthState {
  isAuthenticated: boolean;
  userType: 'staff' | 'client' | 'admin' | null;
  staff: Staff | null;
  client: Client | null;
  admin: Admin | null;
  token: string | null;
  currentClinic: Clinic | null;
  availableClinics: Clinic[];
  login: (email: string, password: string, userType: string) => Promise<StandardResponse>;
  logout: () => void;
  switchClinic: (clinicId: string) => Promise<StandardResponse>;
}

export const createAuthSlice: StateCreator<AuthState> = (set, get) => ({
  isAuthenticated: false,
  userType: null,
  staff: null,
  client: null,
  admin: null,
  token: null,
  currentClinic: null,
  availableClinics: [],
  
  login: async (email, password, userType = 'client') => {
    try {
      const response = await api.post<StandardResponse>('/auth/sign-in', {
        email,
        password,
        userType
      });
      
      if (response.success) {
        const { token, user, staff, client, admin, clinics } = response.data;
        const currentClinic = clinics && clinics.length > 0 ? clinics[0] : null;
        
        set({
          isAuthenticated: true,
          token,
          userType,
          staff: userType === 'staff' ? staff : null,
          client: userType === 'client' ? client : null,
          admin: userType === 'admin' ? admin : null,
          currentClinic,
          availableClinics: clinics || []
        });
      }
      
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Login failed',
        data: {}
      };
    }
  },
  
  logout: () => {
    set({
      isAuthenticated: false,
      userType: null,
      staff: null,
      client: null,
      admin: null,
      token: null,
      currentClinic: null,
      availableClinics: []
    });
    window.location.href = '/';
  },
  
  switchClinic: async (clinicId) => {
    try {
      const response = await api.post<StandardResponse>('/clinics/switch', { clinicId });
      
      if (response.success) {
        const { token, currentClinic } = response.data;
        
        set({
          token,
          currentClinic
        });
      }
      
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Failed to switch clinic',
        data: {}
      };
    }
  }
});
```

### Clinic Slice

```typescript
// src/store/slices/clinicSlice.ts
import { StateCreator } from 'zustand';
import { Clinic, StandardResponse } from '../types';
import api from '@/services/api';
import { StoreState } from '../index';

export interface ClinicState {
  clinics: Clinic[];
  isLoading: boolean;
  error: string | null;
  fetchClinics: () => Promise<void>;
  createClinic: (clinicData: Partial<Clinic>) => Promise<StandardResponse>;
  updateClinic: (clinicId: string, clinicData: Partial<Clinic>) => Promise<StandardResponse>;
  deleteClinic: (clinicId: string) => Promise<StandardResponse>;
}

export const createClinicSlice: StateCreator<StoreState, [], [], ClinicState> = (set, get) => ({
  clinics: [],
  isLoading: false,
  error: null,
  
  fetchClinics: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await api.get<StandardResponse>('/clinics');
      
      if (response.success) {
        set({ clinics: response.data, isLoading: false });
      } else {
        set({ error: response.message, isLoading: false });
      }
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },
  
  createClinic: async (clinicData) => {
    try {
      const response = await api.post<StandardResponse>('/clinics', clinicData);
      
      if (response.success) {
        get().fetchClinics();
      }
      
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Failed to create clinic',
        data: {}
      };
    }
  },
  
  updateClinic: async (clinicId, clinicData) => {
    try {
      const response = await api.put<StandardResponse>(`/clinics/${clinicId}`, clinicData);
      
      if (response.success) {
        get().fetchClinics();
      }
      
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Failed to update clinic',
        data: {}
      };
    }
  },
  
  deleteClinic: async (clinicId) => {
    try {
      const response = await api.delete<StandardResponse>(`/clinics/${clinicId}`);
      
      if (response.success) {
        get().fetchClinics();
      }
      
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Failed to delete clinic',
        data: {}
      };
    }
  }
});
```

## API Integration

### API Service

```typescript
// src/services/api.ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { useStore } from '../store';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = useStore.getState().token;
    
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    // Handle unauthorized errors
    if (error.response && error.response.status === 401) {
      useStore.getState().logout();
    }
    
    return Promise.reject(error.response?.data || error);
  }
);

export default api;
```

## React Query Integration

### Query Hooks

```typescript
// src/hooks/useStaff.ts
import { useQuery, useMutation, useQueryClient } from 'react-query';
import api from '../services/api';
import { Staff, StandardResponse } from '../store/types';
import { useStore } from '../store';

export const useStaffList = (clinicId?: string) => {
  const currentClinic = useStore((state) => state.currentClinic);
  const effectiveClinicId = clinicId || currentClinic?._id;
  
  return useQuery(
    ['staff', effectiveClinicId],
    async () => {
      const response = await api.get<StandardResponse>(
        `/staff?clinicId=${effectiveClinicId}`
      );
      return response.data;
    },
    {
      enabled: !!effectiveClinicId,
    }
  );
};

export const useStaffMember = (staffId: string) => {
  return useQuery(
    ['staff', staffId],
    async () => {
      const response = await api.get<StandardResponse>(`/staff/${staffId}`);
      return response.data;
    },
    {
      enabled: !!staffId,
    }
  );
};

export const useCreateStaff = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    async (staffData: Partial<Staff>) => {
      const response = await api.post<StandardResponse>('/staff', staffData);
      return response;
    },
    {
      onSuccess: (_, variables) => {
        const clinicId = variables.clinicId || useStore.getState().currentClinic?._id;
        queryClient.invalidateQueries(['staff', clinicId]);
      },
    }
  );
};

export const useUpdateStaff = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    async ({ staffId, data }: { staffId: string; data: Partial<Staff> }) => {
      const response = await api.put<StandardResponse>(`/staff/${staffId}`, data);
      return response;
    },
    {
      onSuccess: (_, variables) => {
        const clinicId = variables.data.clinicId || useStore.getState().currentClinic?._id;
        queryClient.invalidateQueries(['staff', clinicId]);
        queryClient.invalidateQueries(['staff', variables.staffId]);
      },
    }
  );
};

export const useDeleteStaff = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    async (staffId: string) => {
      const response = await api.delete<StandardResponse>(`/staff/${staffId}`);
      return response;
    },
    {
      onSuccess: () => {
        const clinicId = useStore.getState().currentClinic?._id;
        queryClient.invalidateQueries(['staff', clinicId]);
      },
    }
  );
};
```

## Authentication Flow

### Login Component

```tsx
// src/pages/auth/Login.tsx
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStore } from '@/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/components/ui/use-toast';

interface LoginFormValues {
  email: string;
  password: string;
  userType: 'staff' | 'client' | 'admin';
}

export default function Login() {
  const [isLoading, setIsLoading] = useState(false);
  const login = useStore((state) => state.login);
  const { toast } = useToast();
  
  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormValues>({
    defaultValues: {
      email: '',
      password: '',
      userType: 'client'
    }
  });
  
  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    
    try {
      const response = await login(data.email, data.password, data.userType);
      
      if (!response.success) {
        toast({
          variant: 'destructive',
          title: 'Login Failed',
          description: response.message || 'Invalid credentials'
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Login Failed',
        description: error.message || 'An error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="w-full max-w-md p-8 space-y-8 bg-card rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Login to VetCare</h1>
          <p className="text-muted-foreground">Enter your credentials to access your account</p>
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...register('email', { required: 'Email is required' })}
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              {...register('password', { required: 'Password is required' })}
            />
            {errors.password && (
              <p className="text-sm text-destructive">{errors.password.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label>User Type</Label>
            <RadioGroup defaultValue="client" className="flex space-x-4">
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="client"
                  id="client"
                  {...register('userType')}
                />
                <Label htmlFor="client">Client</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="staff"
                  id="staff"
                  {...register('userType')}
                />
                <Label htmlFor="staff">Staff</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="admin"
                  id="admin"
                  {...register('userType')}
                />
                <Label htmlFor="admin">Admin</Label>
              </div>
            </RadioGroup>
          </div>
          
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Logging in...' : 'Login'}
          </Button>
        </form>
      </div>
    </div>
  );
}
```

## Example Usage

### Staff Management Page

```tsx
// src/pages/admin/staff/StaffList.tsx
import { useState } from 'react';
import { useStaffList, useDeleteStaff } from '@/hooks/useStaff';
import { useStore } from '@/store';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';
import { Plus, Edit, Trash } from 'lucide-react';

export default function StaffList() {
  const currentClinic = useStore((state) => state.currentClinic);
  const { data, isLoading, error } = useStaffList();
  const deleteStaffMutation = useDeleteStaff();
  const { toast } = useToast();
  
  const handleDelete = async (staffId: string) => {
    if (confirm('Are you sure you want to delete this staff member?')) {
      try {
        const response = await deleteStaffMutation.mutateAsync(staffId);
        
        if (response.success) {
          toast({
            title: 'Success',
            description: 'Staff member deleted successfully'
          });
        } else {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: response.message || 'Failed to delete staff member'
          });
        }
      } catch (error: any) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: error.message || 'An error occurred'
        });
      }
    }
  };
  
  const columns = [
    {
      accessorKey: 'firstName',
      header: 'First Name'
    },
    {
      accessorKey: 'lastName',
      header: 'Last Name'
    },
    {
      accessorKey: 'email',
      header: 'Email'
    },
    {
      accessorKey: 'jobTitle',
      header: 'Job Title'
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <span className={row.original.status === 1 ? 'text-green-500' : 'text-red-500'}>
          {row.original.status === 1 ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Link to={`/admin/staff/edit/${row.original._id}`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDelete(row.original._id)}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];
  
  if (isLoading) {
    return <div>Loading...</div>;
  }
  
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Staff Management</h1>
        <Link to="/admin/staff/add">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Staff
          </Button>
        </Link>
      </div>
      
      {currentClinic ? (
        <div className="bg-muted p-4 rounded-lg">
          <p className="font-medium">Current Clinic: {currentClinic.clinicName}</p>
        </div>
      ) : (
        <div className="bg-yellow-100 p-4 rounded-lg">
          <p className="text-yellow-800">No clinic selected</p>
        </div>
      )}
      
      <DataTable columns={columns} data={data || []} />
    </div>
  );
}
```

This guide provides a comprehensive approach to implementing state management in the Veterinary SaaS application. By following these patterns, you'll create a more maintainable, type-safe, and performant frontend application.
