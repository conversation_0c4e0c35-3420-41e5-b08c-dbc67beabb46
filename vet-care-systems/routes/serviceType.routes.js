import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createServiceType,
    getAllServiceTypes,
    getServiceTypeById,
    updateServiceType,
    deleteServiceType,
    getServiceTypesByCategory
} from '../controllers/serviceType.controller.js';

const serviceTypeRouter = Router();

// Apply authorization middleware to all routes
serviceTypeRouter.use(verifyToken);

serviceTypeRouter.post('/', createServiceType);
serviceTypeRouter.get('/', getAllServiceTypes);
serviceTypeRouter.get('/category/:category', getServiceTypesByCategory);
serviceTypeRouter.get('/:serviceTypeId', getServiceTypeById);
serviceTypeRouter.put('/:serviceTypeId', updateServiceType);
serviceTypeRouter.delete('/:serviceTypeId', deleteServiceType);

export default serviceTypeRouter;