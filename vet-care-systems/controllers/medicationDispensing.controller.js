import MedicationDispensing from '../models/medicationDispensing.model.js';
import Inventory from '../models/inventory.model.js';
import HealthRecord from '../models/healthRecord.model.js';
import Pet from '../models/pet.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

/**
 * Dispense medication
 * This function creates a medication dispensing record and updates inventory
 */
export const dispenseMedication = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const {
            healthRecordId,
            inventoryItemId,
            petId,
            clinicId,
            quantity,
            unit,
            dosage,
            frequency,
            duration,
            price,
            currency,
            batchNumber,
            expiryDate,
            instructions,
            notes
        } = req.body;

        // Check if health record exists
        const healthRecord = await HealthRecord.findOne({ healthRecordId }).session(session);
        if (!healthRecord) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Health record not found");
        }

        // Check if petId and clinicId match the health record
        if (healthRecord.petId.toString() !== petId || healthRecord.clinicId.toString() !== clinicId) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Pet or clinic does not match health record");
        }

        // Check if pet exists
        const pet = await Pet.findOne({ petId }).session(session);
        if (!pet) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Check if inventory item exists and has sufficient quantity
        const inventoryItem = await Inventory.findOne({ inventoryItemId }).session(session);
        if (!inventoryItem) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Inventory item not found");
        }

        if (inventoryItem.quantity < quantity) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Insufficient inventory quantity");
        }

        // Create medication dispensing record
        const dispensingRecord = await MedicationDispensing.create([{
            healthRecordId,
            inventoryItemId,
            petId,
            clinicId,
            dispensedBy: req.user._id,
            dispensedDate: new Date(),
            quantity,
            unit,
            dosage,
            frequency,
            duration,
            price,
            currency: currency || inventoryItem.currency,
            batchNumber: batchNumber || inventoryItem.batchNumber,
            expiryDate: expiryDate || inventoryItem.expiryDate,
            instructions,
            notes,
            status: 'dispensed'
        }], { session });

        // Update inventory quantity
        await Inventory.findOneAndUpdate(
            { inventoryItemId },
            {
                $inc: { quantity: -quantity },
                $set: { lastUsedDate: new Date() },
                $push: {
                    transactions: {
                        type: 'sale',
                        quantity: quantity,
                        date: new Date(),
                        performedBy: req.user._id,
                        notes: `Dispensed for health record ${healthRecordId}`,
                        relatedRecord: {
                            recordType: 'healthRecord',
                            recordId: healthRecordId
                        }
                    }
                }
            },
            { session, new: true }
        );

        // Update health record with medication information
        await HealthRecord.findOneAndUpdate(
            { healthRecordId },
            {
                $push: {
                    medications: {
                        name: inventoryItem.name,
                        dosage: dosage,
                        frequency: frequency,
                        duration: duration,
                        notes: notes,
                        prescribedBy: req.user._id
                    }
                }
            },
            { session }
        );

        await session.commitTransaction();
        await session.endSession();

        // Populate the dispensing record with related data
        const populatedRecord = await MedicationDispensing.findOne({ _id: dispensingRecord[0]._id })
            .populate('healthRecordId', 'recordType date')
            .populate('inventoryItemId', 'name category')
            .populate('petId', 'petName')
            .populate('dispensedBy', 'firstName lastName');

        return sendResponse(res, 201, true, "Medication dispensed successfully", populatedRecord);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all medication dispensing records with pagination and filtering
 */
export const getAllMedicationDispensing = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "dispensedDate",
            sortOrder = "desc",
            petId,
            clinicId,
            healthRecordId,
            inventoryItemId,
            dispensedBy,
            status,
            startDate,
            endDate
        } = req.query;

        // Build query
        let query = {};

        if (petId) query.petId = petId;
        if (clinicId) query.clinicId = clinicId;
        if (healthRecordId) query.healthRecordId = healthRecordId;
        if (inventoryItemId) query.inventoryItemId = inventoryItemId;
        if (dispensedBy) query.dispensedBy = dispensedBy;
        if (status) query.status = status;

        // Date range filter
        if (startDate || endDate) {
            query.dispensedDate = {};
            if (startDate) query.dispensedDate.$gte = new Date(startDate);
            if (endDate) query.dispensedDate.$lte = new Date(endDate);
        }

        // Set up pagination and sorting
        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            sort: { [sortBy]: sortOrder === "desc" ? -1 : 1 },
            populate: [
                { path: 'healthRecordId', select: 'recordType date' },
                { path: 'inventoryItemId', select: 'name category' },
                { path: 'petId', select: 'petName' },
                { path: 'dispensedBy', select: 'firstName lastName' }
            ]
        };

        // Execute query with pagination
        const paginatedResults = paginateResults(MedicationDispensing, query, options);

        return sendResponse(res, 200, true, "Medication dispensing records retrieved successfully", paginatedResults);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get a specific medication dispensing record by ID
 */
export const getMedicationDispensingById = async (req, res) => {
    try {
        const { dispensingId } = req.params;

        const dispensingRecord = await MedicationDispensing.findOne({ medicationDispensingId: parseInt(dispensingId) })
            .populate('healthRecordId')
            .populate('inventoryItemId')
            .populate('petId')
            .populate('dispensedBy')
            .populate('clinicId');

        if (!dispensingRecord) {
            return sendResponse(res, 404, false, "Medication dispensing record not found");
        }

        return sendResponse(res, 200, true, "Medication dispensing record retrieved successfully", dispensingRecord);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update a medication dispensing record
 */
export const updateMedicationDispensing = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { dispensingId } = req.params;
        const { status, notes } = req.body;

        // Only allow updating status and notes
        if (!status && !notes) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "No valid fields to update");
        }

        // Find the dispensing record
        const dispensingRecord = await MedicationDispensing.findOne({ medicationDispensingId: parseInt(dispensingId) }).session(session);
        if (!dispensingRecord) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Medication dispensing record not found");
        }

        // Handle status change from 'dispensed' to 'returned'
        if (status === 'returned' && dispensingRecord.status === 'dispensed') {
            // Return the quantity to inventory
            await Inventory.findOneAndUpdate(
                { inventoryItemId: dispensingRecord.inventoryItemId },
                {
                    $inc: { quantity: dispensingRecord.quantity },
                    $push: {
                        transactions: {
                            type: 'adjustment',
                            quantity: dispensingRecord.quantity,
                            date: new Date(),
                            performedBy: req.user._id,
                            notes: `Returned from dispensing record ${dispensingId}`,
                            relatedRecord: {
                                recordType: 'healthRecord',
                                recordId: dispensingRecord.healthRecordId
                            }
                        }
                    }
                },
                { session }
            );
        }

        // Update the dispensing record
        const updatedRecord = await MedicationDispensing.findOneAndUpdate(
            { medicationDispensingId: parseInt(dispensingId) },
            {
                $set: {
                    ...(status && { status }),
                    ...(notes && { notes })
                }
            },
            { new: true, session }
        ).populate('healthRecordId inventoryItemId petId dispensedBy');

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 200, true, "Medication dispensing record updated successfully", updatedRecord);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        return sendResponse(res, 500, false, error.message);
    }
};
