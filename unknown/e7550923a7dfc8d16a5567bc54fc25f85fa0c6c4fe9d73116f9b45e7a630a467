import {createRequire} from "module"
import Clinic from "../models/clinic.model.js";
import {populate} from "dotenv";

const require = createRequire(import.meta.url)
const {serve} = require("@upstash/workflow/express");

export const sendReminders = serve(async (context) => {
    const {clinicId} = context.requestPayload;
    const clinic = await fetchClinics(context, clinicId);
    
    if (!clinic) {
        return { success: false, message: "Clinic not found" };
    }
    
    // Fetch upcoming appointments
    const appointments = await fetchUpcomingAppointments(context, clinicId);
    
    // Process appointments and send reminders
    const results = await processAppointmentsAndSendReminders(context, appointments, clinic);
    
    return {
        success: true,
        message: "Reminders processed successfully",
        results
    };
});

const fetchClinics = async (context, clinicId) => {
    return await context.run('get clinic', () => {
        return Clinic.findOne({_id: clinicId})
            .populate('user', 'first_name')
            .exec();
    });
};

const fetchUpcomingAppointments = async (context, clinicId) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const dayAfterTomorrow = new Date(tomorrow);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);
    
    return await context.run('get upcoming appointments', () => {
        return Appointment.find({
            clinicId: clinicId,
            dateTime: {
                $gte: tomorrow,
                $lt: dayAfterTomorrow
            },
            status: 'scheduled'
        })
        .populate('petId')
        .populate({
            path: 'petId',
            populate: {
                path: 'ownerId'
            }
        })
        .populate('vetId')
        .exec();
    });
};

const processAppointmentsAndSendReminders = async (context, appointments, clinic) => {
    const results = [];
    
    for (const appointment of appointments) {
        const result = await context.run(`send reminder for appointment ${appointment._id}`, async () => {
            // Logic to send reminder (email/SMS)
            // This would integrate with your notification service
            
            return {
                appointmentId: appointment._id,
                petName: appointment.petId?.name,
                ownerName: appointment.petId?.ownerId?.firstName + ' ' + appointment.petId?.ownerId?.lastName,
                ownerEmail: appointment.petId?.ownerId?.email,
                ownerPhone: appointment.petId?.ownerId?.phoneNumber,
                appointmentTime: appointment.dateTime,
                reminderSent: true
            };
        });
        
        results.push(result);
    }
    
    return results;
};

export const scheduleAppointmentReminders = serve(async (context) => {
    const { appointmentId } = context.requestPayload;
    
    // Fetch appointment details
    const appointment = await context.run('get appointment', () => {
        return Appointment.findById(appointmentId)
            .populate('petId', 'name')
            .populate({
                path: 'petId',
                populate: {
                    path: 'ownerId',
                    select: 'firstName lastName email phoneNumber'
                }
            })
            .populate('clinicId', 'clinicName email phoneNumber')
            .lean();
    });
    
    if (!appointment) {
        return { success: false, message: "Appointment not found" };
    }
    
    const owner = appointment.petId.ownerId;
    const pet = appointment.petId;
    const clinic = appointment.clinicId;
    const appointmentDate = new Date(appointment.dateTime);
    
    // Schedule reminders at different intervals
    const reminders = [
        {
            type: "24h_before",
            scheduledFor: new Date(appointmentDate.getTime() - 24 * 60 * 60 * 1000),
            message: `Reminder: ${pet.name} has an appointment tomorrow at ${appointmentDate.toLocaleTimeString()} with ${clinic.clinicName}.`
        },
        {
            type: "1h_before",
            scheduledFor: new Date(appointmentDate.getTime() - 60 * 60 * 1000),
            message: `Reminder: ${pet.name}'s appointment is in 1 hour at ${clinic.clinicName}.`
        }
    ];
    
    // Schedule each reminder
    const results = await Promise.all(reminders.map(reminder => {
        return context.run(`schedule ${reminder.type}`, () => {
            // In a real implementation, this would send an email or SMS
            console.log(`Scheduled reminder for ${owner.email}: ${reminder.message}`);
            return {
                type: reminder.type,
                scheduledFor: reminder.scheduledFor,
                recipient: owner.email,
                success: true
            };
        });
    }));
    
    return {
        success: true,
        message: "Appointment reminders scheduled successfully",
        reminders: results
    };
});
