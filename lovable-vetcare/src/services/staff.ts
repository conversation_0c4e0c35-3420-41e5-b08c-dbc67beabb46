import { Staff, PaginationData } from '@/store/types';
import { api } from './api';
import { useStore } from '@/store';

export interface StaffResponse {
  success: boolean;
  status: number;
  message: string;
  data: Staff | null;
}

export interface StaffsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Staff[];
    pagination: PaginationData;
  };
}

export interface StaffSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: number;
  clinicId?: string | number;
  [key: string]: any;
}

export interface UpdateStaffDto {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  roleId?: string | number;
  clinicId?: string | number;
  primaryClinicId?: string | number;
  additionalClinics?: (string | number)[];
  status?: number;
  [key: string]: any;
}

// Helper function to get clinic context
const getClinicId = () => {
  const state = useStore.getState();
  return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

/**
 * Get staff with pagination and filtering
 */
export const getStaff = async (params: StaffSearchParams = {}): Promise<StaffsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    // Always include clinicId if available and not already specified
    const clinicId = getClinicId();
    if (clinicId && !params.clinicId) {
      params.clinicId = clinicId;
    }

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    const response = await api.get<StaffsResponse>(`/staff?${queryParams.toString()}`);

    // Process staff data to ensure it has the expected format
    if (response.success && response.data?.data) {
      // Map through staff to ensure they have a name property
      response.data.data = response.data.data.map(staff => {
        // If staff doesn't have a name property, create one from userId if available
        if (!staff.name) {
          if (staff.userId && typeof staff.userId !== 'string') {
            staff.name = `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
          } else if (staff.firstName || staff.lastName) {
            staff.name = `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
          } else {
            staff.name = `Staff ID: ${staff.staffId}`;
          }
        }
        return staff;
      });
    }

    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch staff',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get all staff without pagination (for dropdowns, etc.)
 */
export const getAllStaff = async (): Promise<StaffsResponse> => {
  try {
    // Set a high limit to get all staff
    const response = await api.get<StaffsResponse>('/staff?limit=100');

    // Process staff data to ensure it has the expected format
    if (response.success && response.data?.data) {
      // Map through staff to ensure they have a name property
      response.data.data = response.data.data.map(staff => {
        // If staff doesn't have a name property, create one from userId if available
        if (!staff.name) {
          if (staff.userId && typeof staff.userId !== 'string') {
            staff.name = `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
          } else if (staff.firstName || staff.lastName) {
            staff.name = `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
          } else {
            staff.name = `Staff ID: ${staff.staffId}`;
          }
        }
        return staff;
      });
    }

    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch all staff',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 100,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

export const getStaffById = async (staffId: string | number): Promise<StaffResponse> => {
  try {
    return await api.get<StaffResponse>(`/staff/${staffId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch staff member',
      data: null
    };
  }
};

export const updateStaff = async (staffId: string | number, updateData: UpdateStaffDto): Promise<StaffResponse> => {
  try {
    return await api.put<StaffResponse>(`/staff/${staffId}`, updateData);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update staff member',
      data: null
    };
  }
};

export const createStaff = async (staffData: Partial<Staff>): Promise<StaffResponse> => {
  try {
    return await api.post<StaffResponse>('/staff', staffData);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create staff member',
      data: null
    };
  }
};

export const deleteStaff = async (staffId: string | number): Promise<StaffResponse> => {
  try {
    return await api.delete<StaffResponse>(`/staff/${staffId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete staff member',
      data: null
    };
  }
};

/**
 * Get clinic owners for assignment to clinics
 */
export const getClinicOwners = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: number;
  hasClinic?: boolean;
}): Promise<StaffsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status !== undefined) queryParams.append('status', params.status.toString());
    if (params?.hasClinic !== undefined) queryParams.append('hasClinic', params.hasClinic.toString());

    const url = `/staff/owners${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await api.get<StaffsResponse>(url);

    // Process clinic owners data to ensure it has the expected format
    if (response.success && response.data?.data) {
      response.data.data = response.data.data.map(owner => {
        // Ensure fullName property exists
        if (!owner.fullName) {
          owner.fullName = `${owner.firstName || ''} ${owner.lastName || ''}`.trim();
        }

        // Ensure name property exists for compatibility
        if (!owner.name) {
          owner.name = owner.fullName || `Staff ID: ${owner.staffId}`;
        }

        return owner;
      });
    }

    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch clinic owners',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};
