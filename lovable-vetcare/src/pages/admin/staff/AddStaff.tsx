
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createStaff } from "@/services/staff";
import { useToast } from "@/components/ui/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getClinics } from "@/services/clinics";
import { getRoles } from "@/services/roles";
import { useState } from "react";
import { Check } from "lucide-react";

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
  roleId: z.string().min(1, "Role is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  employmentDate: z.string().min(1, "Employment date is required"),
  salary: z.string().min(1, "Salary is required"),
  clinicId: z.string().min(1, "Clinic is required")
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

const AddStaff = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedClinic, setSelectedClinic] = useState<string>("");

  // Fetch clinics for cards
  const { data: clinicsData, isLoading: isLoadingClinics } = useQuery({
    queryKey: ["clinics"],
    queryFn: async () => {
      const response = await getClinics();
      return response.data;
    }
  });

  // Fetch roles for dropdown
  const { data: rolesData } = useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await getRoles();
      return response.data;
    }
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      roleId: "",
      jobTitle: "",
      employmentDate: new Date().toISOString().split('T')[0],
      salary: "",
      clinicId: ""
    },
  });

  // Handle clinic selection
  const handleClinicSelect = (clinicId: string) => {
    setSelectedClinic(clinicId);
    form.setValue("clinicId", clinicId);
  };

  const handleSubmit = async (values: FormValues) => {
    try {
      // Prepare staff data according to the backend's expected format
      const staffData = {
        firstName: values.firstName,
        middleName: values.middleName || "",
        lastName: values.lastName,
        email: values.email,
        phoneNumber: values.phoneNumber,
        password: values.password,
        roleId: parseInt(values.roleId) || values.roleId, // Try to convert to number first
        jobTitle: values.jobTitle,
        employmentDate: new Date(values.employmentDate),
        salary: Number(values.salary),
        clinicId: parseInt(values.clinicId) || values.clinicId // Try to convert to number first
      };

      // Call the API to create the staff member
      const result = await createStaff(staffData);

      if (!result.success) {
        throw new Error(result.message || "Failed to create staff member");
      }

      toast({
        title: "Success",
        description: "Staff member created successfully",
      });

      // Invalidate the staff query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      navigate("/admin/staff");
    } catch (error: any) {
      console.error("Error creating staff:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to create staff member",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-8 space-y-6">
      {/* Clinic Selection Cards */}
      <Card>
        <CardHeader>
          <CardTitle>Select Clinic</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingClinics ? (
            <div>Loading clinics...</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {clinicsData?.data?.map((clinic: any) => (
                <Card
                  key={clinic.clinicId || clinic._id}
                  className={`cursor-pointer transition-all ${selectedClinic === (clinic.clinicId || clinic._id) ? 'border-primary border-2' : 'hover:border-gray-300'}`}
                  onClick={() => handleClinicSelect(clinic.clinicId || clinic._id)}
                >
                  <CardContent className="p-4 flex items-start justify-between">
                    <div className="pt-2">
                      <h3 className="font-medium">{clinic.clinicName || clinic.name}</h3>
                      <p className="text-sm text-muted-foreground">{clinic.address}</p>
                    </div>
                    {selectedClinic === (clinic.clinicId || clinic._id) && (
                      <div className="bg-primary rounded-full p-1">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Staff Form */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Staff Member</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="roleId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                          <SelectContent>
                            {rolesData?.data.map((role: any) => (
                              <SelectItem key={role.roleId} value={role.roleId.toString()}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter job title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="employmentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employment Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salary</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter salary" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Confirm password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Hidden clinic ID field */}
                <input type="hidden" {...form.register("clinicId")} />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => navigate("/admin/staff")}
                  type="button"
                >
                  Cancel
                </Button>

                <Button
                type="submit">
                  Save Staff Member
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddStaff;
