import Pet from '../models/pet.model.js';
import Client from "../models/client.model.js";
import Species from "../models/species.model.js";
import Breed from "../models/breed.model.js";
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

export const registerPet = async (req, res) => {
    try {
        const { ownerId, clientId, petName, speciesId, breedId, color, lifeStatus, gender, microchipId, weight, dateOfBirth } = req.body;

        // Use clientId if provided, otherwise fall back to ownerId for backward compatibility
        let ownerIdToUse = clientId || ownerId;

        if (!ownerIdToUse) {
            return sendResponse(res, 400, false, "Client ID is required");
        }

        // Convert to number if it's a string
        const clientIdNum = !isNaN(ownerIdToUse) ? parseInt(ownerIdToUse) : ownerIdToUse;

        // Find owner, species, breed and check microchip in parallel
        const [client, existingPet, species, breed] = await Promise.all([
            Client.findOne({ clientId: clientIdNum }).lean(),
            Pet.findOne({ microchipId }).lean(),
            Species.findOne({ speciesId: parseInt(speciesId) }).lean(),
            Breed.findOne({ breedId: parseInt(breedId) }).lean()
        ]);

        if (!client) {
            return sendResponse(res, 404, false, "Pet owner not found with clientId: " + clientIdNum);
        }

        if (existingPet) {
            return sendResponse(res, 400, false, "Microchip already registered");
        }

        if (!species) {
            return sendResponse(res, 404, false, "Species not found with speciesId: " + speciesId);
        }

        if (!breed) {
            return sendResponse(res, 404, false, "Breed not found with breedId: " + breedId);
        }

        const pet = await Pet.create({
            owner: client.clientId, // Use clientId number, not the entire client object
            petName,
            speciesId: parseInt(speciesId),
            breedId: parseInt(breedId),
            color,
            lifeStatus: lifeStatus || "alive",
            gender,
            microchipId,
            weight,
            dateOfBirth,
        });

        return sendResponse(res, 201, true, "Pet registered successfully", pet);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all pets with pagination and optional filters
 * @param {Object} req - Request object with query parameters
 * @param {Object} res - Response object
 */
export const getAllPets = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "createdAt",
            sortOrder = "desc",
            search,
            ownerId,
            clientId,
            speciesId,
            breedId,
            gender,
            lifeStatus
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};

        // Handle clientId parameter - Pet model uses 'owner' field, not 'clientId'
        if (clientId) {
            // Convert to number if it's a string
            query.owner = !isNaN(clientId) ? parseInt(clientId) : clientId;
        } else if (ownerId) {
            // For backward compatibility, use ownerId as owner
            query.owner = !isNaN(ownerId) ? parseInt(ownerId) : ownerId;
        }

        if (speciesId) query.speciesId = !isNaN(speciesId) ? parseInt(speciesId) : speciesId;
        if (breedId) query.breedId = !isNaN(breedId) ? parseInt(breedId) : breedId;
        if (gender) query.gender = gender;
        if (lifeStatus) query.lifeStatus = lifeStatus;

        if (search) {
            query.$or = [
                { petName: { $regex: search, $options: "i" } },
                { microchipId: { $regex: search, $options: "i" } },
                { color: { $regex: search, $options: "i" } }
            ];
        }

        const [pets, totalCount] = await Promise.all([
            Pet.find(query)
                .populate({
                    path: 'owner',
                    select: 'firstName middleName lastName email phoneNumber address clientStatus',
                    localField: 'owner',
                    foreignField: 'clientId'
                })
                .populate({
                    path: 'speciesId',
                    select: 'speciesName',
                    localField: 'speciesId',
                    foreignField: 'speciesId'
                })
                .populate({
                    path: 'breedId',
                    select: 'breedName',
                    localField: 'breedId',
                    foreignField: 'breedId'
                })
                .select('-__v')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Pet.countDocuments(query)
        ]);

        // Enrich pet data with owner information
        const enrichedPets = pets.map(pet => {
            // Rename owner to owner in the response
            const { owner, speciesId, breedId, ...rest } = pet;

            return {
                ...rest,
                owner: owner,
                species: speciesId,
                breed: breedId
            };
        });

        return sendResponse(
            res,
            200,
            true,
            "Pets found successfully",
            paginateResults(enrichedPets, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Search pets by multiple criteria
 */
export const getPetsByQuery = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            ownerId,
            clientId,
            petName,
            species,
            microchipId,
            breed,
            gender
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        let query = {};

        // Handle clientId parameter - Pet model uses 'owner' field, not 'clientId'
        if (clientId) {
            // Convert to number if it's a string
            query.owner = !isNaN(clientId) ? parseInt(clientId) : clientId;
        } else if (ownerId) {
            // For backward compatibility, use ownerId as owner
            query.owner = !isNaN(ownerId) ? parseInt(ownerId) : ownerId;
        }

        if (petName) query.petName = { $regex: petName, $options: "i" };
        if (species) query.speciesId = !isNaN(species) ? parseInt(species) : species;
        if (microchipId) query.microchipId = microchipId;
        if (breed) query.breedId = !isNaN(breed) ? parseInt(breed) : breed;
        if (gender) query.gender = gender;

        // Execute queries in parallel
        const [pets, totalCount] = await Promise.all([
            Pet.find(query)
                .populate({
                    path: 'owner',
                    select: 'firstName middleName lastName email phoneNumber address clientStatus',
                    localField: 'owner',
                    foreignField: 'clientId'
                })
                .populate({
                    path: 'speciesId',
                    select: 'speciesName',
                    localField: 'speciesId',
                    foreignField: 'speciesId'
                })
                .populate({
                    path: 'breedId',
                    select: 'breedName',
                    localField: 'breedId',
                    foreignField: 'breedId'
                })
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Pet.countDocuments(query)
        ]);

        // Enrich pet data with owner information
        const enrichedPets = pets.map(pet => {
            // Rename owner to owner in the response
            const { owner, speciesId, breedId, ...rest } = pet;

            return {
                ...rest,
                owner: owner,
                species: speciesId,
                breed: breedId
            };
        });

        return sendResponse(
            res,
            200,
            true,
            "Pets found successfully",
            paginateResults(enrichedPets, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getPet = async (req, res) => {
    try {
        const petId = parseInt(req.params.petId);

        const pet = await Pet.findOne({ petId })
            .populate({
                path: 'owner',
                select: 'firstName middleName lastName email phoneNumber',
                localField: 'owner',
                foreignField: 'clientId'
            })
            .populate({
                path: 'speciesId',
                select: 'speciesName',
                localField: 'speciesId',
                foreignField: 'speciesId'
            })
            .populate({
                path: 'breedId',
                select: 'breedName',
                localField: 'breedId',
                foreignField: 'breedId'
            })
            .lean();

        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Rename owner to client and speciesId/breedId to species/breed
        const { owner, speciesId, breedId, ...rest } = pet;
        const enrichedPet = {
            ...rest,
            client: owner,
            species: speciesId,
            breed: breedId
        };

        return sendResponse(res, 200, true, "Pet found successfully", enrichedPet);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updatePet = async (req, res) => {
    try {
        const petId = parseInt(req.params.petId);

        // If speciesId or breedId is provided, verify they exist
        if (req.body.speciesId || req.body.breedId) {
            const promises = [];

            if (req.body.speciesId) {
                const speciesId = !isNaN(req.body.speciesId) ? parseInt(req.body.speciesId) : req.body.speciesId;
                promises.push(Species.findOne({ speciesId }).lean());
            }

            if (req.body.breedId) {
                const breedId = !isNaN(req.body.breedId) ? parseInt(req.body.breedId) : req.body.breedId;
                promises.push(Breed.findOne({ breedId }).lean());
            }

            const results = await Promise.all(promises);

            if (req.body.speciesId && !results[0]) {
                return sendResponse(res, 404, false, "Species not found");
            }

            if (req.body.breedId && !results[req.body.speciesId ? 1 : 0]) {
                return sendResponse(res, 404, false, "Breed not found");
            }
        }

        // Convert clientId to owner field if provided
        if (req.body.clientId && !isNaN(req.body.clientId)) {
            req.body.owner = parseInt(req.body.clientId);
            delete req.body.clientId; // Remove clientId since Pet model uses 'owner'
        }

        // Convert speciesId to number if provided
        if (req.body.speciesId && !isNaN(req.body.speciesId)) {
            req.body.speciesId = parseInt(req.body.speciesId);
        }

        // Convert breedId to number if provided
        if (req.body.breedId && !isNaN(req.body.breedId)) {
            req.body.breedId = parseInt(req.body.breedId);
        }

        const updatedPet = await Pet.findOneAndUpdate(
            { petId },
            { $set: req.body },
            { new: true, runValidators: true }
        )
        .populate({
            path: 'owner',
            select: 'firstName middleName lastName email phoneNumber',
            localField: 'owner',
            foreignField: 'clientId'
        })
        .populate({
            path: 'speciesId',
            select: 'speciesName',
            localField: 'speciesId',
            foreignField: 'speciesId'
        })
        .populate({
            path: 'breedId',
            select: 'breedName',
            localField: 'breedId',
            foreignField: 'breedId'
        })
        .lean();

        if (!updatedPet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Rename owner to client and speciesId/breedId to species/breed
        const { owner, speciesId, breedId, ...rest } = updatedPet;
        const enrichedPet = {
            ...rest,
            client: owner,
            species: speciesId,
            breed: breedId
        };

        return sendResponse(res, 200, true, "Pet updated successfully", enrichedPet);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

export const deletePet = async (req, res) => {
    try {
        const petId = parseInt(req.params.petId);

        const pet = await Pet.findOneAndDelete({ petId });

        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        return sendResponse(res, 200, true, "Pet deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
