import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getClientById } from "@/services/api";
import { getPetsByOwner } from "@/services/pets";
import { getAppointmentsByClient } from "@/services/appointments";
import { getInvoicesByClient } from "@/services/invoices";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Calendar, Clock, Mail, MapPin, Phone, Edit, Plus, ChevronRight } from "lucide-react";
import LoadingPage from "@/components/common/LoadingPage";
import AddPetModal from "./components/AddPetModal";

const ClientProfile = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isAddPetModalOpen, setIsAddPetModalOpen] = useState(false);

  // Fetch client data
  const { data: clientResponse, isLoading: isClientLoading } = useQuery({
    queryKey: ["client", id],
    queryFn: () => getClientById(id || ""),
    enabled: !!id
  });

  // Fetch client's pets
  const { data: petsResponse, isLoading: isPetsLoading } = useQuery({
    queryKey: ["pets", id],
    queryFn: () => getPetsByOwner(id || ""),
    enabled: !!id
  });

  // Fetch client's appointments
  const { data: appointmentsResponse, isLoading: isAppointmentsLoading } = useQuery({
    queryKey: ["appointments", id],
    queryFn: () => getAppointmentsByClient(id || ""),
    enabled: !!id
  });

  // Fetch client's invoices
  const { data: invoicesResponse, isLoading: isInvoicesLoading } = useQuery({
    queryKey: ["invoices", id],
    queryFn: () => getInvoicesByClient(id || ""),
    enabled: !!id
  });

  const client = clientResponse?.data?.data;
  // Handle different response structures
  const pets = petsResponse?.data?.data?.data ||
               petsResponse?.data?.data ||
               [];
  const appointments = appointmentsResponse?.data?.data?.data ||
                      appointmentsResponse?.data?.data ||
                      [];
  const invoices = invoicesResponse?.data?.data?.data ||
                  invoicesResponse?.data?.data ||
                  [];

  if (isClientLoading || isPetsLoading || isAppointmentsLoading || isInvoicesLoading) {
    return <LoadingPage />;
  }

  if (!client) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h1 className="text-2xl font-semibold mb-4">Client not found</h1>
        <Button onClick={() => navigate("/clients")}>Back to Clients</Button>
      </div>
    );
  }

  // Get client initials for avatar fallback
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Client Profile</h1>
        <Button variant="outline" onClick={() => navigate("/clients")}>
          Back to Clients
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Client Info Card - Takes 3/4 of the width */}
        <Card className="lg:col-span-3">
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={client.profilePicture} alt={`${client.firstName} ${client.lastName}`} />
                <AvatarFallback className="text-lg">{getInitials(client.firstName, client.lastName)}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">{client.firstName} {client.lastName}</CardTitle>
                <CardDescription>Client since {formatDate(client.createdAt)}</CardDescription>
              </div>
            </div>
            <Button variant="outline" size="icon" onClick={() => navigate(`/clients/${client.accountId}/edit`)}>
              <Edit className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{client.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{client.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{client.address || "No address provided"}</span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Emergency Contact</h3>
                <p>{client.emergencyContactName || "None"}</p>
                {client.emergencyContactPhone && (
                  <div className="flex items-center gap-2 mt-1">
                    <Phone className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm">{client.emergencyContactPhone}</span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Notes</h3>
                <p className="text-sm">{client.notes || "No notes"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Card - Takes 1/4 of the width */}
        <Card>
          <CardHeader>
            <CardTitle>Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Pets</span>
              <Badge variant="outline">{pets.length}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Appointments</span>
              <Badge variant="outline">{appointments.length}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Invoices</span>
              <Badge variant="outline">{invoices.length}</Badge>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full" onClick={() => setIsAddPetModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Pet
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Carousel for upcoming appointments and recent invoices */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Upcoming & Recent</h2>
        <Carousel className="w-full">
          <CarouselContent>
            {/* Upcoming Appointments */}
            {appointments.slice(0, 3).map((appointment) => (
              <CarouselItem key={appointment._id} className="md:basis-1/2 lg:basis-1/3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Appointment</CardTitle>
                    <CardDescription>
                      {appointment.appointmentType?.name || "General Checkup"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDate(appointment.date)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.time}</span>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="ghost" size="sm" className="w-full" onClick={() => navigate(`/appointments/${appointment._id}`)}>
                      View Details <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              </CarouselItem>
            ))}

            {/* Recent Invoices */}
            {invoices.slice(0, 3).map((invoice) => (
              <CarouselItem key={invoice._id} className="md:basis-1/2 lg:basis-1/3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Invoice #{invoice.invoiceNumber}</CardTitle>
                    <CardDescription>
                      {formatDate(invoice.date)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Amount:</span>
                      <span>${invoice.totalAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Status:</span>
                      <Badge variant={invoice.status === "paid" ? "success" : "destructive"}>
                        {invoice.status}
                      </Badge>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="ghost" size="sm" className="w-full" onClick={() => navigate(`/invoices/${invoice._id}`)}>
                      View Invoice <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>

      {/* Pets Section */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Pets</h2>
          <Button onClick={() => setIsAddPetModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Pet
          </Button>
        </div>

        {pets.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <p className="text-muted-foreground mb-4">No pets found for this client</p>
              <Button onClick={() => setIsAddPetModalOpen(true)}>
                <Plus className="mr-2 h-4 w-4" /> Add Pet
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {pets.map((pet) => (
              <Card key={pet._id} className="overflow-hidden">
                <div className="h-40 bg-gray-100 relative">
                  {pet.profilePicture ? (
                    <img
                      src={pet.profilePicture}
                      alt={pet.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-muted-foreground">No image</span>
                    </div>
                  )}
                  <Badge
                    className="absolute top-2 right-2"
                    variant={pet.lifeStatus === "alive" ? "default" : "destructive"}
                  >
                    {pet.lifeStatus}
                  </Badge>
                </div>
                <CardHeader>
                  <CardTitle>{pet.name}</CardTitle>
                  <CardDescription>
                    {pet.breed?.breedName || pet.breedId?.breedName || "Unknown breed"} • {pet.gender}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Color</span>
                    <span>{pet.color}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Weight</span>
                    <span>{pet.weight} kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Date of Birth</span>
                    <span>{formatDate(pet.dateOfBirth)}</span>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full" onClick={() => navigate(`/pets/${pet._id}`)}>
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Add Pet Modal */}
      <AddPetModal
        open={isAddPetModalOpen}
        onOpenChange={setIsAddPetModalOpen}
        client={client}
      />
    </div>
  );
};

export default ClientProfile;
