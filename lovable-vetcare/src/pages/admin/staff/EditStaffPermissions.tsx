import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { getStaffById } from "@/services/staff";
import { getPermissions } from "@/services/permissions";
import { getRoles } from "@/services/roles";
import {
  getStaffPermissions,
  grantSpecialPermissions,
  revokePermissions,
  resetStaffPermissions
} from "@/services/staffPermissions";
import { Permission, Role, Staff } from "@/store/types";
import LoadingPage from "@/components/common/LoadingPage";
import { ArrowLeft, RefreshCw } from "lucide-react";

const EditStaffPermissions = () => {
  const { _id } = useParams<{ _id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [selectedSpecialPermissions, setSelectedSpecialPermissions] = useState<number[]>([]);
  const [selectedRevokedPermissions, setSelectedRevokedPermissions] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState("special");

  // Fetch staff details
  const { data: staff, isLoading: isStaffLoading } = useQuery({
    queryKey: ["staff", _id],
    queryFn: async () => {
      const response = await getStaffById(_id!);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!_id
  });

  // Fetch staff permissions
  const { data: staffPermissions, isLoading: isPermissionsLoading, refetch: refetchPermissions } = useQuery({
    queryKey: ["staffPermissions", _id],
    queryFn: async () => {
      const response = await getStaffPermissions(_id!);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!_id
  });

  // Fetch all permissions
  const { data: allPermissions = [], isLoading: isAllPermissionsLoading } = useQuery({
    queryKey: ["permissions"],
    queryFn: async () => {
      const response = await getPermissions();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data.data;
    }
  });

  // Fetch all roles
  const { data: roles = [], isLoading: isRolesLoading } = useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await getRoles();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data.data;
    }
  });

  // Initialize selected permissions when data is loaded
  useEffect(() => {
    if (staffPermissions) {
      setSelectedSpecialPermissions(staffPermissions.specialPermissions || []);
      setSelectedRevokedPermissions(staffPermissions.revokedPermissions || []);
    }
  }, [staffPermissions]);

  // Handle adding special permissions
  const handleAddSpecialPermissions = async () => {
    if (!_id || selectedSpecialPermissions.length === 0) return;

    try {
      // Get only the new permissions that aren't already in the staff's special permissions
      const currentSpecialPermissions = staffPermissions?.specialPermissions || [];
      const newPermissions = selectedSpecialPermissions.filter(
        p => !currentSpecialPermissions.includes(p)
      );

      if (newPermissions.length === 0) {
        toast({
          title: "No changes",
          description: "No new permissions were selected",
        });
        return;
      }

      const response = await grantSpecialPermissions(_id, newPermissions);

      if (!response.success) {
        throw new Error(response.message);
      }

      await refetchPermissions();

      toast({
        title: "Success",
        description: "Special permissions granted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to grant special permissions",
        variant: "destructive",
      });
    }
  };

  // Handle revoking permissions
  const handleRevokePermissions = async () => {
    if (!_id || selectedRevokedPermissions.length === 0) return;

    try {
      // Get only the new permissions to revoke that aren't already revoked
      const currentRevokedPermissions = staffPermissions?.revokedPermissions || [];
      const newRevokedPermissions = selectedRevokedPermissions.filter(
        p => !currentRevokedPermissions.includes(p)
      );

      if (newRevokedPermissions.length === 0) {
        toast({
          title: "No changes",
          description: "No new permissions were selected to revoke",
        });
        return;
      }

      const response = await revokePermissions(_id, newRevokedPermissions);

      if (!response.success) {
        throw new Error(response.message);
      }

      await refetchPermissions();

      toast({
        title: "Success",
        description: "Permissions revoked successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to revoke permissions",
        variant: "destructive",
      });
    }
  };

  // Handle resetting permissions to role defaults
  const handleResetPermissions = async () => {
    if (!_id) return;

    try {
      const response = await resetStaffPermissions(_id);

      if (!response.success) {
        throw new Error(response.message);
      }

      await refetchPermissions();

      // Reset selected permissions
      setSelectedSpecialPermissions([]);
      setSelectedRevokedPermissions([]);

      toast({
        title: "Success",
        description: "Permissions reset to role defaults successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to reset permissions",
        variant: "destructive",
      });
    }
  };

  // Toggle permission selection for special permissions
  const toggleSpecialPermission = (permissionId: number) => {
    setSelectedSpecialPermissions(prev =>
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // Toggle permission selection for revoked permissions
  const toggleRevokedPermission = (permissionId: number) => {
    setSelectedRevokedPermissions(prev =>
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // Get staff name
  const getStaffName = (staff: Staff | null) => {
    if (!staff) return "Unknown Staff";

    if (staff.name) return staff.name;

    if (typeof staff.userId === 'object' && staff.userId) {
      return `${staff.userId.firstName || ''} ${staff.userId.lastName || ''}`.trim();
    }

    return `Staff ID: ${staff.staffId || staff._id}`;
  };

  // Get role name
  const getRoleName = (staff: Staff | null) => {
    if (!staff) return "Unknown Role";

    if (typeof staff.roleId === 'object' && staff.roleId) {
      return staff.roleId.name || "Unknown Role";
    }

    // Find role by ID
    const roleId = typeof staff.roleId === 'number' ? staff.roleId :
                  typeof staff.roleId === 'string' ? parseInt(staff.roleId) : null;

    if (roleId) {
      const role = roles.find(r => r.roleId === roleId);
      return role?.name || "Unknown Role";
    }

    return "Unknown Role";
  };

  if (isStaffLoading || isPermissionsLoading || isAllPermissionsLoading || isRolesLoading) {
    return <LoadingPage />;
  }

  if (!staff) {
    return (
      <div className="p-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">Staff member not found</div>
            <Button onClick={() => navigate("/admin/staff")} className="mt-4">
              Back to Staff List
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate("/admin/staff")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">Edit Staff Permissions</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Staff Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Name</Label>
              <div className="font-medium">{getStaffName(staff)}</div>
            </div>
            <div>
              <Label>Role</Label>
              <div className="font-medium">{getRoleName(staff)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Permissions Management</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => refetchPermissions()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="destructive" size="sm" onClick={handleResetPermissions}>
              Reset to Role Defaults
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="special">Add Special Permissions</TabsTrigger>
              <TabsTrigger value="revoke">Revoke Role Permissions</TabsTrigger>
            </TabsList>

            <TabsContent value="special">
              <div className="mb-4">
                <p className="text-sm text-gray-500 mb-2">
                  Special permissions are additional permissions granted to this staff member beyond their role.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                  {allPermissions.map((permission) => {
                    const isInRole = staffPermissions?.permissions.some(p => p.permissionId === permission.permissionId);
                    const isSpecial = staffPermissions?.specialPermissions.includes(permission.permissionId);

                    // Skip if already in role (no need to add as special)
                    if (isInRole && !isSpecial) return null;

                    return (
                      <div key={permission.permissionId} className="flex items-start space-x-2">
                        <Checkbox
                          id={`special-${permission.permissionId}`}
                          checked={selectedSpecialPermissions.includes(permission.permissionId)}
                          onCheckedChange={() => toggleSpecialPermission(permission.permissionId)}
                          disabled={isSpecial} // Disable if already a special permission
                        />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor={`special-${permission.permissionId}`}
                            className={isSpecial ? "text-gray-400" : ""}
                          >
                            {permission.name}
                            {isSpecial && " (Already granted)"}
                          </Label>
                          <p className="text-sm text-gray-500">{permission.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <Button onClick={handleAddSpecialPermissions}>
                Grant Special Permissions
              </Button>
            </TabsContent>

            <TabsContent value="revoke">
              <div className="mb-4">
                <p className="text-sm text-gray-500 mb-2">
                  Revoked permissions are permissions that are part of the staff member's role but have been explicitly removed.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                  {staffPermissions?.permissions.map((permission) => {
                    const isRevoked = staffPermissions?.revokedPermissions.includes(permission.permissionId);

                    return (
                      <div key={permission.permissionId} className="flex items-start space-x-2">
                        <Checkbox
                          id={`revoke-${permission.permissionId}`}
                          checked={selectedRevokedPermissions.includes(permission.permissionId)}
                          onCheckedChange={() => toggleRevokedPermission(permission.permissionId)}
                          disabled={isRevoked} // Disable if already revoked
                        />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor={`revoke-${permission.permissionId}`}
                            className={isRevoked ? "text-gray-400" : ""}
                          >
                            {permission.name}
                            {isRevoked && " (Already revoked)"}
                          </Label>
                          <p className="text-sm text-gray-500">{permission.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <Button onClick={handleRevokePermissions}>
                Revoke Selected Permissions
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditStaffPermissions;
