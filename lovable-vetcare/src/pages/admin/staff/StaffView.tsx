import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getStaffById } from "@/services/staff";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pencil, ArrowLeft, Phone, Mail, Calendar, DollarSign, Building, User } from "lucide-react";
import LoadingPage from "@/components/common/LoadingPage";
import { useToast } from "@/components/ui/use-toast";
import { useEffect } from "react";
import { format } from "date-fns";

const StaffView = () => {
  const { _id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const { data: staffData, isLoading, error } = useQuery({
    queryKey: ["staff", _id],
    queryFn: () => getStaffById(_id!),
    enabled: !!_id,
  });

  // Handle error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to load staff information",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const staff = staffData?.data;

  if (isLoading) {
    return <LoadingPage />;
  }

  if (!staff) {
    return (
      <div className="p-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p>Staff member not found</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => navigate("/admin/staff")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Staff List
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6">
      <div className="flex justify-between items-center">
        <Button 
          variant="outline" 
          onClick={() => navigate("/admin/staff")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <Button 
          onClick={() => navigate(`/admin/staff/${_id}/edit`)}
        >
          <Pencil className="mr-2 h-4 w-4" /> Edit
        </Button>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Staff Profile</CardTitle>
          {staff.status === 1 ? (
            <Badge variant="default" className="bg-green-500">
              Active
            </Badge>
          ) : (
            <Badge variant="secondary" className="bg-gray-400">
              Inactive
            </Badge>
          )}
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="w-full md:w-1/3">
              <div className="aspect-square rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-4xl">
                {staff.userId?.firstName?.charAt(0) || staff.name?.charAt(0) || "U"}
              </div>
            </div>
            <div className="w-full md:w-2/3 space-y-4">
              <h2 className="text-2xl font-bold">
                {staff.userId?.firstName} {staff.userId?.lastName}
                {!staff.userId?.firstName && staff.name}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Job Title</p>
                    <p>{staff.jobTitle || "N/A"}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Role</p>
                    <p>{typeof staff.roleId === 'object' ? staff.roleId?.name : (staff.role || "Unknown")}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p>{staff.userId?.email || staff.email || "N/A"}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p>{staff.userId?.phoneNumber || staff.phone || "N/A"}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Employment Date</p>
                    <p>{staff.employmentDate ? format(new Date(staff.employmentDate), 'PPP') : "N/A"}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Salary</p>
                    <p>{staff.salary ? `$${staff.salary.toLocaleString()}` : "N/A"}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="text-lg font-medium mb-2">Clinic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Primary Clinic</p>
                <p>
                  {typeof staff.clinicId === 'object' 
                    ? (staff.clinicId?.clinicName || staff.clinicId?.name) 
                    : "Unknown"}
                </p>
              </div>
              
              {staff.isClinicOwner && (
                <div>
                  <p className="text-sm text-gray-500">Role</p>
                  <Badge variant="outline" className="bg-blue-100">Clinic Owner</Badge>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StaffView;
