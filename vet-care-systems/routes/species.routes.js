import {Router} from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createSpecies, deleteSpecies,
    getAllSpecies, updateSpecies,
} from "../controllers/species.controller.js";


const speciesRouter = Router();

// Protected routes
speciesRouter.use(verifyToken);

speciesRouter.post('/', createSpecies);
speciesRouter.get('/', getAllSpecies);
speciesRouter.put('/:id', updateSpecies);
speciesRouter.delete('/:id', deleteSpecies);

export default speciesRouter;