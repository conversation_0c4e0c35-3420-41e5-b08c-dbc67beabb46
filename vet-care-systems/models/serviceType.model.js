import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const serviceTypeSchema = new mongoose.Schema({
    // Auto-increment ID
    serviceTypeId: {
        type: Number,
        index: true,
        unique: true,
    },
    name: {
        type: String,
        required: [true, "Service type name is required"],
        unique: true,
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    category: {
        type: String,
        enum: ["vaccination", "treatment", "surgery", "grooming", "checkup", "medication", "other"],
        required: [true, "Service category is required"],
    },
    minCharge: {
        type: Number,
        required: [true, "Minimum charge is required"],
        min: 0,
    },
    maxCharge: {
        type: Number,
        required: [true, "Maximum charge is required"],
        min: 0,
        validate: {
            validator: function(value) {
                return value >= this.minCharge;
            },
            message: "Maximum charge must be greater than or equal to minimum charge"
        }
    },
    defaultDuration: {
        type: Number,
        required: [true, "Default duration is required"],
        min: 5, // minimum 5 minutes
    },
    currency: {
        type: String,
        required: [true, "Currency is required"],
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },
    isActive: {
        type: Boolean,
        default: true,
    }
}, { timestamps: true });

// Add indexes for better query performance
serviceTypeSchema.index({ category: 1 });
serviceTypeSchema.index({ name: 1 }, { unique: true });
serviceTypeSchema.index({ isActive: 1 });

// Add auto-increment plugin
serviceTypeSchema.plugin(AutoIncrement, { inc_field: 'serviceTypeId', start_seq: 1001 });
serviceTypeSchema.index({ serviceTypeId: 1 }, { unique: true });

const ServiceType = mongoose.model('ServiceType', serviceTypeSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = ServiceType.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await ServiceType.init();

        // Add default service types
        await ServiceType.insertMany([
            { name: 'Core Vaccination', category: 'vaccination', description: 'Core vaccines for pets', minCharge: 30, maxCharge: 80, defaultDuration: 20, currency: 'KES' },
            { name: 'Non-core Vaccination', category: 'vaccination', description: 'Optional vaccines based on risk', minCharge: 40, maxCharge: 100, defaultDuration: 20, currency: 'KES' },
            { name: 'Routine Checkup', category: 'checkup', description: 'Regular health examination', minCharge: 50, maxCharge: 120, defaultDuration: 30, currency: 'KES' },
            { name: 'Dental Cleaning', category: 'treatment', description: 'Dental hygiene procedure', minCharge: 100, maxCharge: 250, defaultDuration: 60, currency: 'KES' },
            { name: 'Spay/Neuter', category: 'surgery', description: 'Reproductive surgery', minCharge: 200, maxCharge: 500, defaultDuration: 120, currency: 'KES' },
            { name: 'Basic Grooming', category: 'grooming', description: 'Basic coat and nail care', minCharge: 40, maxCharge: 120, defaultDuration: 45, currency: 'KES' },
            { name: 'Full Grooming', category: 'grooming', description: 'Complete grooming service', minCharge: 80, maxCharge: 200, defaultDuration: 90, currency: 'KES' },
            { name: 'Prescription Medication', category: 'medication', description: 'Prescribed medicines', minCharge: 20, maxCharge: 300, defaultDuration: 15, currency: 'KES' },
            { name: 'Emergency Treatment', category: 'treatment', description: 'Urgent care services', minCharge: 150, maxCharge: 600, defaultDuration: 60, currency: 'KES' },
            { name: 'X-ray/Imaging', category: 'treatment', description: 'Diagnostic imaging', minCharge: 100, maxCharge: 350, defaultDuration: 45, currency: 'KES' },
            { name: 'Laboratory Tests', category: 'treatment', description: 'Blood work and other tests', minCharge: 80, maxCharge: 300, defaultDuration: 30, currency: 'KES' },
            { name: 'Orthopedic Surgery', category: 'surgery', description: 'Bone and joint surgery', minCharge: 300, maxCharge: 1200, defaultDuration: 180, currency: 'KES' },
            { name: 'Soft Tissue Surgery', category: 'surgery', description: 'Non-bone related surgery', minCharge: 250, maxCharge: 900, defaultDuration: 150, currency: 'KES' },
            { name: 'Parasite Treatment', category: 'treatment', description: 'Deworming and parasite control', minCharge: 30, maxCharge: 100, defaultDuration: 20, currency: 'KES' },
            { name: 'Behavioral Consultation', category: 'other', description: 'Pet behavior assessment', minCharge: 70, maxCharge: 180, defaultDuration: 60, currency: 'KES' },
        ]);

        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default ServiceType;