import { But<PERSON> } from "@/components/ui/button";
import { Plus, MoreHorizontal, RefreshCw, Search, Filter, Calendar as CalendarIcon } from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getPets } from "@/services/pets";
import { getSpecies } from "@/services/species";
import { getBreedsBySpecies } from "@/services/breeds";
import { format } from "date-fns";
import LoadingPage from "@/components/common/LoadingPage";
import { Input } from "@/components/ui/input";
import AddPetWithClientModal from "./components/AddPetWithClientModal";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

const Pets = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize state from URL parameters
  const [page, setPage] = useState(parseInt(searchParams.get('page') || '1'));
  const [limit, setLimit] = useState(parseInt(searchParams.get('limit') || '10'));
  const [searchName, setSearchName] = useState(searchParams.get('name') || "");
  const [ownerName, setOwnerName] = useState(searchParams.get('ownerName') || "");
  const [selectedClientId, setSelectedClientId] = useState(searchParams.get('clientId') || "");
  const [ageRange, setAgeRange] = useState<[number, number]>([0, 100]);
  const [ageUnit, setAgeUnit] = useState<"years" | "months">("years");
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(searchParams.get('speciesId') || null);
  const [selectedBreed, setSelectedBreed] = useState<string | null>(searchParams.get('breedId') || null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(searchParams.get('lifeStatus') || null);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
  const [showAddPetModal, setShowAddPetModal] = useState(false);
  interface Species {
    speciesId?: number;
    speciesName: string;
  }
  const [speciesList, setSpeciesList] = useState<Species[]>([]);
  interface Breed {
    breedId?: number;
    breedName: string;
  }
  const [breedsList, setBreedsList] = useState<Breed[]>([]);

  const { data: response, isLoading, error, refetch } = useQuery({
    queryKey: ["pets", page, limit, searchName, ownerName, selectedClientId, ageRange, ageUnit, selectedSpecies, selectedBreed, selectedStatus, dateRange],
    queryFn: () => getPets({
      page,
      offset: (page - 1),
      limit,
      name: searchName || undefined,
      ownerName: ownerName || undefined,
      clientId: selectedClientId || undefined,
      speciesId: selectedSpecies && selectedSpecies !== "all_species" ? selectedSpecies : undefined,
      breedId: selectedBreed && selectedBreed !== "all_breeds" ? selectedBreed : undefined,
      lifeStatus: selectedStatus && selectedStatus !== "all_status" ? selectedStatus : undefined,
      createdFrom: dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
      createdTo: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined
    }),
  });

  // Fetch species
  const { data: speciesResponse } = useQuery({
    queryKey: ['species'],
    queryFn: () => getSpecies()
  });

  // Fetch breeds based on selected species
  const { data: breedsData, refetch: refetchBreeds } = useQuery({
    queryKey: ['breeds', selectedSpecies],
    queryFn: () => getBreedsBySpecies({ speciesId: selectedSpecies || undefined }),
    enabled: !!selectedSpecies && selectedSpecies !== "all_species",
  });

  // Handle breeds data updates
  useEffect(() => {
    if (breedsData?.success && breedsData?.data?.data) {
      setBreedsList(breedsData.data.data);
    }
  }, [breedsData]);

  useEffect(() => {
    if (error || (!isLoading && !response?.success)) {
      toast({
        title: "Error",
        description: response?.message || "Failed to fetch pets",
        variant: "destructive",
      });
    }
  }, [error, response?.success, response?.message, toast, isLoading]);

  useEffect(() => {
    if (speciesResponse?.success && speciesResponse?.data?.data) {
      setSpeciesList(speciesResponse.data.data as Species[]);
    }
  }, [speciesResponse]);

  // Effect to handle URL parameter changes
  useEffect(() => {
    const urlSpeciesId = searchParams.get('speciesId');
    const urlBreedId = searchParams.get('breedId');
    const urlClientId = searchParams.get('clientId');

    if (urlSpeciesId && urlSpeciesId !== selectedSpecies) {
      setSelectedSpecies(urlSpeciesId);
      setIsAdvancedSearchOpen(true);
    }

    if (urlBreedId && urlBreedId !== selectedBreed) {
      setSelectedBreed(urlBreedId);
      setIsAdvancedSearchOpen(true);
    }

    if (urlClientId && urlClientId !== selectedClientId) {
      setSelectedClientId(urlClientId);
      setIsAdvancedSearchOpen(true);
    }
  }, [searchParams, selectedSpecies, selectedBreed, selectedClientId]);

  // Handle species change
  const handleSpeciesChange = (value: string) => {
    setSelectedSpecies(value);
    setSelectedBreed(null); // Reset breed when species changes
    if (value && value !== "all_species") {
      refetchBreeds();
    }
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  const pets = response?.data?.data || [];
  const pagination = response?.data?.pagination;
  const totalPages = pagination?.totalPages || 1;
  const currentPage = pagination?.page || 1;

  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleSearch = () => {
    setPage(1); // Reset to first page when searching
    refetch();
  };

  const resetFilters = () => {
    setSearchName("");
    setOwnerName("");
    setSelectedClientId("");
    setAgeRange([0, 100]);
    setAgeUnit("years");
    setSelectedSpecies(null);
    setSelectedBreed(null);
    setSelectedStatus(null);
    setDateRange({ from: undefined, to: undefined });
    setPage(1);
    refetch();
  };

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Pets</h1>
        <Button onClick={() => setShowAddPetModal(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Pet
        </Button>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-grow">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by pet name..."
                  value={searchName}
                  onChange={(e) => {
                    const value = e.target.value;
                    console.log(value);
                    if (value.length >= 4) {
                    setSearchName(value);
                    handleSearch();
                    }
                  }}
                  className="pl-8"
                />
              </div>

              <Button
                variant="outline"
                onClick={() => setIsAdvancedSearchOpen(!isAdvancedSearchOpen)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {isAdvancedSearchOpen ? "Hide Filters" : "Show Filters"}
              </Button>

              <Select
          value={String(limit)}
          onValueChange={handleLimitChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select page size"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="15">15 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
          </SelectContent>
        </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>

            {isAdvancedSearchOpen && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                <div className="space-y-2">
                  <Label>Owner Name</Label>
                  <Input
                    placeholder="Search by owner name..."
                    value={ownerName}
                    onChange={(e) => setOwnerName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Client ID</Label>
                  <Input
                    placeholder="Filter by client ID..."
                    value={selectedClientId}
                    onChange={(e) => setSelectedClientId(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Species</Label>
                  <Select
                    value={selectedSpecies || undefined}
                    onValueChange={handleSpeciesChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_species">All Species</SelectItem>
                      {speciesList.map((species) => (
                        <SelectItem key={species.speciesId} value={species.speciesId?.toString() || ""}>
                          {species.speciesName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Breed</Label>
                  <Select
                    value={selectedBreed || undefined}
                    onValueChange={setSelectedBreed}
                    disabled={!selectedSpecies || selectedSpecies === "all_species"}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={selectedSpecies && selectedSpecies !== "all_species" ? "Select breed" : "Select species first"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_breeds">All Breeds</SelectItem>
                      {breedsList.length > 0 ? (
                        breedsList.map((breed) => (
                          <SelectItem key={breed.breedId} value={breed.breedId?.toString() || ""}>
                            {breed.breedName}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="all_breeds" disabled>No breeds found</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select
                    value={selectedStatus || undefined}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_status">All Status</SelectItem>
                      <SelectItem value="alive">Alive</SelectItem>
                      <SelectItem value="deceased">Deceased</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Age Range</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={ageRange[0]}
                      onChange={(e) => setAgeRange([parseInt(e.target.value), ageRange[1]])}
                      className="w-20"
                    />
                    <span>to</span>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={ageRange[1]}
                      onChange={(e) => setAgeRange([ageRange[0], parseInt(e.target.value)])}
                      className="w-20"
                    />
                    <Select
                      value={ageUnit}
                      onValueChange={(value) => setAgeUnit(value as "years" | "months")}
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="years">Years</SelectItem>
                        <SelectItem value="months">Months</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Registration Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !dateRange.from && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "LLL dd, y")} -{" "}
                              {format(dateRange.to, "LLL dd, y")}
                            </>
                          ) : (
                            format(dateRange.from, "LLL dd, y")
                          )
                        ) : (
                          "Select date range"
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={dateRange.from}
                        selected={{
                          from: dateRange.from,
                          to: dateRange.to,
                        }}
                        onSelect={(range) =>
                          setDateRange({
                            from: range?.from,
                            to: range?.to
                          })
                        }
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            )}

            {isAdvancedSearchOpen && (
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={resetFilters}>Reset Filters</Button>
                <Button onClick={handleSearch}>Apply Filters</Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* <div className="flex justify-end mb-4">
        <Select
          value={String(limit)}
          onValueChange={handleLimitChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select page size"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="15">15 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
          </SelectContent>
        </Select>
      </div> */}

      {pets.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">No pets found</div>
      ) : (
        <>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Owner</TableHead>
                  {/* <TableHead>Species</TableHead> */}
                  {/* <TableHead>Breed</TableHead> */}
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right pr-6">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pets.map((pet) => (
                  <TableRow key={pet.petId}>
                    <TableCell className="font-medium">{(pet as any).petName || pet.name}</TableCell>
                    <TableCell>{(pet as any).owner ? `${(pet as any).owner.firstName || ''} ${(pet as any).owner.lastName || ''}` : 'Unknown'}</TableCell>
                    {/* <TableCell>{pet.species.name}</TableCell> */}
                    {/* <TableCell>{pet.species.name}</TableCell> */}
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          pet.lifeStatus === "alive"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {pet.lifeStatus}
                      </span>
                    </TableCell>
                    <TableCell>
                      {pet.createdAt ? format(new Date(pet.createdAt), "MMM dd, yyyy") : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[160px]">
                          <DropdownMenuItem
                            onClick={() => navigate(`/pets/${pet.petId}/edit`, { state: { pet } })}
                          >
                            Edit Pet
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, pagination?.totalCount || 0)} of {pagination?.totalCount || 0} entries
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </>
      )}

      {/* Add Pet Modal */}
      <AddPetWithClientModal
        open={showAddPetModal}
        onOpenChange={setShowAddPetModal}
      />
    </div>
  );
};

export default Pets;
