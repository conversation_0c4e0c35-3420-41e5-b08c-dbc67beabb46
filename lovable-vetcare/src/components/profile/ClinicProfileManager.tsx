import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { api } from "@/services/api";
import { 
  Building2, 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Edit, 
  Save, 
  X, 
  Check,
  Users,
  Globe
} from "lucide-react";
import { Clinic, Staff } from "@/store/types";

interface ClinicProfileManagerProps {
  clinic: Clinic;
  onUpdate: (updatedClinic: Clinic) => void;
  isOwner?: boolean;
}

interface EditableField {
  key: string;
  value: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'url';
}

export const ClinicProfileManager = ({ clinic, onUpdate, isOwner = false }: ClinicProfileManagerProps) => {
  const { toast } = useToast();
  const [editingField, setEditingField] = useState<EditableField | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const [updating, setUpdating] = useState(false);
  const [staffMembers, setStaffMembers] = useState<Staff[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);

  // Fetch staff members for this clinic
  useEffect(() => {
    const fetchStaff = async () => {
      if (!clinic.clinicId) return;
      
      setLoadingStaff(true);
      try {
        const response = await api.get(`/staff?clinicId=${clinic.clinicId}`);
        if (response.data?.success) {
          setStaffMembers(response.data.data.data || []);
        }
      } catch (error) {
        console.error("Error fetching staff:", error);
      } finally {
        setLoadingStaff(false);
      }
    };

    fetchStaff();
  }, [clinic.clinicId]);

  // Start editing a field
  const startEditing = (key: string, value: string, type: 'text' | 'email' | 'tel' | 'textarea' | 'url' = 'text') => {
    setEditingField({ key, value, type });
    setEditValue(value || '');
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingField(null);
    setEditValue("");
  };

  // Save the edited field
  const saveEdit = async () => {
    if (!editingField) return;

    setUpdating(true);
    try {
      const { key } = editingField;
      const updateData = { [key]: editValue };

      const response = await api.put(`/clinics/${clinic.clinicId}/profile`, updateData);

      if (response.data?.success) {
        const updatedClinic = { ...clinic, [key]: editValue };
        onUpdate(updatedClinic);
        
        toast({
          title: "Success",
          description: "Clinic information updated successfully",
        });
      } else {
        throw new Error(response.data?.message || "Failed to update");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update clinic information",
      });
    } finally {
      setUpdating(false);
      setEditingField(null);
    }
  };

  // Handle key press in edit field
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && editingField?.type !== 'textarea') {
      saveEdit();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  // Editable field component
  const EditableField = ({
    label,
    value,
    fieldKey,
    icon: Icon,
    type = 'text'
  }: {
    label: string,
    value: string | undefined,
    fieldKey: string,
    icon: React.ElementType,
    type?: 'text' | 'email' | 'tel' | 'textarea' | 'url'
  }) => {
    const isEditing = editingField?.key === fieldKey;
    const displayValue = value || "Not provided";

    return (
      <div>
        <h3 className="text-sm font-medium text-muted-foreground flex items-center justify-between">
          {label}
          {isOwner && !isEditing && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => startEditing(fieldKey, displayValue, type)}
            >
              <Edit className="h-3 w-3" />
            </Button>
          )}
        </h3>

        {isEditing ? (
          <div className="flex items-start mt-1">
            {type === 'textarea' ? (
              <Textarea
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyPress}
                autoFocus
                className="min-h-[80px]"
                disabled={updating}
              />
            ) : (
              <Input
                type={type}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyPress}
                autoFocus
                className="h-8"
                disabled={updating}
              />
            )}
            <div className="flex ml-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-green-500"
                onClick={saveEdit}
                disabled={updating}
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-red-500"
                onClick={cancelEditing}
                disabled={updating}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center mt-1">
            <Icon className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className={type === 'textarea' ? 'whitespace-pre-wrap' : ''}>
              {displayValue}
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building2 className="mr-2 h-5 w-5" />
            Clinic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <EditableField
              label="Clinic Name"
              value={clinic.clinicName}
              fieldKey="clinicName"
              icon={Building2}
            />
            
            <EditableField
              label="Phone Number"
              value={clinic.phoneNumber}
              fieldKey="phoneNumber"
              icon={Phone}
              type="tel"
            />
            
            <EditableField
              label="Email"
              value={clinic.email}
              fieldKey="email"
              icon={Mail}
              type="email"
            />
            
            <EditableField
              label="Website"
              value={clinic.website}
              fieldKey="website"
              icon={Globe}
              type="url"
            />
          </div>
          
          <EditableField
            label="Address"
            value={clinic.address}
            fieldKey="address"
            icon={MapPin}
          />
          
          <EditableField
            label="Description"
            value={clinic.description}
            fieldKey="description"
            icon={Building2}
            type="textarea"
          />
        </CardContent>
      </Card>

      {/* Operating Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            Operating Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {clinic.operatingHours && Object.entries(clinic.operatingHours).map(([day, hours]) => (
              <div key={day} className="flex items-center justify-between p-3 border rounded-lg">
                <span className="font-medium capitalize">{day}</span>
                <span className="text-sm text-muted-foreground">
                  {hours.closed ? 'Closed' : `${hours.open} - ${hours.close}`}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Staff Members */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Staff Members ({staffMembers.length})
            </div>
            {isOwner && (
              <Button size="sm" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Manage Staff
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingStaff ? (
            <div className="text-center py-4">Loading staff...</div>
          ) : staffMembers.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {staffMembers.map((staff) => (
                <div key={staff.staffId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">
                      {staff.firstName} {staff.lastName}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {staff.jobTitle}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {staff.isClinicOwner && (
                      <Badge variant="default">Owner</Badge>
                    )}
                    {staff.role && (
                      <Badge variant="outline">{staff.role.roleName}</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              No staff members found
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
