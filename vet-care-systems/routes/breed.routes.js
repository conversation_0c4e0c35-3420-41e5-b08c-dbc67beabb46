import {Router} from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {registerBreed, getAllBreeds, getBreedById, updateBreed, deleteBreed} from '../controllers/breed.controller.js';

const breedRouter = Router();

// Protected routes
breedRouter.use(verifyToken);

breedRouter.post('/', registerBreed);
breedRouter.get('/', getAllBreeds);
breedRouter.get('/:breedId', getBreedById);
breedRouter.put('/:breedId', updateBreed);
breedRouter.delete('/:breedId', deleteBreed);

export default breedRouter;