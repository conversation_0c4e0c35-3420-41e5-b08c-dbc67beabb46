import { Pet, PaginatedResponse } from '@/store/types';
import {
  getPaginatedData,
  PaginationParams,
  createEntity,
  getEntityById,
  updateEntity,
  deleteEntity
} from './apiUtils';
import { useStore } from '@/store';

export interface PetSearchParams extends PaginationParams {
  petProfileId?: string;
  name?: string;
  breed?: string;
  clientId?: string;
}

export interface CreatePetDto {
  petName: string; // Backend expects 'petName'
  speciesId: string;
  breedId: string;
  color: string;
  lifeStatus: 'alive' | 'deceased';
  gender: 'male' | 'female';
  microchipId?: string;
  weight: number;
  dateOfBirth: Date;
  clientId: string;
  ownerId?: string; // Kept for backward compatibility
  petStatus?: number;
}

// CRUD operations for pets
// Helper function to get clinic context
const getClinicId = () => {
  const state = useStore.getState();
  return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

export const getPets = async (params: PetSearchParams = {}): Promise<PaginatedResponse<Pet>> => {
  // Always include clinicId if available
  const clinicId = getClinicId();
  if (clinicId && !params.clinicId) {
    params.clinicId = clinicId;
  }

  return getPaginatedData<Pet>('pets', params);
};

export const getPetById = async (id: string) => {
  return getEntityById<Pet>('pets', id);
};

export const createPet = async (petData: CreatePetDto) => {
  return createEntity<Pet, CreatePetDto>('pets', petData);
};

export const updatePet = async (petId: string, petData: Partial<CreatePetDto>) => {
  return updateEntity<Pet, Partial<CreatePetDto>>('pets', petId, petData);
};

export const deletePet = async (petId: string) => {
  return deleteEntity('pets', petId);
};

/**
 * Get all pets for a specific client
 * @param clientId The ID of the pet owner
 * @param params Additional search parameters
 */
export const getPetsByOwner = async (clientId: string, params: PetSearchParams = {}) => {
  // Use the existing getPets function with the clientId parameter
  return getPets({ ...params, clientId });
};

/**
 * Legacy function - use getPetsByOwner instead
 * @deprecated Use getPetsByOwner with clientId instead
 */
export const getPetsByClient = async (clientId: string, params: PetSearchParams = {}) => {
  return getPetsByOwner(clientId, params);
};
