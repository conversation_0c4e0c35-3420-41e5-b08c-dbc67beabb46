
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        sidebar: "hsl(var(--sidebar))",
        primary: {
          DEFAULT: "#00A3E0", // Medical blue
          foreground: "#FFFFFF",
          50: "#E6F7FE",
          100: "#CCF0FD",
          200: "#99E0FB",
          300: "#66D1F9",
          400: "#33C1F7",
          500: "#00A3E0",
          600: "#0082B3",
          700: "#006286",
          800: "#00415A",
          900: "#00212D",
        },
        secondary: {
          DEFAULT: "#20C997", // Healing green
          foreground: "#FFFFFF",
          50: "#E9FBF5",
          100: "#D3F8EB",
          200: "#A7F1D7",
          300: "#7BEAC3",
          400: "#4FE3AF",
          500: "#20C997",
          600: "#1AA179",
          700: "#13795B",
          800: "#0D503D",
          900: "#06281E",
        },
        accent: {
          DEFAULT: "#0096CC", // Soft medical blue
          foreground: "#FFFFFF",
          50: "#E6F5FA",
          100: "#CCEBF5",
          200: "#99D7EB",
          300: "#66C3E0",
          400: "#33AFD6",
          500: "#0096CC",
          600: "#0078A3",
          700: "#005A7A",
          800: "#003C52",
          900: "#001E29",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        fadeIn: {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        slideIn: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(0)" },
        },
        heartbeat: {
          "0%": { transform: "scale(1)" },
          "14%": { transform: "scale(1.3)" },
          "28%": { transform: "scale(1)" },
          "42%": { transform: "scale(1.3)" },
          "70%": { transform: "scale(1)" },
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        fadeIn: "fadeIn 0.5s ease-out",
        slideIn: "slideIn 0.3s ease-out",
        heartbeat: "heartbeat 1.5s infinite ease-in-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
