import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * HealthRecord Model
 *
 * This model represents a medical record for a pet.
 * It is designed to support cross-clinic functionality while maintaining
 * proper attribution and access controls.
 */
const healthRecordSchema = new mongoose.Schema({
    // Auto-increment ID
    healthRecordId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Core identifiers
    petId: {
        type: Number,
        required: true,
        index: true
    },
    serviceId: {
        type: Number,
        required: true,
        index: true
    },

    // Provider information
    performedBy: {
        type: Number,
        required: true,
        index: true
    },
    clinicId: {
        type: Number,
        required: true,
        index: true
    },

    // Related records
    appointmentId: {
        type: Number,
        index: true
    },
    petClinicRelationshipId: {
        type: Number,
        required: true,
        index: true
    },

    // Record metadata
    recordType: {
        type: String,
        enum: ['consultation', 'vaccination', 'surgery', 'laboratory', 'imaging', 'prescription', 'other'],
        required: true,
        index: true
    },
    date: {
        type: Date,
        required: true,
        default: Date.now
    },

    // Medical information
    description: {
        type: String,
        required: [true, "Description is required"],
        trim: true
    },
    diagnosis: {
        type: String,
        trim: true
    },
    treatment: {
        type: String,
        trim: true
    },

    // Medications
    medications: [{
        name: {
            type: String,
            required: true
        },
        dosage: {
            type: String,
            required: true
        },
        frequency: {
            type: String,
            required: true
        },
        duration: String,
        notes: String,
        prescribedBy: {
            type: Number
        }
    }],

    // Lab results
    labResults: [{
        testName: String,
        result: String,
        normalRange: String,
        interpretation: String,
        performedBy: {
            type: Number
        },
        attachmentUrl: String
    }],

    // Vital signs
    vitalSigns: {
        temperature: Number,
        heartRate: Number,
        respiratoryRate: Number,
        weight: Number,
        bloodPressure: String
    },

    // Attachments (images, documents, etc.)
    attachments: [{
        fileName: String,
        fileUrl: String,
        fileType: String,
        description: String,
        uploadDate: {
            type: Date,
            default: Date.now
        },
        uploadedBy: {
            type: Number
        }
    }],

    // Follow-up information
    followUpDate: Date,
    followUpInstructions: String,
    followUpStatus: {
        type: String,
        enum: ['scheduled', 'completed', 'missed', 'cancelled', 'none'],
        default: 'none'
    },

    // Additional notes
    notes: {
        type: String,
        trim: true
    },

    // Billing information
    billingDetails: {
        baseAmount: {
            type: Number,
            required: true,
            min: 0
        },
        afterHoursCharge: {
            type: Number,
            default: 0,
            min: 0
        },
        amount: {
            type: Number,
            required: true,
            min: 0
        },
        currency: {
            type: String,
            required: true,
            enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
            default: 'KES'
        },
        paymentStatus: {
            type: String,
            enum: ['pending', 'paid', 'partially_paid', 'waived'],
            default: 'pending'
        },
        isAfterHours: {
            type: Boolean,
            default: false
        },
        visitTime: {
            type: Date,
            required: true
        },
        invoiceId: {
            type: Number
        }
    },

    // Access control
    accessControl: {
        isPublic: {
            type: Boolean,
            default: true
        },
        restrictedTo: [{
            type: Number
        }],
        accessReason: {
            type: String,
            trim: true
        }
    }
}, { timestamps: true });

// Add indexes for better query performance
healthRecordSchema.index({ petId: 1, date: -1 });
healthRecordSchema.index({ clinicId: 1, date: -1 });
healthRecordSchema.index({ petId: 1, recordType: 1 });
healthRecordSchema.index({ performedBy: 1 });
healthRecordSchema.index({ serviceId: 1 });
healthRecordSchema.index({ appointmentId: 1 });
healthRecordSchema.index({ petClinicRelationshipId: 1 });
healthRecordSchema.index({ 'billingDetails.paymentStatus': 1 });
healthRecordSchema.index({ 'accessControl.isPublic': 1 });

// Add auto-increment plugin
healthRecordSchema.plugin(AutoIncrement, { inc_field: 'healthRecordId', start_seq: 1001 });
healthRecordSchema.index({ healthRecordId: 1 }, { unique: true });

const HealthRecord = mongoose.model('HealthRecord', healthRecordSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = HealthRecord.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await HealthRecord.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default HealthRecord;
