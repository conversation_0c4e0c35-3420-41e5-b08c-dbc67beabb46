# Development Environment Configuration
# Copy this file to .env.development.local and fill in the values

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_URI=mongodb://localhost:27017/vet-care-dev

# Authentication Configuration
JWT_SECRET=your_development_jwt_secret_key
JWT_EXPIRES_IN=1h

# Security Configuration (Optional)
# ARCJET_KEY=your_arcjet_key
# ARCJET_ENV=development

# Queue Configuration (Optional)
# QSTASH_URL=your_qstash_url
# QSTASH_TOKEN=your_qstash_token
